# Enhanced Driver Rating API - Complete Guide

This guide covers all enhanced driver rating endpoints with pagination, advanced filtering, total points tracking, and robust vehicle switching support.

## All Available Endpoints

### 1. Driver Rating Dashboard
```
GET /api/drivers/{driver_id}/rating/
```
**Enhanced Features:**
- Total points tracking (period + all-time)
- Complete vehicle history
- Vehicle switching tracking
- Rating calculation methodology info

**Response includes:**
```json
{
  "driver_id": 1,
  "driver_name": "<PERSON>",
  "driver_code": "DRV-0001",
  "current_rating": 4.8,
  "total_points_period": 145,
  "total_points_all_time": 2847,
  "vehicle_history": [
    {
      "id": 1,
      "registration": "KCA 123X",
      "make": "Toyota",
      "has_rating": true,
      "is_active": true,
      "total_points": 145.0
    }
  ],
  "rating_calculation_info": {
    "calculation_method": "payment_based",
    "points_on_time": 5.0,
    "points_late": 2.5
  }
}
```

---

### 2. Payment History (Enhanced with Pagination & Filtering)
```
GET /api/drivers/{driver_id}/payment_history/
```

**Query Parameters:**
- `page` (default: 1) - Page number
- `page_size` (default: 20, max: 100) - Records per page
- `start_date` (YYYY-MM-DD) - Filter from date
- `end_date` (YYYY-MM-DD) - Filter to date
- `days` (integer) - Days to look back
- `vehicle_id` - Filter by specific vehicle
- `vehicle_registration` - Filter by vehicle registration
- `status` (on_time/late/all) - Filter by payment status
- `min_amount` - Minimum payment amount
- `max_amount` - Maximum payment amount

**Enhanced Response:**
```json
{
  "driver_id": 1,
  "total_points_all_time": 2847,
  "pagination": {
    "current_page": 1,
    "page_size": 20,
    "total_pages": 5,
    "total_records": 95,
    "has_next": true,
    "next_page": 2
  },
  "filters_applied": {
    "start_date": "2024-01-01",
    "vehicle_id": "1",
    "status": "on_time"
  },
  "payment_history": [
    {
      "id": 123,
      "date": "2024-07-15",
      "amount": 1500.0,
      "vehicle": {
        "id": 1,
        "registration": "KCA 123X",
        "make": "Toyota",
        "payment_date": "2024-07-15"
      },
      "status": "On Time",
      "points": 5.0
    }
  ],
  "summary": {
    "total_payments": 95,
    "on_time_payments": 88,
    "late_payments": 7,
    "total_amount": 142500.0,
    "total_points": 462.5,
    "success_rate": 93,
    "average_amount": 1500.0,
    "vehicles_used": ["KCA 123X", "KCB 456Y"]
  }
}
```

---

### 3. Partner Performance Report (Enhanced with Pagination)
```
GET /api/drivers/performance_report/
```

**Query Parameters:**
- `page` (default: 1) - Page number
- `page_size` (default: 20, max: 100) - Records per page
- `age_group` - Filter by age group
- `vehicle_type` - Filter by vehicle type
- `vehicle_registration` - Filter by vehicle registration
- `status` (all/active/inactive) - Filter by status
- `min_rating` - Minimum rating filter
- `max_rating` - Maximum rating filter
- `sort_by` (rating/name/success_rate/total_points) - Sort field
- `sort_order` (asc/desc) - Sort direction

**Enhanced Response:**
```json
{
  "partner_id": 1,
  "partner_name": "ABC Transport Ltd",
  "summary": {
    "total_drivers": 25,
    "active_drivers": 22,
    "inactive_drivers": 3,
    "average_rating": 4.2,
    "total_points_all_drivers": 45680
  },
  "pagination": {
    "current_page": 1,
    "page_size": 20,
    "total_pages": 2,
    "total_records": 25
  },
  "filters_applied": {
    "status": "active",
    "sort_by": "rating",
    "sort_order": "desc"
  },
  "drivers": [
    {
      "driver_name": "John Doe",
      "driver_code": "DRV-0001",
      "current_rating": 4.8,
      "total_points_all_time": 2847,
      "success_rate": 93,
      "vehicle_count": 2
    }
  ]
}
```

---

### 4. Rating Trend
```
GET /api/drivers/{driver_id}/rating_trend/
```
**Parameters:**
- `period` (weekly/monthly) - Trend period
- `weeks` (default: 8) - Number of weeks

---

### 5. Partner Payment History Report
```
GET /api/drivers/payment_history_report/
```
**Parameters:**
- `days` (default: 30) - Days to look back
- `driver_id` - Filter by specific driver
- `limit_per_driver` (default: 10, max: 50) - Records per driver

---

### 6. Calculate Rating (NEW)
```
POST /api/drivers/{driver_id}/calculate_rating/
```
**Purpose:** Manually trigger rating calculation
**Access:** Partners (their drivers), Admins (all drivers)

**Request Body:**
```json
{
  "recalculate_from_date": "2024-01-01",
  "vehicle_id": 1  // Optional: specific vehicle only
}
```

**Response:**
```json
{
  "driver_id": 1,
  "driver_name": "John Doe",
  "calculation_triggered": true,
  "calculation_timestamp": "2024-07-28T10:30:00Z",
  "overall_rating": 4.8,
  "total_points_all_time": 2847,
  "vehicles_processed": 2,
  "vehicle_results": [
    {
      "vehicle_id": 1,
      "vehicle_registration": "KCA 123X",
      "rating_updated": true,
      "current_rating": 4.8,
      "total_points": 145.0,
      "payment_days": 30
    }
  ]
}
```

---

## Advanced Filtering Examples

### Payment History with Multiple Filters
```bash
curl -H "Authorization: Bearer TOKEN" \
  "http://localhost:8000/api/drivers/1/payment_history/?page=1&page_size=50&start_date=2024-01-01&end_date=2024-07-28&vehicle_registration=KCA&status=on_time&min_amount=1000"
```

### Performance Report with Sorting
```bash
curl -H "Authorization: Bearer TOKEN" \
  "http://localhost:8000/api/drivers/performance_report/?page=1&status=active&sort_by=total_points&sort_order=desc&min_rating=4.0"
```

### Trigger Rating Calculation
```bash
curl -X POST -H "Authorization: Bearer TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"recalculate_from_date": "2024-01-01"}' \
  http://localhost:8000/api/drivers/1/calculate_rating/
```

---

## Vehicle Switching Handling

### How Vehicle Switches Are Tracked

1. **Separate Ratings per Vehicle**: Each driver-vehicle combination has its own rating record
2. **Historical Preservation**: All payment records maintain original vehicle associations
3. **Vehicle History API**: Complete history of all vehicles a driver has used
4. **Continuous Calculations**: Ratings continue seamlessly across vehicle switches

### Vehicle History Response
```json
{
  "vehicle_history": [
    {
      "id": 1,
      "registration": "KCA 123X",
      "make": "Toyota",
      "has_rating": true,
      "is_active": false,  // Previous vehicle
      "rating_start_date": "2024-01-01",
      "total_points": 850.0,
      "payment_days": 170
    },
    {
      "id": 2,
      "registration": "KCB 456Y",
      "make": "Nissan",
      "has_rating": true,
      "is_active": true,   // Current vehicle
      "rating_start_date": "2024-06-01",
      "total_points": 145.0,
      "payment_days": 30
    }
  ]
}
```

---

## Total Points Tracking

### Two Types of Points Tracking

1. **Period Points** (`total_points_period`): Points for current analysis period (e.g., last 30 days)
2. **All-Time Points** (`total_points_all_time`): Cumulative points across all vehicles and time

### Points Calculation
- **On-time Payment**: 5.0 points
- **Late Payment**: 2.5 points
- **Missed Payment**: 0.0 points

### Rating Formula
```
Rating = Total Points ÷ Total Expected Payment Days
```

---

## Pagination Best Practices

1. **Default Page Size**: 20 records (adjustable up to 100)
2. **Navigation**: Use `has_next`/`has_previous` for UI controls
3. **Total Records**: Available in `pagination.total_records`
4. **Performance**: Large datasets are automatically paginated

---

## Error Handling

### Common HTTP Status Codes
- **200**: Success
- **400**: Bad Request (invalid parameters)
- **401**: Authentication required
- **403**: Permission denied
- **404**: Resource not found
- **500**: Server error during calculation

### Example Error Response
```json
{
  "detail": "Invalid date format. Use YYYY-MM-DD.",
  "error_code": "INVALID_DATE_FORMAT"
}
```

---

## Performance Optimization Tips

1. **Use Pagination**: Don't fetch all records at once
2. **Apply Filters**: Reduce dataset size with appropriate filters
3. **Cache Results**: Cache frequently accessed data
4. **Batch Operations**: Use bulk calculation endpoints when available
5. **Monitor Performance**: Track API response times

This enhanced API provides comprehensive driver rating management with robust filtering, pagination, and vehicle switching support.
