import { createContext, useState, useContext, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import axios from "axios";
import { BASE_URL } from "../../services/config";

export const AuthContext = createContext();

const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [token, setToken] = useState(localStorage.getItem("token") || "");
  const navigate = useNavigate();

  // Load user data from localStorage on initialization
  useEffect(() => {
    const storedUser = localStorage.getItem("user");
    const storedToken = localStorage.getItem("token");

    if (storedUser) {
      try {
        setUser(JSON.parse(storedUser));
      } catch (error) {
        console.error("Error parsing stored user data:", error);
        localStorage.removeItem("user");
      }
    } else if (storedToken) {
      // If we have a token but no user data, fetch it
      fetchUserData(storedToken);
    }
  }, []);

  const fetchUserData = async (accessToken) => {
    try {
      const userResponse = await axios.get(`${BASE_URL}api/users/me/`, {
        headers: {
          Authorization: `Bearer ${accessToken}`
        }
      });

      if (userResponse.status === 200) {
        const userData = userResponse.data;
        setUser(userData);
        localStorage.setItem("user", JSON.stringify(userData));
      }
    } catch (error) {
      console.error("Error fetching user data:", error);
      // If fetching fails, clear the token as it might be invalid
      localStorage.removeItem("token");
      localStorage.removeItem("refresh_token");
      setToken("");
    }
  };

    const loginAction = async (data, setErrorMessage, returnUrl = null) => {

        try {
            const response = await axios.post(`${BASE_URL}api/token/`, data);
            if (response.status === 200 && response.data.access && response.data.refresh) {

        // Store tokens
        setToken(response.data.access);
        localStorage.setItem("token", response.data.access);
        localStorage.setItem("refresh_token", response.data.refresh);

        // Fetch and store user profile data
        try {
          const userResponse = await axios.get(`${BASE_URL}api/users/me/`, {
            headers: {
              Authorization: `Bearer ${response.data.access}`
            }
          });

          if (userResponse.status === 200) {
            const userData = userResponse.data;
            setUser(userData);
            localStorage.setItem("user", JSON.stringify(userData));
          }
        } catch (userError) {
          console.error("Error fetching user data:", userError);
          // Continue with login even if user fetch fails
        }

        // If there's a return URL, navigate there instead of the default dashboard
        if (returnUrl) {
          navigate(returnUrl);
        } else if (data.role === "partner") {
          navigate("/partnerdashboard");
        } else if (data.role === "driver") {
          navigate("/driverdashboard");
        } else {
          navigate("/");
        }
      }
    } catch (err) {
      if (err.response && err.response.status === 400) {
        if (err.response.data && err.response.data.non_field_errors) {
          setErrorMessage(err.response.data.non_field_errors.join(" "));
        } else {
          setErrorMessage("Invalid login credentials or unexpected error.");
        }
      } else {
        console.error("Login error:", err);
        setErrorMessage(
          "An error occurred during login. Please try again later."
        );
      }
    }
  };

  const logOut = () => {
    setUser(null);
    setToken("");
    localStorage.removeItem("token");
    localStorage.removeItem("refresh_token");
    localStorage.removeItem("user");
    navigate("/");
  };

  // Determine if user is authenticated
  const isAuthenticated = !!token && !!user;

  return (
    <AuthContext.Provider value={{ token, user, loginAction, logOut, isAuthenticated }}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthProvider;

export const useAuth = () => {
  return useContext(AuthContext);
};
