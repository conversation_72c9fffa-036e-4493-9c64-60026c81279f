"""
Email templates for the Kadere Connect application.
This file contains HTML templates for various email notifications.
"""

from django.conf import settings


def get_base_template():
    """
    Returns the base HTML template with common styling.
    """
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <style type="text/css">
            /* Reset styles */
            body, html {{
                margin: 0;
                padding: 0;
                width: 100% !important;
                height: 100% !important;
            }}
            
            /* Base styles */
            body {{
                -webkit-text-size-adjust: 100%;
                -ms-text-size-adjust: 100%;
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
                line-height: 1.6;
                color: #333333;
                background-color: #ffffff;
            }}
            
            /* Container */
            .container {{
                width: 100%;
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
                box-sizing: border-box;
            }}
            
            /* Header */
            .header {{
                background-color: #f8f9fa;
                padding: 20px;
                text-align: center;
                border-radius: 8px;
                margin-bottom: 20px;
            }}
            
            /* Content Box */
            .content-box {{
                background-color: #f8f9fa;
                padding: 20px;
                margin: 20px 0;
                border-radius: 8px;
                border-left: 4px solid #007bff;
            }}
            
            /* Button */
            .button {{
                background: #007bff;
                border: 1px solid #007bff;
                border-radius: 6px;
                color: #ffffff !important;
                display: inline-block;
                font-size: 16px;
                font-weight: bold;
                margin: 0;
                padding: 12px 25px;
                text-decoration: none;
                text-align: center;
                min-width: 120px;
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            }}
            
            /* Note */
            .note {{
                font-style: italic;
                color: #666666;
                text-align: center;
                margin: 20px 0;
                font-size: 14px;
            }}
            
            /* Footer */
            .footer {{
                text-align: center;
                color: #666666;
                margin-top: 30px;
                padding-top: 20px;
                border-top: 1px solid #eeeeee;
            }}
            
            /* Outlook-specific fixes */
            body {{
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
            }}
            
            * {{
                -ms-text-size-adjust: 100%;
            }}
            
            a {{
                text-decoration: none;
            }}
            
            img {{
                -ms-interpolation-mode: bicubic;
            }}
            
            table, td {{
                mso-table-lspace: 0pt;
                mso-table-rspace: 0pt;
            }}
            
            /* Mobile responsiveness */
            @media only screen and (max-width: 480px) {{
                .container {{
                    padding: 10px !important;
                }}
                
                .button {{
                    width: 100% !important;
                    min-width: 100% !important;
                }}
            }}
        </style>
    </head>
    <body>
        <div class="container">
            {content}
            <div class="footer">
                <p style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;">
                    Best regards,<br>
                    <strong>Kadere Connect Team</strong>
                </p>
            </div>
        </div>
    </body>
    </html>
    """


def get_verification_email_template(user):
    """
    Returns the HTML template for email verification.
    """
    role = 'partner' if user.role == 'partner' else 'driver'
    verification_link = f"{settings.FRONTEND_URL}/verify-email/{user.verification_code}/?role={role}"
    
    content = f"""
    <div class="header">
        <h2>📧 Email Verification</h2>
    </div>
    
    <p>Hello {user.first_name},</p>
    <p>Thank you for registering with Kadere Connect. Please verify your email address to complete your registration.</p>
    
    <div style="text-align: center; margin: 30px 0;">
        <a href="{verification_link}" class="button">
            Verify Email
        </a>
    </div>
    
    <p class="note">
        If the button above doesn't work, you can copy and paste the following link into your browser:<br>
        {verification_link}
    </p>
    """
    
    return get_base_template().format(content=content)


def get_driver_added_email_template(driver, partner_name, password):
    """
    Returns the HTML template for driver added notification.
    """
    verification_link = f"{settings.FRONTEND_URL}/verify-email/{driver.user.verification_code}/?role=driver"
    
    content = f"""
    <div class="header">
        <h2>🚗 Welcome to Kadere Connect!</h2>
    </div>
    
    <p>Hello {driver.first_name},</p>
    <p>You have been added as a driver by <strong>{partner_name}</strong>.</p>
    
    <div class="content-box">
        <h3 style="margin-top: 0;">Your Login Details:</h3>
        <p><strong>Email:</strong> {driver.email}</p>
        <p><strong>Temporary Password:</strong> {password}</p>
    </div>
    
    <p>Please confirm your email and change your password after logging in.</p>
    
    <div style="text-align: center; margin: 30px 0;">
        <a href="{verification_link}" class="button">
            Verify Email
        </a>
    </div>
    
    <p class="note">
        If the button above doesn't work, you can copy and paste the following link into your browser:<br>
        {verification_link}
    </p>
    """
    
    return get_base_template().format(content=content)


def get_driver_hired_email_template(driver, partner, job):
    """
    Returns the HTML template for driver hired notification.
    """
    content = f"""
    <div class="header">
        <h2>🎉 Congratulations! You've Been Hired</h2>
    </div>
    
    <p>Hello {driver.user.first_name},</p>
    <p>We're pleased to inform you that <strong>{partner.user.first_name} {partner.user.last_name}</strong> has hired you for the following job:</p>
    
    <div class="content-box">
        <h3 style="margin-top: 0;">Job Details:</h3>
        <p><strong>🚘 Vehicle:</strong> {job.vehicle_make.name} {job.vehicle_model.name}</p>
        <p><strong>📍 Work Area:</strong> {job.preferred_work_area.name}</p>
        <p><strong>📅 Work Days:</strong> {job.work_days}</p>
    </div>
    
    <p>Please contact your employer for further instructions and next steps.</p>
    
    <div style="text-align: center; margin: 30px 0;">
        <a href="{settings.FRONTEND_URL}/dashboard" class="button">
            Go to Dashboard
        </a>
    </div>
    """
    
    return get_base_template().format(content=content)


def get_driver_rejected_email_template(driver, partner, job):
    """
    Returns the HTML template for driver rejection notification.
    """
    content = f"""
    <div class="header">
        <h2>Update on Your Job Application</h2>
    </div>
    
    <p>Hello {driver.user.first_name},</p>
    <p>We regret to inform you that your application for the job with <strong>{partner.user.first_name} {partner.user.last_name}</strong> has been rejected.</p>
    
    <div class="content-box">
        <h3 style="margin-top: 0;">Job Details:</h3>
        <p><strong>🚘 Vehicle:</strong> {job.vehicle_make.name} {job.vehicle_model.name}</p>
        <p><strong>📍 Work Area:</strong> {job.preferred_work_area.name}</p>
    </div>
    
    <p>Don't be discouraged! There are many other opportunities available on our platform.</p>
    
    <div style="text-align: center; margin: 30px 0;">
        <a href="{settings.FRONTEND_URL}/jobs" class="button">
            Browse More Jobs
        </a>
    </div>
    
    <p class="note">
        Keep applying! The right opportunity is waiting for you.
    </p>
    """
    
    return get_base_template().format(content=content)


def get_password_reset_email_template(user, reset_link):
    """
    Returns the HTML template for password reset notification.
    """
    content = f"""
    <div class="header">
        <h2>🔐 Password Reset Request</h2>
    </div>
    
    <p>Hello {user.first_name},</p>
    <p>We received a request to reset your password. Click the button below to create a new password:</p>
    
    <div style="text-align: center; margin: 30px 0;">
        <a href="{reset_link}" class="button">
            Reset Password
        </a>
    </div>
    
    <p class="note">
        If you did not request a password reset, please ignore this email or contact support if you have concerns.
    </p>
    
    <p class="note">
        If the button above doesn't work, you can copy and paste the following link into your browser:<br>
        {reset_link}
    </p>
    """
    
    return get_base_template().format(content=content)
