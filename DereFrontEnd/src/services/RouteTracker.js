import { useEffect } from "react";
import { useLocation } from "react-router-dom";

const RouteTracker = () => {
  const location = useLocation();

  const sendPageView = (path, section = null, dashboardType = null) => {
    try {
      if (window.gtag) {
        const configObj = {
          page_path: path,
        };
        if (section && dashboardType) {
          configObj.dimension1 = section;
        }
        window.gtag("config", "G-8MSXFYNE2G", configObj);
        if (section && dashboardType) {
          window.gtag("event", "dashboard_section_view", {
            event_category: dashboardType,
            event_label: section,
          });
        }
      }
    } catch (error) {
      console.error("Google Analytics error:", error);
    }
  };

  const trackCurrentRoute = () => {
    let path = location.pathname + location.search + location.hash;
    let section = null;
    let dashboardType = null;

    if (location.pathname.includes("/partnerdashboard")) {
      dashboardType = "PartnerDashboard";
      section = window.history.state?.section || location.hash.replace("#", "") || "reports";
      if (!location.hash) {
        path = `${location.pathname}${location.search}#${section}`;
      }
    } else if (location.pathname.includes("/driverdashboard")) {
      dashboardType = "DriverDashboard";
      section = window.history.state?.section || location.hash.replace("#", "") || "vehicle";
      if (!location.hash) {
        path = `${location.pathname}${location.search}#${section}`;
      }
    }

    sendPageView(path, section, dashboardType);
  };

 
  useEffect(() => {
    trackCurrentRoute();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [location]);

  useEffect(() => {
    const originalPushState = window.history.pushState;
    window.history.pushState = function (...args) {
      originalPushState.apply(window.history, args);
      if (args[2].includes("/partnerdashboard") || args[2].includes("/driverdashboard")) {
        trackCurrentRoute();
      }
    };

    const handlePopstate = () => {
      trackCurrentRoute();
    };

    window.addEventListener("popstate", handlePopstate);
    return () => {
      window.history.pushState = originalPushState;
      window.removeEventListener("popstate", handlePopstate);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return null;
};

export default RouteTracker;