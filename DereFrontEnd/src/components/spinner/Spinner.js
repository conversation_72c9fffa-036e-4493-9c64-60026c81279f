// import React from 'react';

// const Spinner = () => {
//   return (
//     <div className="flex justify-center items-center h-screen">
//       <div className="relative flex justify-center items-center h-12 w-12 animate-spin">
//         {[...Array(12)].map((_, i) => (
//           <div
//             key={i}
//             className="absolute w-2 h-6 bg-orange-600 rounded-sm"
//             style={{
//               transform: `rotate(${i * 30}deg) translate(0, -24px)`,
//               opacity: `${1 - i * 0.08}`,
//             }}
//           ></div>
//         ))}
//       </div>
//     </div>
//   );
// };

// export default Spinner;


// import React from 'react';

// const Spinner = () => {
//   return (
//     <div className="flex flex-col justify-center items-center h-[80vh]">
//       <div className="relative flex justify-center items-center h-24 w-24">
//         {[...Array(4)].map((_, i) => (
//           <div
//             key={i}
//             className={`absolute w-5 h-5 rounded-full animate-spin ${
//               i === 2 ? 'bg-gray-800' : 'bg-orange-600'
//             }`}
//             style={{
//               transform: i === 0 ? 'translate(-32px, -32px)' :
//                      i === 1 ? 'translate(32px, -32px)' :
//                      i === 2 ? 'translate(-32px, 32px)' :
//                      'translate(32px, 32px)',
//               animationDelay: `${i * 0.2}s`,
//             }}
//           ></div>
//         ))}
//       </div>
//       <p className="mt-4 text-gray-600">Please wait while we load the content...</p>
//     </div>
// //   );
// // };

// export default Spinner;

// import React from 'react';

// const Spinner = () => {
//   return (
//     <div className="flex flex-col justify-center items-center h-[80vh]">
//       <div className="relative flex justify-center items-center h-24 w-24 animate-spin">
//         {[...Array(4)].map((_, i) => (
//           <div
//             key={i}
//             className={`absolute w-5 h-5 rounded-full ${
//               i === 2 ? 'bg-gray-800' : 'bg-orange-600'
//             }`}
//             style={{
//               transform: i === 0 ? 'translate(-32px, -32px)' :
//                      i === 1 ? 'translate(32px, -32px)' :
//                      i === 2 ? 'translate(-32px, 32px)' :
//                      'translate(32px, 32px)',
//             }}
//           ></div>
//         ))}
//       </div>
//       <p className="mt-4 text-gray-600">Please wait while we load the content...</p>
//     </div>
//   );
// };

// export default Spinner;


// import React from 'react';

// const Spinner = () => {
//   return (
//     <div className="flex flex-col justify-center items-center h-[80vh]">
//       <div
//         className="relative flex justify-center items-center h-16 w-16"
//         style={{
//           animation: 'spin 2s linear infinite',
//         }}
//       >
//         {[...Array(4)].map((_, i) => (
//           <div
//             key={i}
//             className={`absolute w-5 h-5 rounded-full ${
//               i === 2 ? 'bg-gray-800' : 'bg-orange-600'
//             }`}
//             style={{
//               transform: i === 0 ? 'translate(-16px, -16px)' :
//                      i === 1 ? 'translate(16px, -16px)' :
//                      i === 2 ? 'translate(-16px, 16px)' :
//                      'translate(16px, 16px)',
//             }}
//           ></div>
//         ))}
//       </div>
//       <p className="mt-4 text-gray-600">Please wait while we load the content...</p>

//       <style jsx>{`
//         @keyframes spin {
//           from {
//             transform: rotate(0deg);
//           }
//           to {
//             transform: rotate(360deg);
//           }
//         }
//       `}</style>
//     </div>
//   );
// };

// export default Spinner;


import React from 'react';

const Spinner = () => {
  return (
    <div className="flex flex-col justify-center items-center h-screen">
      <div className="relative flex justify-center items-center h-12 w-12 animate-spin">
        {[...Array(4)].map((_, i) => (
          <div
            key={i}
            className={`absolute w-4 h-4 rounded-full ${
              i === 2 ? 'bg-gray-800' : 'bg-orange-600'
            }`}
            style={{
              transform: i === 0 ? 'translate(-12px, -12px)' :
                     i === 1 ? 'translate(12px, -12px)' :
                     i === 2 ? 'translate(-12px, 12px)' :
                     'translate(12px, 12px)',
            }}
          ></div>
        ))}
      </div>
      <p className="mt-4 text-gray-600 font-serif">Please wait while we load the content...</p>
    </div>
  );
};

export default Spinner;