import React, { useState, useEffect } from 'react';
import { Dialog } from '@headlessui/react';

const FileViewerModal = ({ isOpen, onClose, fileUrl, fileName, fileType, vehicleInfo }) => {
  const [copySuccess, setCopySuccess] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const [detectedFileType, setDetectedFileType] = useState(null);

  useEffect(() => {
    if (isOpen && fileUrl) {
      // Reset file type on new file
      setDetectedFileType(null);

      // Sniff the content type from the URL
      fetch(fileUrl, { method: 'HEAD' })
        .then(response => {
          if (response.ok) {
            const contentType = response.headers.get('Content-Type');
            console.log("Detected Content-Type:", contentType);
            setDetectedFileType(contentType);
          }
        })
        .catch(error => {
          console.error('Error sniffing file type:', error);
        });
    }
  }, [isOpen, fileUrl]);

  const handleCopyUrl = async () => {
    try {
      await navigator.clipboard.writeText(fileUrl);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (error) {
      console.error('Failed to copy URL:', error);
      const textArea = document.createElement('textarea');
      textArea.value = fileUrl;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    }
  };

  const handleDownload = async () => {
    setIsDownloading(true);
    try {
      const response = await fetch(fileUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;

      // Construct the new filename
      const originalFileName = fileName || 'document';
      const vehicleIdentifier = vehicleInfo?.registrationNumber || vehicleInfo?.partnerName || '';
      const newFileName = vehicleIdentifier 
        ? `${vehicleIdentifier.replace(/\s+/g, '_')}_${originalFileName}`
        : originalFileName;

      link.download = newFileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Failed to download file:', error);
      window.open(fileUrl, '_blank');
    } finally {
      setIsDownloading(false);
    }
  };

  const finalFileType = detectedFileType || fileType;

  const isImage = finalFileType?.toLowerCase().startsWith('image') || 
                  fileUrl?.toLowerCase().match(/\.(jpg|jpeg|png|gif|webp|bmp|svg)$/i);
  const isPdf = finalFileType?.toLowerCase().includes('pdf') || 
               fileUrl?.toLowerCase().includes('.pdf');

  if (!isOpen || !fileUrl) return null;

  return (
    <Dialog open={isOpen} onClose={onClose} className="relative z-[1100]">
      <div className="fixed inset-0 bg-black/70" aria-hidden="true" />
      
      <div className="fixed inset-0 flex items-center justify-center p-4">
        <Dialog.Panel className="relative w-full max-w-4xl max-h-[90vh] rounded-lg bg-white shadow-xl overflow-hidden">
          <div className="flex items-center justify-between p-4 border-b bg-gray-50">
            <Dialog.Title className="text-lg font-semibold text-gray-900 truncate">
              {fileName || 'Document Viewer'}
            </Dialog.Title>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 p-1"
            >
              <span className="text-2xl font-bold">×</span>
            </button>
          </div>

          {copySuccess && (
            <div className="absolute top-16 left-1/2 transform -translate-x-1/2 bg-green-500 text-white px-4 py-2 rounded shadow-lg z-10">
              URL copied to clipboard!
            </div>
          )}

          <div className="flex gap-3 p-4 border-b bg-gray-50">
            <button
              onClick={handleCopyUrl}
              className="flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 text-sm font-medium"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
              </svg>
              Copy URL
            </button>
            
            <button
              onClick={handleDownload}
              disabled={isDownloading}
              className="flex items-center gap-2 px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 text-sm font-medium disabled:bg-gray-400"
            >
              {isDownloading ? (
                <svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path>
                </svg>
              ) : (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2-8H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2z" />
                </svg>
              )}
              {isDownloading ? 'Downloading...' : 'Download'}
            </button>

            <a
              href={fileUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-2 px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600 text-sm font-medium"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
              </svg>
              Open in New Tab
            </a>
          </div>

          <div className="flex-1 overflow-auto" style={{ maxHeight: 'calc(90vh - 140px)' }}>
            {isImage ? (
              <div className="flex items-center justify-center p-4 bg-gray-100">
                <img
                  src={fileUrl}
                  alt={fileName}
                  className="max-w-full max-h-full object-contain rounded-lg shadow-md"
                  style={{ maxHeight: 'calc(90vh - 200px)' }}
                />
              </div>
            ) : isPdf ? (
              <div className="w-full h-full">
                <iframe
                  src={fileUrl}
                  className="w-full h-full border-0"
                  style={{ minHeight: '500px' }}
                  title={fileName}
                />
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center p-8 text-gray-500">
                <svg className="w-16 h-16 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <p className="text-lg font-medium mb-2">Preview not available</p>
                <p className="text-sm text-center mb-4">
                  This file type cannot be previewed in the browser.<br />
                  Use the download button or open in a new tab to view the file.
                </p>
              </div>
            )}
          </div>
        </Dialog.Panel>
      </div>
    </Dialog>
  );
};

export default FileViewerModal;
