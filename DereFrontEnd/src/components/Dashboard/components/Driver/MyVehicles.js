import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { BASE_URL } from '../../../../services/config';
import SpinnerTwo from '../../../spinner/SpinnerTwo';
import { Dialog } from '@headlessui/react';
import FileViewerModal from '../common/FileViewerModal';

const Vehicle = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [vehicleData, setVehicleData] = useState(null);
  const [isPaymentOpen, setIsPaymentOpen] = useState(false);
  const [paymentAmount, setPaymentAmount] = useState('');
  const [paymentDate, setPaymentDate] = useState('');
  const [confirmationMessage, setConfirmationMessage] = useState('');
  const [showPopup, setShowPopup] = useState(false);
  const [popupMessage, setPopupMessage] = useState('');
  const [popupType, setPopupType] = useState('success');
  const [recentPayments, setRecentPayments] = useState([]);
  const [isFileViewerOpen, setIsFileViewerOpen] = useState(false);
  const [currentFileUrl, setCurrentFileUrl] = useState('');
  const [currentFileName, setCurrentFileName] = useState('');
  const [currentFileType, setCurrentFileType] = useState('');

  const handleViewFile = (fileUrl, fileName, fileType) => {
    setCurrentFileUrl(fileUrl);
    setCurrentFileName(fileName);
    setCurrentFileType(fileType);
    setIsFileViewerOpen(true);
  };

  const showMessage = (message, type = 'success') => {
    setPopupMessage(message);
    setPopupType(type);
    setShowPopup(true);
    setTimeout(() => {
      setShowPopup(false);
    }, 3000);
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        const token = localStorage.getItem('token');
        if (!token) {
          throw new Error('No authentication token found');
        }

        // First fetch payments data separately
        const paymentsResponse = await axios.get(`${BASE_URL}api/revenues/`, {
          headers: { Authorization: `Bearer ${token}` }
        });

        // Continue with rest of data fetching
        const [vehicleRes, makesRes, modelsRes, areasRes] = await Promise.all([
          axios.get(`${BASE_URL}api/vehicles/`, {
            headers: { Authorization: `Bearer ${token}` }
          }),
          axios.get(`${BASE_URL}api/vehicle-makes/`),
          axios.get(`${BASE_URL}api/vehicle-models/`),
          axios.get(`${BASE_URL}api/work-areas/`)
        ]);

        const vehicle = vehicleRes.data.length > 0 ? vehicleRes.data[0] : null;

        if (vehicle) {
          // Set vehicle data first
          const make = makesRes.data.find(m => String(m.id) === String(vehicle.vehicle_make));
          const model = modelsRes.data.find(m => String(m.id) === String(vehicle.vehicle_model));
          const workArea = areasRes.data.find(a => String(a.id) === String(vehicle.preferred_work_area));

          setVehicleData({
            ...vehicle,
            make_name: make ? make.name : 'N/A',
            model_name: model ? model.name : 'N/A',
            work_area_name: workArea ? workArea.name : 'N/A',
            owner_name: vehicle.partner ? `${vehicle.partner.first_name} ${vehicle.partner.last_name}` : 'N/A',
            owner_contact: vehicle.partner?.mobile_number || 'N/A'
          });

          // Handle payments data
          const payments = paymentsResponse.data;
          const sortedPayments = payments
            .sort((a, b) => new Date(b.date) - new Date(a.date))
            .slice(0, 6);
          
          setRecentPayments(sortedPayments);
        }
      } catch (error) {
        setError(error.response?.data?.detail || 'Failed to fetch data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handlePaymentSubmit = async () => {
    if (!paymentDate || !paymentAmount || !confirmationMessage) {
      showMessage('Please fill in all required fields', 'error');
      return;
    }

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        showMessage('Authentication token not found', 'error');
        return;
      }

      // Create form data
      const formData = new FormData();
      formData.append('date', paymentDate);
      formData.append('amount', paymentAmount);
      formData.append('confirmation_message', confirmationMessage);
      formData.append('vehicle', vehicleData.id);

      const response = await axios.post(
        `${BASE_URL}api/revenues/`,
        formData,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'multipart/form-data'
          }
        }
      );

      if (response.status === 201) {
        // Add new payment to existing payments and sort
        const newPayment = response.data;
        const updatedPayments = [newPayment, ...recentPayments]
          .sort((a, b) => new Date(b.date) - new Date(a.date))
          .slice(0, 6);

        setRecentPayments(updatedPayments);
        showMessage('Payment submitted successfully');
        setIsPaymentOpen(false);
        
        // Reset form
        setPaymentDate('');
        setPaymentAmount('');
        setConfirmationMessage('');
      }
    } catch (error) {
      const errorMessage = error.response?.data?.detail || 
                          error.response?.data?.message ||
                          Object.values(error.response?.data || {})[0] ||
                          'Failed to submit payment';
      showMessage(Array.isArray(errorMessage) ? errorMessage[0] : errorMessage, 'error');
    }
  };

  if (loading) return <SpinnerTwo />;
  if (error) return <div className="p-4 text-red-600">{error}</div>;
  if (!vehicleData) return <div className="p-4 h-[50vh] flex items-center justify-center"> <span className='text-3xl font-bold font-serif'>You Currently Have No Linked Vehicle</span></div>;

  return (
    <div className="p-4 pt-0 relative min-h-fit">
      {showPopup && (
        <div
          className={`fixed top-5 left-1/2 transform -translate-x-1/2 ${
            popupType === 'success' ? 'bg-green-500' : 'bg-red-500'
          } text-white p-4 rounded shadow-lg z-[9999] font-serif text-sm md:text-base max-w-xs sm:max-w-sm md:max-w-md`}
          style={{ boxShadow: '0 0 20px rgba(0, 0, 0, 0.3)' }}
        >
          {popupMessage}
        </div>
      )}
      {/* Vehicle Registration Header */}
      <div className="w-full bg-[rgba(218,217,215,0.6)] rounded-xl py-1 mb-4 shadow-sm">
        <h2 className="text-xl sm:text-2xl md:text-3xl font-black text-center">
          {vehicleData.registration_number}
        </h2>
      </div>
      
      <div className="relative max-w-7xl mx-auto px-2 sm:px-4 lg:px-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6"> 
          {/* Vehicle Details Card */}
          <div className="bg-[rgba(218,217,215,0.6)] rounded-2xl sm:rounded-3xl shadow-sm h-auto lg:h-[400px] overflow-y-auto">
            <h3 className="text-xl sm:text-2xl md:text-2xl font-[900] p-2 sm:p-3 underline sticky top-0 z-10">
              Vehicle details
            </h3>
            <div className="p-5 sm:p-4 rounded-b-lg space-y-3 sm:space-y-4">
              {/* Vehicle details items */}
              <div className="flex items-center gap-1 min-w-0">
                <label className="text-sm xl:text-base font-bold text-black text-left shrink-0 w-[150px]">OWNER:</label>
                <p className="text-sm  xl:text-base break-words">{vehicleData.owner_name || 'N/A'}</p>
              </div>

              <div className="flex items-center gap-1 min-w-0">
                <label className="text-sm xl:text-base font-bold text-black text-left shrink-0 w-[150px]">OWNER CONTACT:</label>
                <p className="text-sm xl:text-base break-words">{vehicleData.owner_contact || 'N/A'}</p>
              </div>

              <div className="flex items-center gap-1 min-w-0">
                <label className="text-sm xl:text-base font-bold text-black text-left shrink-0 w-[150px]">VEHICLE MAKE:</label>
                <p className="text-sm xl:text-base break-words">{vehicleData.make_name}</p>
              </div>

              <div className="flex items-center gap-1 min-w-0">
                <label className="text-sm xl:text-base font-bold text-black text-left shrink-0 w-[150px]">VEHICLE MODEL:</label>
                <p className="text-sm xl:text-base break-words">{vehicleData.model_name}</p>
              </div>

              <div className="flex items-center gap-1 min-w-0">
                <label className="text-sm xl:text-base font-bold text-black text-left shrink-0 w-[150px]">WORKING AREA:</label>
                <p className="text-sm xl:text-base break-words">{vehicleData.work_area_name}</p>
              </div>

              <div className="flex items-center gap-1 min-w-0">
                <label className=" text-sm xl:text-base font-bold text-black text-left shrink-0 w-[150px]">WORKING DAYS:</label>
                <p className="text-sm xl:text-base break-words">{vehicleData.work_days || 'N/A'}</p>
              </div>
            </div>
          </div>

          {/* Shared Documents Card */}
          <div className="bg-[rgba(218,217,215,0.6)] rounded-2xl sm:rounded-3xl shadow-sm h-auto lg:h-[400px] overflow-y-auto">
            <h3 className="text-xl sm:text-2xl md:text-2xl font-[900] p-2 sm:p-3 underline sticky top-0 z-10">
              Shared Documents
            </h3>
            <div className="p-3 sm:p-4 rounded-b-lg space-y-2 sm:space-y-3">
              {vehicleData.ntsa_inspection_doc && (
                <div className="flex items-center justify-between">
                  <button
                    onClick={() => handleViewFile(vehicleData.ntsa_inspection_doc, 'NTSA Inspection Details', 'application/pdf')}
                    className="text-orange-700 hover:text-orange-800 flex items-center"
                  >
                    <div className="flex items-start">
                      <span className='text-left font-bold italic'>NTSA INSPECTION DETAILS</span>
                      <svg 
                        xmlns="http://www.w3.org/2000/svg" 
                        className="h-4 w-4 ml-0.5 mt-0.5" 
                        fill="none" 
                        viewBox="0 0 24 24" 
                        stroke="currentColor"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                      </svg>
                    </div>
                    {vehicleData.ntsa_expiry_date && (
                      <span className="ml-2 text-sm text-black font-bold">
                        Expiry: {new Date(vehicleData.ntsa_expiry_date).toLocaleDateString()}
                      </span>
                    )}
                  </button>
                </div>
              )}

              {vehicleData.lease_agreement && (
                <div className="flex items-center justify-between">
                  <button
                    onClick={() => handleViewFile(vehicleData.lease_agreement, 'Lease Agreement', 'application/pdf')}
                    className="text-orange-700 hover:text-orange-800 flex items-start"
                  >
                    <span className='text-left font-bold italic'>LEASE AGREEMENT</span>
                    <svg 
                      xmlns="http://www.w3.org/2000/svg" 
                      className="h-4 w-4 ml-0.5 mt-0.5" 
                      fill="none" 
                      viewBox="0 0 24 24" 
                      stroke="currentColor"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                  </button>
                </div>
              )}

              {vehicleData.insurance_doc && (
                <div className="flex items-center justify-between">
                  <button
                    onClick={() => handleViewFile(vehicleData.insurance_doc, 'Insurance Details', 'application/pdf')}
                    className="text-orange-700 hover:text-orange-800 flex items-start"
                  >
                    <span className='text-left font-bold italic'>INSURANCE DETAILS</span>
                    <svg 
                      xmlns="http://www.w3.org/2000/svg" 
                      className="h-4 w-4 ml-0.5 mt-0.5" 
                      fill="none" 
                      viewBox="0 0 24 24" 
                      stroke="currentColor"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                    {vehicleData.insurance_expiry_date && (
                      <span className="ml-2 text-sm not-italic text-black font-bold">
                        Expiry: {new Date(vehicleData.insurance_expiry_date).toLocaleDateString()}
                      </span>
                    )}
                  </button>
                </div>
              )}

              {vehicleData.logbook && (
                <div className="flex items-center justify-between">
                  <button
                    onClick={() => handleViewFile(vehicleData.logbook, 'Car Logbook', 'application/pdf')}
                    className="text-orange-700 hover:text-orange-800 flex items-start"
                  >
                    <span className='text-left font-bold italic'>CAR LOG BOOK</span>
                    <svg 
                      xmlns="http://www.w3.org/2000/svg" 
                      className="h-4 w-4 ml-0.5 mt-0.5" 
                      fill="none" 
                      viewBox="0 0 24 24" 
                      stroke="currentColor"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Recent Payments Column */}
          <div className="flex flex-col h-auto lg:h-[400px] justify-between md:col-span-2 lg:col-span-1">
            {/* Recent Payments Card */}
            <div className="bg-[rgba(218,217,215,0.6)] rounded-2xl sm:rounded-3xl shadow-sm flex-1">
              <h3 className="text-xl sm:text-2xl md:text-2xl font-[900] p-2 sm:p-3 underline">
                Recent Payments
              </h3>
              <div className="py-3 sm:py-4 rounded-b-lg px-4 sm:px-9">
                <div className="flex justify-between mb-3">
                  <span className="font-bold text-base underline">DATE</span>
                  <span className="font-bold text-base underline">AMOUNT</span>
                </div>
                <div className="space-y-2 font-bold">
                  {recentPayments.length > 0 ? (
                    recentPayments.map((payment, index) => (
                      <div key={payment.id} className="flex justify-between items-center">
                        <span className="text-base">
                          {new Date(payment.date).toLocaleDateString()}
                        </span>
                        <div className="flex-grow border-t-2 border-dotted border-black mx-2"></div>
                        <div className="flex items-center">
                          <span className="text-base">KSH</span>
                          <span className="text-base ml-1">{payment.amount}</span>
                        </div>
                      </div>
                    ))
                  ) : (
                    <p className="text-center text-gray-500 py-4">No payment history available</p>
                  )}
                </div>
              </div>
            </div>

            {/* Make Payments Button */}
            <div className="flex justify-end mt-4">
              <button
                onClick={() => setIsPaymentOpen(true)}
                className="w-full sm:w-auto bg-orange-700 hover:bg-orange-800 text-white font-medium px-4 sm:px-5 py-2 rounded-lg shadow-lg transition-colors border-none text-sm sm:text-base"
              >
                MAKE PAYMENTS
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Payment Dialog */}
      <Dialog
        open={isPaymentOpen}
        onClose={() => setIsPaymentOpen(false)}
        className="relative z-50"
      >
        <div className="fixed inset-0 bg-black/30" aria-hidden="true" />
        <div className="fixed inset-0 flex items-center justify-center p-2 sm:p-4">
          <Dialog.Panel className="w-full sm:w-[500px] md:w-[600px] lg:w-[700px] h-[90vh] sm:h-[607px] transform overflow-hidden rounded-lg bg-white shadow-xl transition-all">
            <div className="h-full flex flex-col p-6">
              {/* Header */}
              <div className="flex justify-between items-center mb-8">
                <button
                  onClick={() => setIsPaymentOpen(false)}
                  className="text-gray-400 hover:text-gray-500 absolute right-4 top-4"
                >
                  <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
                <div className="w-full text-center">
                  <Dialog.Title className="text-xl font-bold">
                    PAYMENT : {vehicleData.registration_number}
                  </Dialog.Title>
                </div>
              </div>
              
              <div className="flex-1 space-y-6">
                <div>
                  <label className="block text-sm font-medium text-black-700 mb-2">
                    DATE:
                  </label>
                  <div className="relative">
                    <input
                      type="date"
                      value={paymentDate}
                      onChange={(e) => setPaymentDate(e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-orange-500 bg-[#F3F4F6]"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-black-700 mb-2">
                    AMOUNT:
                  </label>
                  <div className="relative">
                    <input
                      type="number"
                      value={paymentAmount}
                      onChange={(e) => setPaymentAmount(e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-orange-500 bg-[#F3F4F6]"
                      placeholder="Enter amount"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-black-700 mb-2">
                    CONFIRMATION MESSAGE:
                  </label>
                  <div className="relative">
                    <textarea
                      value={confirmationMessage}
                      onChange={(e) => setConfirmationMessage(e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-orange-500 bg-[#F3F4F6]"
                      placeholder="Enter confirmation message"
                      rows="4"
                      required
                    ></textarea>
                  </div>
                </div>
              </div>

              {/* Submit Button */}
              <div className="mt-8 mb-8">
                <button
                  onClick={handlePaymentSubmit}
                  className="w-full bg-orange-700 hover:bg-orange-800 text-white font-medium py-3 rounded-lg shadow-lg transition-colors"
                >
                  SUBMIT PAYMENT
                </button>
              </div>
            </div>
          </Dialog.Panel>
        </div>
      </Dialog>
      <FileViewerModal
        isOpen={isFileViewerOpen}
        onClose={() => setIsFileViewerOpen(false)}
        fileUrl={currentFileUrl}
        fileName={currentFileName}
        fileType={currentFileType}
        vehicleInfo={{
          registrationNumber: vehicleData.registration_number,
        }}
      />
    </div>
  );
};

export default Vehicle;
