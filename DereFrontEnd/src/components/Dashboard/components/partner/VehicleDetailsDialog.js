import React, { useState, useEffect } from 'react';
import axios from "axios";
import { Dialog } from '@headlessui/react';
import { BASE_URL } from "../../../../services/config";
import '../../../../styles/noscroll.css';
import FileViewerModal from '../common/FileViewerModal';

const DriverDetailsCard = ({ driver, onViewFile }) => {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
        <div>
          <label className="block text-xs sm:text-sm font-medium text-black mb-1">
            FIRST NAME
          </label>
          <input
            type="text"
            value={driver.first_name || 'N/A'}
            disabled
            className="w-full px-3 py-2 border border-transparent rounded-md bg-gray-100 text-sm"
          />
        </div>
        <div>
          <label className="block text-xs sm:text-sm font-medium text-black mb-1">
            LAST NAME
          </label>
          <input
            type="text"
            value={driver.last_name || 'N/A'}
            disabled
            className="w-full px-3 py-2 border border-transparent rounded-md bg-gray-100 text-sm"
          />
        </div>
        <div>
          <label className="block text-xs sm:text-sm font-medium text-black mb-1">
            EMAIL
          </label>
          <input
            type="text"
            value={driver.email || 'N/A'}
            disabled
            className="w-full px-3 py-2 border border-transparent rounded-md bg-gray-100 text-sm"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
        <div>
          <label className="block text-xs sm:text-sm font-medium text-black mb-1">
            MOBILE NUMBER
          </label>
          <input
            type="text"
            value={driver.mobile_number || 'N/A'}
            disabled
            className="w-full px-3 py-2 border border-transparent rounded-md bg-gray-100 text-sm"
          />
        </div>
        <div>
          <label className="block text-xs sm:text-sm font-medium text-black mb-1">
            ID NUMBER
          </label>
          <input
            type="text"
            value={driver.id_number || 'N/A'}
            disabled
            className="w-full px-3 py-2 border border-transparent rounded-md bg-gray-100 text-sm"
          />
        </div>
      </div>

      <div>
        <label className="block text-xs sm:text-sm font-medium text-black mb-2">
          DOCUMENTS
        </label>
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
          {driver.id_photo && (
            <button
              onClick={() => onViewFile(driver.id_photo, 'ID Photo', 'image')}
              className="px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600 text-sm text-center"
            >
              View ID Photo
            </button>
          )}
          {driver.license_photo && (
            <button
              onClick={() => onViewFile(driver.license_photo, 'Driver License', 'image')}
              className="px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600 text-sm text-center"
            >
              View License
            </button>
          )}
          {driver.psv_photo && (
            <button
              onClick={() => onViewFile(driver.psv_photo, 'PSV License', 'image')}
              className="px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600 text-sm text-center"
            >
              View PSV
            </button>
          )}
          {driver.good_conduct_photo && (
            <button
              onClick={() => onViewFile(driver.good_conduct_photo, 'Good Conduct Certificate', 'image')}
              className="px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600 text-sm text-center"
            >
              View Good Conduct
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

const FileUpload = ({
  label,
  file,
  fileType,
  isEditMode,
  handleFileChange,
  tempFiles,
  tempFilePreviews,
  expiryDate,
  handleExpiryChange,
  onViewFile
}) => {
  const needsExpiry = fileType === 'ntsa_inspection_doc' || fileType === 'insurance_doc';

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <label className="text-xs sm:text-sm font-medium text-black">
          {label}
        </label>

        {needsExpiry && (
          <div className="flex items-center gap-1 ml-4">
            <span className="text-xs text-gray-600 whitespace-nowrap">Expiry&nbsp;Date:</span>
            <input
              type="date"
              value={expiryDate || ''}
              onChange={handleExpiryChange}
              disabled={!isEditMode}
              className={`px-2 py-1 text-xs border ${
                isEditMode ? 'border-gray-300' : 'border-transparent'
              } rounded-md bg-gray-100`}
            />
          </div>
        )}
      </div>
      <div className="flex flex-col gap-2">
        <div className="flex items-center gap-3">
          {tempFilePreviews[fileType] ? (
            <span className="text-sm text-gray-700">New file selected</span>
          ) : file ? (
            <button
              onClick={() => onViewFile(file, label, fileType)}
              className="text-sm font-semibold font-serif text-orange-600 hover:text-orange-700 underline"
            >
              View&nbsp;File
            </button>
          ) : (
            <span className="text-sm text-gray-500">No document</span>
          )}

          <input
            type="file"
            id={fileType}
            onChange={(e) => handleFileChange(e, fileType)}
            className="hidden"
            accept="image/jpeg,image/png,image/jpg,application/pdf"
          />

          {isEditMode && (
            <button
              type="button"
              onClick={() => document.getElementById(fileType).click()}
              className="text-sm font-medium font-serif text-orange-600 hover:text-orange-700"
            >
              Update&nbsp;File
            </button>
          )}
        </div>

        {tempFiles[fileType] && (
          <p className="text-sm text-gray-600">
            Selected:&nbsp;{tempFiles[fileType].name}
          </p>
        )}
      </div>
    </div>
  );
};

const VehicleDetailsDialog = ({
  isOpen,
  onClose,
  vehicleData,
  onShowDriverDetails,
  onUnlinkDriver,
  onLinkDriver,
  onUpdateVehicle,
  setShowPaymentPlans
}) => {
  const [isEditMode, setIsEditMode] = useState(false);
  const [editableData, setEditableData] = useState(vehicleData || {});
  const [workAreas, setWorkAreas] = useState([]);
  const [vehicleMakes, setVehicleMakes] = useState([]);
  const [vehicleModels, setVehicles] = useState([]);
  const [successMessage, setSuccessMessage] = useState(""); // Success message state
  const [errorMessage, setErrorMessage] = useState(""); // Add state for error message
  const [isLinkDriverDialogOpen, setIsLinkDriverDialogOpen] = useState(false);
  const [driverSearchQuery, setDriverSearchQuery] = useState('');
  const [isAssigning, setIsAssigning] = useState(false);
  const [availableDrivers, setAvailableDrivers] = useState([]);
  const [filteredDrivers, setFilteredDrivers] = useState([]);
  const [isUnlinkConfirmOpen, setIsUnlinkConfirmOpen] = useState(false);
  const [isUnlinking, setIsUnlinking] = useState(false);
  const [isDriverDetailsDialogOpen, setIsDriverDetailsDialogOpen] = useState(false);
  const [selectedDriver, setSelectedDriver] = useState(null);

  // File viewer modal state
  const [isFileViewerOpen, setIsFileViewerOpen] = useState(false);
  const [currentFileUrl, setCurrentFileUrl] = useState('');
  const [currentFileName, setCurrentFileName] = useState('');
  const [currentFileType, setCurrentFileType] = useState('');

  const [tempFiles, setTempFiles] = useState({
    lease_agreement: null,
    logbook: null,
    vehicle_photo: null,
    ntsa_inspection_doc: null,
    insurance_doc: null
  });

  const [tempFilePreviews, setTempFilePreviews] = useState({
    lease_agreement: null,
    logbook: null,
    vehicle_photo: null,
    ntsa_inspection_doc: null,
    insurance_doc: null
  });

  const [isVehicleUpdating, setIsVehicleUpdating] = useState(false);
  const [isDialogLoading, setIsDialogLoading] = useState(false);

  useEffect(() => {
    if (isOpen && vehicleData) {
      setEditableData({
        ...vehicleData,
        make_id: String(vehicleData.make?.id || vehicleData.vehicle_make || ''),
        model_id: String(vehicleData.model?.id || vehicleData.vehicle_model || ''),
        registration_number: vehicleData.registration_number || '',
        preferred_work_area: vehicleData.preferred_work_area || '',
        work_days: vehicleData.work_days || '',
        ntsa_expiry_date: vehicleData.ntsa_expiry_date || '',
        insurance_expiry_date: vehicleData.insurance_expiry_date || '',
        year_of_manufacture: vehicleData.year_of_manufacture || '',
      });
    }
  }, [isOpen, vehicleData]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [makesRes, areasRes] = await Promise.all([
          axios.get(`${BASE_URL}api/vehicle-makes/`),
          axios.get(`${BASE_URL}api/work-areas/`),
        ]);

        setVehicleMakes(makesRes.data || []);
        setWorkAreas(areasRes.data || []);
      } catch (error) {
        console.error("Error fetching vehicle makes/areas:", error);
      }
    };
    fetchData();
  }, []);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const modelsRes = await axios.get(`${BASE_URL}api/vehicle-models/`);
        setVehicles(modelsRes.data || []);
      } catch (error) {
        console.error("Error fetching vehicle models:", error);
      }
    };
    fetchData();
  }, []);

  useEffect(() => {
    console.log('Current make_id:', editableData.make_id);
    console.log('Available models:', vehicleModels);
  }, [editableData.make_id, vehicleModels]);

  useEffect(() => {
    const fetchPartnerDrivers = async () => {
      if (isLinkDriverDialogOpen) {
        try {
          const token = localStorage.getItem("token");
          const response = await axios.get(`${BASE_URL}api/partner-drivers/`, {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          });
          setAvailableDrivers(response.data || []);
        } catch (error) {
          console.error("Error fetching partner drivers:", error);
        }
      }
    };

    fetchPartnerDrivers();
  }, [isLinkDriverDialogOpen]);

  useEffect(() => {
    if (!driverSearchQuery.trim()) {
      setFilteredDrivers([]);
      return;
    }

    const filtered = availableDrivers.filter(driver =>
      driver.first_name?.toLowerCase().includes(driverSearchQuery.toLowerCase()) ||
      driver.last_name?.toLowerCase().includes(driverSearchQuery.toLowerCase()) ||
      driver.id_number?.toLowerCase().includes(driverSearchQuery.toLowerCase())
    );
    setFilteredDrivers(filtered);
  }, [driverSearchQuery, availableDrivers]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setEditableData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleFileChange = (e, fileType) => {
    const file = e.target.files[0];
    if (!file) return;
    if (fileType === 'vehicle_photo') {
      const validImageTypes = ["image/jpeg", "image/png", "image/jpg", "image/gif", "application/pdf"];

      if (!validImageTypes.includes(file.type)) {
        alert("Please select a valid image file (JPG, PNG, PDF, JPEG, or GIF)");
        e.target.value = '';
        return;
      }
    }

    setTempFiles(prev => ({
      ...prev,
      [fileType]: file
    }));

    const previewUrl = URL.createObjectURL(file);
    setTempFilePreviews(prev => ({
      ...prev,
      [fileType]: previewUrl
    }));
  };

  // File viewer handler
  const handleViewFile = (fileUrl, fileName, fileType) => {
    setCurrentFileUrl(fileUrl);
    setCurrentFileName(fileName);
    setCurrentFileType(fileType);
    setIsFileViewerOpen(true);
  };

  const handleConfirmUpdate = async () => {
    try {
      const submissionData = new FormData();

      submissionData.append("registration_number", editableData.registration_number);
      submissionData.append("vehicle_make", editableData.make_id);
      submissionData.append("vehicle_model", editableData.model_id);
      submissionData.append("preferred_work_area", editableData.preferred_work_area);
      submissionData.append("work_days", editableData.work_days);
      submissionData.append("ntsa_expiry_date", editableData.ntsa_expiry_date || '');
      submissionData.append("insurance_expiry_date", editableData.insurance_expiry_date || '');
      submissionData.append("year_of_manufacture", editableData.year_of_manufacture || '');

      Object.entries(tempFiles).forEach(([key, file]) => {
        if (file) {
          submissionData.append(key, file);
        }
      });

      const token = localStorage.getItem("token");
      if (!token) {
        console.error("Authentication token not found.");
        return;
      }

      setIsVehicleUpdating(true);
      const response = await axios.patch(
        `${BASE_URL}api/vehicles/${vehicleData.id}/`,
        submissionData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.status === 200) {
        setSuccessMessage("Vehicle details updated successfully!");
        setTempFiles({});
        setTempFilePreviews({});

        if (onUpdateVehicle) {
          onUpdateVehicle(response.data);
        }
        setTimeout(() => {
          setSuccessMessage("");
          setIsEditMode(false);
        }, 1500);
      }
    } catch (error) {
      console.error("Error updating vehicle details:", error);
      alert(`Error updating vehicle details: ${error.message}`);
    } finally {
      setIsVehicleUpdating(false);
    }
  };

  const handleCancelUpdate = () => {
    setEditableData(vehicleData);
    setIsEditMode(false);
    setTempFiles({});
    setTempFilePreviews({});
  };

  const handleAssignDriver = async (driverId) => {
    if (!driverId) {
      setErrorMessage("Please select a driver.");
      return;
    }

    setIsAssigning(true);
    setIsVehicleUpdating(true);
    setErrorMessage("");
    try {
      const token = localStorage.getItem("token");
      if (!token) {
        console.error("Authentication token not found.");
        return;
      }

      const response = await axios.patch(
        `${BASE_URL}api/vehicles/${vehicleData.id}/assign_driver/`,
        { driver_id: driverId },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.status === 200) {
        setSuccessMessage("Driver linked successfully!");
        setIsLinkDriverDialogOpen(false);
        await refreshVehicleData();
        setTimeout(() => {
          setSuccessMessage("");
        }, 1500);
      }
    } catch (error) {
      console.error("Error linking driver:", error);
      let errorMsg = "Error linking driver. Please try again.";
      if (error.response && error.response.data) {
        if (error.response.data.detail) {
          errorMsg = error.response.data.detail;
        } else if (error.response.data.non_field_errors) {
          errorMsg = error.response.data.non_field_errors.join(", ");
        } else {
          errorMsg = "This driver is already linked to another vehicle, unlink them to link again or try linking a different driver.";
        }
      }
      setErrorMessage(errorMsg);
    } finally {
      setIsAssigning(false);
      setIsVehicleUpdating(false);
    }
  };

  const handleUnlinkDriver = async () => {
    setIsUnlinking(true);
    try {
      const token = localStorage.getItem("token");
      if (!token) {
        console.error("Authentication token not found.");
        return;
      }

      const response = await axios.patch(
        `${BASE_URL}api/vehicles/${vehicleData.id}/assign_driver/`,
        { driver_id: null },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
        }
      );

      if (response.status === 200) {
        setSuccessMessage('Driver unlinked successfully!');
        setIsUnlinkConfirmOpen(false);
        await refreshVehicleData();
        setTimeout(() => {
          setSuccessMessage('');
        }, 1500);
      }
    } catch (error) {
      console.error('Error unlinking driver:', error);
      alert(`Error unlinking driver: ${error.response?.data?.message || error.message}`);
    } finally {
      setIsUnlinking(false);
    }
  };

  const handleShowDriverDetails = () => {
    if (editableData.driver) {
      setSelectedDriver(editableData.driver);
      setIsDriverDetailsDialogOpen(true);
    }
  };

  const refreshVehicleData = async () => {
    setIsDialogLoading(true);
    try {
      const token = localStorage.getItem("token");
      const response = await axios.get(`${BASE_URL}api/vehicles/${vehicleData.id}/`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      setEditableData({
        ...response.data,
        make_id: String(response.data.make?.id || response.data.vehicle_make || ''),
        model_id: String(response.data.model?.id || response.data.vehicle_model || ''),
        registration_number: response.data.registration_number || '',
        preferred_work_area: response.data.preferred_work_area || '',
        work_days: response.data.work_days || '',
        ntsa_expiry_date: response.data.ntsa_expiry_date || '',
        insurance_expiry_date: response.data.insurance_expiry_date || '',
      });
      // Optionally notify parent
      if (onUpdateVehicle) onUpdateVehicle(response.data);
    } catch (error) {
      console.error("Error refreshing vehicle data:", error);
    } finally {
      setIsDialogLoading(false);
    }
  };

  if (!isOpen || !vehicleData) return null;

  return (
    <div className="flex flex-col min-h-screen bg-cover max-w-full">
      <Dialog open={isOpen} onClose={onClose} className="relative z-[990] no-scrollbar">
        <div className="fixed inset-0 bg-black/40" aria-hidden="true" />

        <div className="fixed inset-0 flex items-start justify-center p-4 overflow-y-auto no-scrollbar">
          <Dialog.Panel className="relative w-full max-w-5xl rounded-lg bg-white p-4 sm:p-6 shadow-xl my-2 no-scrollbar">
            <div className="max-h-[90vh] overflow-y-auto no-scrollbar">
              {/* Success Popup */}
              {successMessage && (
                <div className="fixed top-4 left-1/2 transform -translate-x-1/2 bg-green-500 text-white px-6 py-3 rounded shadow-lg z-[999]">
                  {successMessage}
                </div>
              )}

              {/* Error Popup
              {errorMessage && (
                <div className="fixed top-4 left-1/2 transform -translate-x-1/2 bg-red-500 text-white px-6 py-3 rounded shadow-lg z-[999]">
                  {errorMessage}
                </div>
              )} */}

              {/* Header */}
              <div className="sticky top-0 bg-white z-10 mb-6 pb-2 border-b no-scrollbar">
                <Dialog.Title className="text-center text-2xl font-bold text-gray-900 no-scrollbar">
                  {editableData.registration_number || "Vehicle Details"}
                </Dialog.Title>
                <button
                  onClick={onClose}
                  className="absolute top-0 right-0 text-gray-400 hover:text-gray-500 p-1"
                >
                  <span className="text-2xl font-bold leading-none">×</span>
                </button>
              </div>

              {/* Form Fields */}
              <div className="space-y-4 sm:space-y-6 overflow-y-auto no-scrollbar" no-scrollbar>
                {/* Vehicle Details */}
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-4 sm:gap-6 no-scrollbar">
                  <div className="w-full no-scrollbar">
                    <label className="block text-xs sm:text-sm font-medium text-black mb-1">
                      CAR REGISTRATION NUMBER *
                    </label>
                    <input
                      type="text"
                      name="registration_number"
                      value={editableData.registration_number || ''}
                      onChange={handleInputChange}
                      disabled={!isEditMode}
                      className={`w-full px-3 py-2 border ${isEditMode ? 'border-gray-300' : 'border-transparent'
                        } rounded-md bg-gray-100`}
                    />
                  </div>

                  <div className="w-full">
                    <label className="block text-xs sm:text-sm font-medium text-black mb-1">
                      VEHICLE MAKE *
                    </label>
                    <select
                      name="make_id"
                      value={editableData.make_id || ''}
                      onChange={handleInputChange}
                      disabled={!isEditMode}
                      className={`w-full px-3 py-2 border ${isEditMode ? 'border-gray-300' : 'border-transparent'
                        } rounded-md bg-gray-100`}
                    >
                      <option value="">Select Make</option>
                      {vehicleMakes.map((make) => (
                        <option key={make.id} value={make.id}>
                          {make.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div className="w-full">
                    <label className="block text-xs sm:text-sm font-medium text-black mb-1">
                      VEHICLE MODEL *
                    </label>
                    <select
                      name="model_id"
                      value={editableData.model_id || ''}
                      onChange={handleInputChange}
                      disabled={!isEditMode || !editableData.make_id}
                      className={`w-full px-3 py-2 border ${isEditMode ? 'border-gray-300' : 'border-transparent'
                        } rounded-md bg-gray-100`}
                    >
                      <option value="">Select Model</option>
                      {vehicleModels
                        .filter((model) => String(model.make.id) === String(editableData.make_id))
                        .map((model) => (
                          <option key={model.id} value={model.id}>
                            {model.name}
                          </option>
                        ))}
                    </select>
                  </div>

                  <div className="w-full">
                    <label className="block text-xs sm:text-sm font-medium text-black mb-1">
                      YEAR OF MANUFACTURE *
                    </label>
                    <select
                      name="year_of_manufacture"
                      value={editableData.year_of_manufacture || ''}
                      onChange={handleInputChange}
                      disabled={!isEditMode}
                      className={`w-full px-3 py-2 border ${isEditMode ? 'border-gray-300' : 'border-transparent'
                        } rounded-md bg-gray-100`}
                    >
                      <option value="">-- Select Year --</option>
                      {Array.from({ length: new Date().getFullYear() - 1989 }, (_, i) => {
                        const year = new Date().getFullYear() - i;
                        return (
                          <option key={year} value={year}>
                            {year}
                          </option>
                        );
                      })}
                    </select>
                  </div>
                </div>

                {/* Work Details */}
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 items-center no-scrollbar">
                  <div>
                    <label className="block text-xs sm:text-sm font-medium text-black mb-1">
                      PREFERRED WORKING AREA *
                    </label>
                    <select
                      name="preferred_work_area"
                      value={editableData.preferred_work_area || ''}
                      onChange={handleInputChange}
                      disabled={!isEditMode}
                      className={`w-full px-3 py-2 border ${isEditMode ? 'border-gray-300' : 'border-transparent'
                        } rounded-md bg-gray-100`}
                    >
                      <option value="">Select Work Area</option>
                      {workAreas.map((area) => (
                        <option key={area.id} value={area.id}>
                          {area.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-xs sm:text-sm font-medium text-black mb-1">
                      WORKING DAYS *
                    </label>
                    <input
                      type="text"
                      name="work_days"
                      value={editableData.work_days || ''}
                      onChange={handleInputChange}
                      disabled={!isEditMode}

                      className={`w-full px-3 py-2 border ${isEditMode ? 'border-gray-300' : 'border-transparent'
                        } rounded-md bg-gray-100`}
                    />
                  </div>

                  <div className="flex md:mt-6">
                    {editableData.driver ? (
                      <button
                        onClick={handleShowDriverDetails}
                        className="px-12 py-2 text-sm text-white font-serif bg-gray-600 hover:bg-gray-700 rounded-md font-medium flex items-center gap-2"
                      >
                        Driver Details
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-4 w-4"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M14 5l7 7m0 0l-7 7m7-7H3"
                          />
                        </svg>
                      </button>
                    ) : (
                      <p className="text-sm text-gray-500 italic">No driver linked to this vehicle</p>
                    )}
                  </div>
                </div>

                {/* File Uploads */}
                <div>
                  <p className="text-sm font-semibold font-serif text-gray-800 mb-4">
                    Optional Uploads:
                    <span className="block text-xs text-orange-500">
                      Tip: Sharing these details will help drivers with taxi app registrations, making the process smoother and more convenient.
                    </span>
                  </p>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-8 gap-y-6">
                    {/* Left Column */}
                    <div className="space-y-6">
                      <FileUpload
                        label="LEASE AGREEMENT"
                        file={vehicleData.lease_agreement}
                        fileType="lease_agreement"
                        isEditMode={isEditMode}
                        handleFileChange={handleFileChange}
                        tempFiles={tempFiles}
                        tempFilePreviews={tempFilePreviews}
                        onViewFile={handleViewFile}
                      />

                      <FileUpload
                        label="CAR LOG BOOK"
                        file={vehicleData.logbook}
                        fileType="logbook"
                        isEditMode={isEditMode}
                        handleFileChange={handleFileChange}
                        tempFiles={tempFiles}
                        tempFilePreviews={tempFilePreviews}
                        onViewFile={handleViewFile}
                      />

                      <FileUpload
                        label="CAR PHOTO"
                        file={vehicleData.vehicle_photo}
                        fileType="vehicle_photo"
                        isEditMode={isEditMode}
                        handleFileChange={handleFileChange}
                        tempFiles={tempFiles}
                        tempFilePreviews={tempFilePreviews}
                        onViewFile={handleViewFile}
                      />
                    </div>

                    {/* Right Column */}
                    <div className="space-y-6">
                      <FileUpload
                        label="NTSA INSPECTION DETAILS"
                        file={vehicleData.ntsa_inspection_doc}
                        fileType="ntsa_inspection_doc"
                        isEditMode={isEditMode}
                        handleFileChange={handleFileChange}
                        tempFiles={tempFiles}
                        tempFilePreviews={tempFilePreviews}
                        expiryDate={editableData.ntsa_expiry_date}
                        handleExpiryChange={(e) => handleInputChange({
                          target: { name: 'ntsa_expiry_date', value: e.target.value }
                        })}
                        onViewFile={handleViewFile}
                      />

                      <FileUpload
                        label="INSURANCE DETAILS"
                        file={vehicleData.insurance_doc}
                        fileType="insurance_doc"
                        isEditMode={isEditMode}
                        handleFileChange={handleFileChange}
                        tempFiles={tempFiles}
                        tempFilePreviews={tempFilePreviews}
                        expiryDate={editableData.insurance_expiry_date}
                        handleExpiryChange={(e) => handleInputChange({
                          target: { name: 'insurance_expiry_date', value: e.target.value }
                        })}
                        onViewFile={handleViewFile}
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Footer Buttons */}
              <div className="flex flex-col items-center gap-3 mt-6 pt-4 border-t no-scrollbar">
                {isEditMode ? (
                  <>
                    <button
                      type="button"
                      onClick={handleConfirmUpdate}
                      className="w-full max-w-xs px-6 py-2 rounded-md text-white bg-green-600 hover:bg-green-700 text-sm font-medium"
                    >
                      Confirm Update
                    </button>
                    <button
                      type="button"
                      onClick={handleCancelUpdate}
                      className="w-full max-w-xs px-6 py-2 rounded-md text-white bg-gray-500 hover:bg-gray-600 text-sm font-medium"
                    >
                      Cancel
                    </button>
                  </>
                ) : (
                  <>
                    {editableData.driver ? (
                      <button
                        type="button"
                        onClick={() => setIsUnlinkConfirmOpen(true)}
                        className="w-full max-w-xs px-6 py-2 rounded-md text-white bg-gray-500 hover:bg-red-600 text-sm font-medium"
                      >
                        Unlink Driver
                      </button>
                    ) : (
                      <button
                        type="button"
                        onClick={() => setIsLinkDriverDialogOpen(true)}
                        className="w-full max-w-xs px-6 py-2 rounded-md text-white bg-gray-500 hover:bg-gray-600 text-sm font-medium"
                      >
                        Link Driver
                      </button>
                    )}
                    <button
                      type="button"
                      onClick={() => setIsEditMode(true)}
                      className="w-full max-w-xs px-6 py-2 rounded-md text-white bg-orange-500 hover:bg-orange-600 text-sm font-medium"
                    >
                      Update
                    </button>

                  </>
                )}
              </div>

              {isVehicleUpdating && (
                <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-70 z-50">
                  <div className="flex flex-col items-center">
                    <svg className="animate-spin h-8 w-8 text-orange-500 mb-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path>
                    </svg>
                    <span className="text-orange-600 font-semibold">Updating vehicle details...</span>
                  </div>
                </div>
              )}

              {isDialogLoading && (
                <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-70 z-50">
                  <div className="flex flex-col items-center">
                    <svg className="animate-spin h-8 w-8 text-orange-500 mb-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path>
                    </svg>
                    <span className="text-orange-600 font-semibold">Refreshing...</span>
                  </div>
                </div>
              )}
            </div>
          </Dialog.Panel>
        </div>
      </Dialog>

      {/* Link Driver Dialog */}
      <Dialog
        open={isLinkDriverDialogOpen}
        onClose={() => setIsLinkDriverDialogOpen(false)}
        className="relative z-[1000] no-scrollbar"
      >
        <div className="fixed inset-0 bg-black/40" aria-hidden="true" />
        <div className="fixed inset-0 flex items-center justify-center p-4 overflow-y-auto">
          <Dialog.Panel className="mx-auto w-full max-w-md rounded-lg bg-white p-6 shadow-xl">
            <div className="relative mb-4">
              <Dialog.Title className="text-center text-xl font-bold text-gray-900">
                Link Driver
              </Dialog.Title>
              <button
                onClick={() => setIsLinkDriverDialogOpen(false)}
                className="absolute top-0 right-0 text-gray-400 hover:text-gray-500 p-1"
              >
                <span className="text-2xl font-bold leading-none">×</span>
              </button>
            </div>

            {/* Error Alert */}
            {errorMessage && (
              <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                {errorMessage}
              </div>
            )}

            {/* Search Input */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Search Driver
              </label>
              <input
                type="text"
                value={driverSearchQuery}
                onChange={(e) => setDriverSearchQuery(e.target.value)}
                placeholder="Search by name or ID number..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100"
              />
            </div>

            {/* Search Results */}
            <div className="mt-4 max-h-60 overflow-y-auto">
              {driverSearchQuery ? (
                filteredDrivers.length > 0 ? (
                  <div className="space-y-2">
                    {filteredDrivers.map((driver) => (
                      <div
                        key={driver.id}
                        className="flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg border border-gray-200"
                      >
                        <div>
                          <p className="font-medium">{`${driver.first_name} ${driver.last_name}`}</p>
                          <p className="text-sm text-gray-500">ID: {driver.id_number}</p>
                        </div>
                        <button
                          onClick={() => handleAssignDriver(driver.id)}
                          disabled={isAssigning}
                          className={`px-3 py-1 text-white rounded-md ${isAssigning
                              ? 'bg-gray-400 cursor-not-allowed'
                              : 'bg-orange-500 hover:bg-orange-600'
                            } text-sm font-medium`}
                        >
                          {isAssigning ? 'Linking...' : 'Link'}
                        </button>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-center text-gray-500 py-4">No drivers found</p>
                )
              ) : (
                <p className="text-center text-gray-500 py-4">
                  Type to search for available drivers
                </p>
              )}
            </div>
          </Dialog.Panel>
        </div>
      </Dialog>

      {/* Unlink Driver Confirmation Dialog */}
      <Dialog
        open={isUnlinkConfirmOpen}
        onClose={() => setIsUnlinkConfirmOpen(false)}
        className="relative z-[1000] no-scrollbar"
      >
        <div className="fixed inset-0 bg-black/40" aria-hidden="true" />
        <div className="fixed inset-0 flex items-center justify-center p-4">
          <Dialog.Panel className="mx-auto max-w-sm rounded-lg bg-white p-6 shadow-xl">
            <div className="text-center">
              <Dialog.Title className="text-sm font-semibold font-serif text-gray-900 mb-4">
                Are you sure you want to unlink this driver?
              </Dialog.Title>
              <div className="mt-6 flex justify-center gap-4">
                <button
                  type="button"
                  onClick={handleUnlinkDriver}
                  disabled={isUnlinking}
                  className="px-4 py-2 bg-red-500 text-white text-sm rounded-md font-serif hover:bg-red-600 disabled:bg-gray-400"
                >
                  {isUnlinking ? 'Unlinking...' : 'Yes, Unlink'}
                </button>
                <button
                  type="button"
                  onClick={() => setIsUnlinkConfirmOpen(false)}
                  className="px-4 py-2 bg-gray-500 text-white text-sm font-serif rounded-md hover:bg-gray-600"
                >
                  Cancel
                </button>
              </div>
            </div>
          </Dialog.Panel>
        </div>
      </Dialog>

      {/* Driver Details Dialog */}
      <Dialog
        open={isDriverDetailsDialogOpen}
        onClose={() => setIsDriverDetailsDialogOpen(false)}
        className="relative z-[1001] no-scrollbar"
      >
        <div className="fixed inset-0 bg-black/40" aria-hidden="true" />
        <div className="fixed inset-0 flex items-center justify-center p-4">
          <Dialog.Panel className="mx-auto w-full max-w-4xl rounded-lg bg-white p-6 shadow-xl">
            <div className="relative mb-6">
              <Dialog.Title className="text-center text-xl font-bold text-gray-900">
                Driver Details
              </Dialog.Title>
              <button
                onClick={() => setIsDriverDetailsDialogOpen(false)}
                className="absolute top-0 right-0 text-gray-400 hover:text-gray-500"
              >
                <span className="text-2xl font-bold leading-none">×</span>
              </button>
            </div>

            {selectedDriver && <DriverDetailsCard driver={selectedDriver} onViewFile={handleViewFile} />}
          </Dialog.Panel>
        </div>
      </Dialog>

      {/* File Viewer Modal */}
      <FileViewerModal
        isOpen={isFileViewerOpen}
        onClose={() => setIsFileViewerOpen(false)}
        fileUrl={currentFileUrl}
        fileName={currentFileName}
        fileType={currentFileType}
        vehicleInfo={{
          registrationNumber: editableData.registration_number,
          partnerName: vehicleData.partner?.name
        }}
      />
    </div>
  );
};

export default VehicleDetailsDialog;