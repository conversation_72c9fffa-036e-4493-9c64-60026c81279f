import React, { useState } from "react";
import PostExpenditure from "./PostExpenditure";

const PartnerVehicle = ({ 
  vehicle, 
  makeNameProp, 
  modelNameProp, 
  onRemoveRequest, 
  onShowDetails
}) => {
  const [isExpenditureOpen, setIsExpenditureOpen] = useState(false);
  const isLinked = vehicle.driver !== null && vehicle.driver !== undefined;
  const makeName = makeNameProp || "N/A";
  const modelName = modelNameProp || "N/A";

  const handleRemoveClick = (e) => {
    e.stopPropagation(); 
    if (onRemoveRequest) {
      onRemoveRequest(vehicle.id);
    }
  };

  const handleAddExpenditure = (e) => {
    e.stopPropagation();
    setIsExpenditureOpen(true);
  };

  // Handler for clicking the entire card
  const handleCardClick = () => {
    if (onShowDetails) {
      onShowDetails(vehicle.id);
    }
  };

  return (
    <>
      <div
        onClick={handleCardClick} 
        className="bg-gray-100 shadow-lg rounded-lg p-4 mb-4 relative flex justify-between items-start cursor-pointer hover:bg-gray-50 transition-colors duration-150"
        style={{
          background: "rgba(255, 255, 255, 0.8)",
          backdropFilter: "blur(5px)",
        }}
      >
    
        <div className="pr-16">
          <div className="flex items-center gap-2 text-sm font-semibold mb-2"> 
            <span
              className={`h-3 w-3 rounded-full ${
                isLinked ? "bg-green-500" : "bg-red-500" 
              }`}
            ></span>
            <span className={isLinked ? "text-green-600 italic" : "text-red-600 italic"}> 
              {isLinked ? "Linked to Driver" : "Not linked to a driver"}
            </span>
          </div>

          {/* Vehicle Details */}
          <h2 className="text-base font-serif sm:text-lg font-bold">
            Vehicle ID: <span className="text-orange-700 font-bold">{vehicle.registration_number || "N/A"}</span>
          </h2>
          <p className="text-sm text-left font-serif font-medium mt-2"><span className="font-semibold">MAKE :</span> {makeName}</p>
          <p className="text-sm  text-left font-serif font-medium mt-2"><span className="font-semibold">MODEL :</span> {modelName}</p>
        </div>

        <div className="absolute bottom-4 right-4 flex flex-col gap-2">
          <button
            onClick={handleAddExpenditure}
            className="text-orange-600 font-bold underline text-xs sm:text-sm hover:text-orange-700 z-10"
          >
            Add Expenditure
          </button>
          
          <button
            onClick={handleRemoveClick}
            className="text-red-600 font-bold underline text-xs sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed z-10" 
          >
            Remove Vehicle
          </button>
        </div>
      </div>

      <PostExpenditure
        isOpen={isExpenditureOpen}
        onClose={() => setIsExpenditureOpen(false)}
        vehicleNumber={vehicle.registration_number}
        vehicleId={vehicle.id}
      />
    </>
  );
};

export default PartnerVehicle;