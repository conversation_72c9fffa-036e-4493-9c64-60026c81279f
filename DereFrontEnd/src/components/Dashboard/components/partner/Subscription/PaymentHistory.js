import React, { useState, useEffect } from 'react';
import api from '../../../../../services/api';
import { BASE_URL } from '../../../../../services/config';

const PaymentHistory = ({ onBack }) => {
  const [payments, setPayments] = useState([]); 
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchPaymentHistory = async () => {
      try {
        const token = localStorage.getItem('token');
        if (!token) {
          setError('Authentication token not found');
          setLoading(false);
          return;
        }

        const response = await api.get(`${BASE_URL}api/subscriptions/payment-history/`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });
        
        console.log('Full response:', response); 
        
        if (response?.results) {
          setPayments(response.results);
        } else if (Array.isArray(response)) {
         
          setPayments(response);
        } else {
          console.warn('Unexpected response format:', response);
          setPayments([]);
        }
        
        setLoading(false);
      } catch (error) {
        console.error('Error fetching payment history:', error);
        setError('You currently have no payment history');
        setPayments([]);
        setLoading(false);
      }
    };

    fetchPaymentHistory();
  }, []);

  return (
    <div className="w-full min-h-screen bg-gray-100 px-4 py-6">
      <div className="max-w-[1200px] mx-auto font-serif">
        {/* Header Section */}
        <div className="flex items-center gap-4 mb-8">
          <button 
            onClick={onBack}
            className="flex items-center text-black hover:text-gray-700 transition-colors font-serif"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
          </button>
          <h1 className="text-2xl font-bold font-serif">Payment History</h1>
        </div>

        {/* Table Card */}
        <div className="bg-white rounded-xl shadow-lg overflow-hidden">
          {loading ? (
            <div className="p-8 text-center text-gray-500 font-serif">
              <div className="animate-spin inline-block w-6 h-6 border-2 border-current border-t-transparent rounded-full mb-2"></div>
              <p>Loading payment history...</p>
            </div>
          ) : error ? (
            <div className="p-8 text-center text-red-600 font-serif">
              <svg className="w-8 h-8 mx-auto mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p>{error}</p>
            </div>
          ) : payments.length === 0 ? (
            <div className="p-8 text-center text-gray-500 font-serif">
              <svg className="w-8 h-8 mx-auto mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
              </svg>
              <p>No payment history found</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 font-serif">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-8 py-4 text-middle text-xs font-bold text-gray-500 uppercase tracking-wider font-serif">
                      Date
                    </th>
                    <th scope="col" className="px-8 py-4 text-middle text-xs font-bold text-gray-500 uppercase tracking-wider font-serif">
                      Plan Type
                    </th>
                    <th scope="col" className="px-8 py-4 text-middle text-xs font-bold text-gray-500 uppercase tracking-wider font-serif">
                      Amount
                    </th>
                    <th scope="col" className="px-8 py-4 text-middle text-xs font-bold text-gray-500 uppercase tracking-wider font-serif">
                      Payment Method
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {payments.map((payment, index) => (
                    <tr key={index} className="hover:bg-gray-50 transition-colors font-serif">
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium font-serif">
                        {new Date(payment.payment_date).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'short',
                          day: 'numeric'
                        })}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap font-serif">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          payment.plan_type === 'Pro' ? 'bg-purple-100 text-purple-800' : 'bg-green-100 text-green-800'
                        }`}>
                          {payment.plan_type} Plan
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-serif">
                        KES {parseFloat(payment.amount).toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 font-serif">
                        {payment.payment_method}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PaymentHistory;