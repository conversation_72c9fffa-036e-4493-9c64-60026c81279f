import React, { useState, useEffect } from 'react';
import SetPaymentPlan from './SetPaymentPlan';
import api from '../../../../../services/api';
import { BASE_URL } from '../../../../../services/config';

const ManagePlan = ({ onBack }) => {
  const [showSetPaymentPlan, setShowSetPaymentPlan] = useState(false);
  const [planDetails, setPlanDetails] = useState({
    plan_type: '',
    vehicle_count: 0,
    status: '',
    amount: '0.00',
    billing_cycle: '',
    latest_payment: null,
    expiry_date: null,
    is_expired: false
  });
  const [isLoading, setIsLoading] = useState(false);
  const [showPopup, setShowPopup] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);

  useEffect(() => {
    const fetchPlanDetails = async () => {
      try {
        const token = localStorage.getItem('token');
        const response = await api.get(`${BASE_URL}api/subscriptions/subscription-history/`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        const latestSubscription = Array.isArray(response) ?
          response.sort((a, b) => new Date(b.created_at) - new Date(a.created_at))[0]
          : response;

        if (latestSubscription) {
          const isExpired = new Date(latestSubscription.expiry_date) < new Date();

          setPlanDetails({
            plan_type: latestSubscription.plan_type || 'No Plan',
            vehicle_count: latestSubscription.vehicle_count || 0,
            status: latestSubscription.status,
            amount: latestSubscription.amount,
            billing_cycle: latestSubscription.billing_cycle,
            latest_payment: latestSubscription.latest_payment,
            expiry_date: latestSubscription.expiry_date,
            is_expired: isExpired
          });
        }
      } catch (error) {
        console.error('Error fetching plan details:', error);
      }
    };

    if (!showSetPaymentPlan) {
      fetchPlanDetails();
    }
  }, [showSetPaymentPlan]);

  const getPlanStatusBadge = () => {
    if (planDetails.is_expired) {
      return 'bg-red-100 text-red-800';
    }
    if (planDetails.status === 'cancelled') {
      return 'bg-yellow-100 text-yellow-800';
    }
    return planDetails.latest_payment?.status === 'success' ?
      'bg-green-100 text-green-800' :
      'bg-yellow-100 text-yellow-800';
  };

  const getPlanStatusText = () => {
    if (planDetails.is_expired) {
      return 'EXPIRED';
    }
    if (planDetails.status === 'cancelled') {
      return 'CANCELLED - ACTIVE UNTIL EXPIRY';
    }
    return planDetails.latest_payment?.status === 'success' ? 'ACTIVE' : planDetails.status.toUpperCase();
  };

  const handleViewPlans = () => {
    setShowSetPaymentPlan(true);
  };

  const handleCancelClick = () => {
    setShowConfirmation(true);
  };

  const handleConfirmCancel = async () => {
    try {
      setIsLoading(true);
      const token = localStorage.getItem('token');
      await api.post(`${BASE_URL}api/subscriptions/cancel/`, {}, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      setShowConfirmation(false);
      setShowPopup(true);

      
      const fetchPlanDetails = async () => {
        try {
          const token = localStorage.getItem('token');
          const response = await api.get(`${BASE_URL}api/subscriptions/subscription-history/`, {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          });
  
          const latestSubscription = Array.isArray(response) ?
            response.sort((a, b) => new Date(b.created_at) - new Date(a.created_at))[0]
            : response;
  
          if (latestSubscription) {
            const isExpired = new Date(latestSubscription.expiry_date) < new Date();
  
            setPlanDetails({
              plan_type: latestSubscription.plan_type || 'No Plan',
              vehicle_count: latestSubscription.vehicle_count || 0,
              status: latestSubscription.status,
              amount: latestSubscription.amount,
              billing_cycle: latestSubscription.billing_cycle,
              latest_payment: latestSubscription.latest_payment,
              expiry_date: latestSubscription.expiry_date,
              is_expired: isExpired
            });
          }
        } catch (error) {
          console.error('Error fetching plan details:', error);
        }
      };
      fetchPlanDetails();

      setTimeout(() => {
        setShowPopup(false);
      }, 3000);

    } catch (error) {
      console.error('Error cancelling plan:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (showSetPaymentPlan) {
    return <SetPaymentPlan onBack={() => setShowSetPaymentPlan(false)} />;
  }

  return (
    <div className="w-full min-h-screen bg-gray-100 px-4 py-6 font-serif">
      {showPopup && (
        <div className="fixed top-26 font-serif left-1/2 transform -translate-x-1/2 bg-orange-600 text-white p-4 rounded shadow-md z-50 max-w-md text-center">
          <p className="text-sm">
            Your subscription has been cancelled. You can continue using all features until your billing cycle ends.
          </p>
        </div>
      )}

      {showConfirmation && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 font-serif">
            <div className="mb-6 text-center">
              <svg className="w-12 h-12 mx-auto text-orange-600 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <h3 className="text-lg font-bold text-gray-900 mb-2">Cancel {planDetails.plan_type} Plan?</h3>
              <p className="text-gray-600 text-sm">
                You will continue to have access to all features until your current billing cycle ends.
              </p>
            </div>
            <div className="flex flex-col gap-3">
              <button
                onClick={handleConfirmCancel}
                disabled={isLoading}
                className="w-full px-4 py-2 bg-orange-600 text-white rounded hover:bg-orange-700 font-medium disabled:opacity-50 font-serif"
              >
                {isLoading ? 'Cancelling...' : 'Yes, Cancel My Plan'}
              </button>
              <button
                onClick={() => setShowConfirmation(false)}
                className="w-full px-4 py-2 text-gray-600 hover:text-gray-700 font-medium font-serif"
              >
                No, Keep My Plan
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="max-w-[1200px] mx-auto space-y-6 font-serif">
        {/* Header with back button */}
        <div className="flex items-center gap-4 mb-8">
          <button
            onClick={onBack}
            className="flex items-center text-black hover:text-gray-700 font-serif"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M10 19l-7-7m0 0l7-7m-7 7h18"
              />
            </svg>
          </button>
          <h1 className="text-2xl font-bold font-serif">MANAGE PLAN</h1>
        </div>

        {/* Current Plan Card */}
        <div className="bg-white rounded-xl shadow-sm p-6 font-serif">
          <h2 className="text-sm font-bold text-gray-700 mb-4 font-serif">YOUR PLAN</h2>
          <div className="space-y-4">
            <div className="flex flex-col md:flex-row md:justify-between md:items-center">
              <div>
                <div className="flex items-center gap-3">
                  <p className="text-lg text-left font-semibold font-serif">
                    {planDetails.plan_type.toUpperCase()} PLAN
                  </p>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium font-serif ${getPlanStatusBadge()}`}>
                    {getPlanStatusText()}
                  </span>
                </div>
                <p className="text-sm text-gray-600 mt-1 font-serif text-left">
                  NO. OF VEHICLES: {planDetails.vehicle_count}
                </p>
                {planDetails.amount && (
                  <p className="text-sm text-gray-600 mt-1 font-serif text-left">
                    AMOUNT: KSH {parseFloat(planDetails.amount).toFixed(2)} {planDetails.billing_cycle || 'pm'}
                  </p>
                )}
                {planDetails.expiry_date && (
                  <p className="text-sm text-gray-600 mt-1 font-serif text-left">
                    VALID UNTIL: {new Date(planDetails.expiry_date).toLocaleDateString()}
                  </p>
                )}
              </div>

              {/* Button stays inline on md+, moves below on small screens */}
              <div className="mt-3 md:mt-0 md:ml-4">
                <button
                  onClick={handleViewPlans}
                  className="text-orange-600 hover:text-orange-700 text-left font-semibold font-serif"
                >
                  View Plans
                </button>
              </div>
            </div>

          </div>
        </div>

        {/* Only show Cancel Plan Card if plan is active and not cancelled */}
        {(planDetails.status === 'active' && !planDetails.is_expired) && (
          <div className="bg-white rounded-xl shadow-sm p-6 font-serif">
            <h2 className="text-xl font-bold mb-2 font-serif">CANCEL YOUR CURRENT PLAN</h2>
            <p className="text-gray-600 mb-4 font-serif">
              You can still access your plan until your current billing cycle ends
            </p>
            <button
              onClick={handleCancelClick}
              className="text-orange-600 hover:text-orange-700 font-semibold font-serif"
            >
              Cancel Plan
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default ManagePlan;