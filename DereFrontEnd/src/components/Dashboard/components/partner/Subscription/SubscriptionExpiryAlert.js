import React, { useState, useEffect } from 'react';
import api from '../../../../../services/api';
import { BASE_URL } from '../../../../../services/config';

const SubscriptionExpiryAlert = () => {
  const [timeRemaining, setTimeRemaining] = useState(null);
  const [planType, setPlanType] = useState('');
  const [showAlert, setShowAlert] = useState(false);

  const getTimeRemaining = (expiryDate) => {
    const now = new Date();
    const expiry = new Date(expiryDate);
    const diffTime = expiry - now;
    
    const days = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diffTime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diffTime % (1000 * 60 * 60)) / (1000 * 60));

    return { days, hours, minutes, total: diffTime };
  };

  useEffect(() => {
    const fetchSubscriptionStatus = async () => {
      try {
        const token = localStorage.getItem('token');
        const response = await api.get(`${BASE_URL}api/subscriptions/subscription-history/`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        // Get the latest subscription
        const latestSubscription = Array.isArray(response) ? 
          response.sort((a, b) => new Date(b.created_at) - new Date(a.created_at))[0] 
          : response;

        if (latestSubscription && latestSubscription.expiry_date) {
          const time = getTimeRemaining(latestSubscription.expiry_date);
          
          // Show alert for last 3 days and active subscriptions
          if (time.total > 0 && 
              time.days <= 3 && 
              (latestSubscription.status === 'active' || 
               (latestSubscription.status === 'cancelled' && !time.total < 0))) {
            setTimeRemaining(time);
            setPlanType(latestSubscription.plan_type);
            setShowAlert(true);
          }
        }
      } catch (error) {
        console.error('Error fetching subscription status:', error);
      }
    };

    fetchSubscriptionStatus();
    // Check every minute
    const interval = setInterval(fetchSubscriptionStatus, 60 * 1000);
    return () => clearInterval(interval);
  }, []);

  if (!showAlert || !timeRemaining) return null;

  const getAlertStyle = () => {
    if (timeRemaining.days === 0 && timeRemaining.hours < 12) return 'bg-red-50 text-red-700 border-red-200';
    if (timeRemaining.days === 1) return 'bg-yellow-50 text-yellow-700 border-yellow-200';
    return 'bg-orange-50 text-orange-700 border-orange-200'; 
  };

  const getTimeDisplay = () => {
    if (timeRemaining.days > 0) {
      return `${timeRemaining.days} day${timeRemaining.days !== 1 ? 's' : ''}`;
    }
    if (timeRemaining.hours > 0) {
      return `${timeRemaining.hours} hour${timeRemaining.hours !== 1 ? 's' : ''} and ${timeRemaining.minutes} minute${timeRemaining.minutes !== 1 ? 's' : ''}`;
    }
    return `${timeRemaining.minutes} minute${timeRemaining.minutes !== 1 ? 's' : ''}`;
  };

  return (
    <div className={`fixed top-20 left-1/2 transform -translate-x-1/2 w-full max-w-sm z-50 font-serif
      ${getAlertStyle()} rounded-md shadow-sm p-4 mx-4 border animate-pulse`}
    >
      <div className="flex items-center space-x-3">
        <div className="flex-shrink-0">
          <svg 
            className="w-5 h-5" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" 
            />
          </svg>
        </div>
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium">
            Your {planType} Plan expires in {getTimeDisplay()}
          </p>
        </div>
        <button 
          onClick={() => setShowAlert(false)}
          className="flex-shrink-0 text-sm hover:text-gray-700 transition-colors"
          aria-label="Close"
        >
          ×
        </button>
      </div>
    </div>
  );
};

export default SubscriptionExpiryAlert;