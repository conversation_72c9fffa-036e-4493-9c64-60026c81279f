import React, { useState, useEffect } from "react";
import api from '../../../../../services/api';
import { BASE_URL } from '../../../../../services/config';


const MakePayment = ({ onClose, planType = "Standard", onPaymentSuccess }) => {
  const [paymentMethod, setPaymentMethod] = useState("MPESA");
  const [phoneNumber, setPhoneNumber] = useState("");
  const [paymentOption, setPaymentOption] = useState("Monthly");
  const [vehicles, setVehicles] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [showPopup, setShowPopup] = useState(false);
  const [errorPopup, setErrorPopup] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [error, setError] = useState(null);
  const [profile, setProfile] = useState(null);

  useEffect(() => {
    const fetchProfile = async () => {
      try {
        const token = localStorage.getItem('token');
        if (!token) return;

        const response = await api.get(`${BASE_URL}api/users/me/`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });
        setProfile(response);
      } catch (error) {
        console.error('Error fetching profile:', error);
        setError('Failed to load profile data');
      }
    };

    const fetchVehicles = async () => {
      try {
        const token = localStorage.getItem('token');
        if (!token) return;
        
        const response = await api.get(`${BASE_URL}api/vehicles/`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });
        setVehicles(response?.length || 0);
      } catch (error) {
        console.error('Error fetching vehicles:', error);
        setError('Failed to load vehicles data');
      }
    };

    fetchProfile();
    fetchVehicles();
  }, []);

 
  const basePrice = planType === "Pro" ? 1800 : 1000;

  const getPeriodAmount = (option) => {
    switch (option) {
      case "Quarterly":
        return basePrice * 3 * 0.9; // 3 months with 10% discount
      case "Yearly": 
        return basePrice * 12 * 0.8; // 12 months with 20% discount
      default:
        return basePrice; // Monthly amount
    }
  };

  const handlePromptPayment = async () => {
    try {
      setIsLoading(true);
      setError(null);
      setErrorPopup(false);
      const token = localStorage.getItem('token');

      // Validate phone number format
      let formattedPhone = phoneNumber;
      if (phoneNumber.startsWith('+')) {
        formattedPhone = phoneNumber.substring(1);
      }
      if (!formattedPhone.startsWith('254')) {
        if (formattedPhone.startsWith('0')) {
          formattedPhone = '254' + formattedPhone.substring(1);
        } else if (formattedPhone.startsWith('7') || formattedPhone.startsWith('1')) {
          formattedPhone = '254' + formattedPhone;
        } else {
          throw new Error('Phone number must start with 254, 0, 7, or 1');
        }
      }

      const requestBody = {
        partner: profile.partner_details.id,
        plan_type: planType === "Pro" ? "Pro" : "Standard",
        vehicle_count: vehicles || 1, 
        billing_cycle: paymentOption,
        phone_number: formattedPhone 
      };

      console.log('Request body:', requestBody); 

      const response = await api.post(
        `${BASE_URL}api/subscriptions/activate/`,
        requestBody,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (response) {
        setShowPopup(true);
        setTimeout(() => {
          setShowPopup(false);
          onClose();
          if (onPaymentSuccess) {
            onPaymentSuccess();
          }
        }, 3000);
      }
    } catch (error) {
      console.error('Error activating subscription:', error);
      let message;
      
      if (error.response?.data?.non_field_errors?.[0]?.includes("active subscription")) {
        message = "You have an active subscription.";
      } else {
        message = 
          error.response?.data?.phone_number?.[0] ||
          error.response?.data?.plan_type?.[0] ||
          error.response?.data?.partner?.[0] ||
          error.response?.data?.non_field_errors?.[0] ||
          'Payment Failed. Try again or contact Support';
      }
      
      setErrorMessage(message);
      setErrorPopup(true);
      setTimeout(() => {
        setErrorPopup(false);
      }, 3000);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/60 p-4">
      {showPopup && (
        <div className="fixed top-26 font-serif left-1/2 transform -translate-x-1/2 bg-green-500 text-white p-4 rounded shadow-md z-50">
          Payment Has been completed successfully
        </div>
      )}

      {errorPopup && (
        <div className="fixed top-26 font-serif left-1/2 transform -translate-x-1/2 bg-red-500 text-white p-4 rounded shadow-md z-50">
          {errorMessage}
        </div>
      )}

      {/* Modal card */}
      <div className="relative w-full max-w-[480px] rounded-2xl bg-white px-8 py-10 shadow-2xl">
        {/* Close button */}
        <button
          onClick={onClose}
          className="absolute right-4 top-4 text-2xl font-semibold text-gray-500 transition-colors hover:text-gray-700"
          aria-label="Close"
        >
          ×
        </button>

        {/* Heading */}
        <h2 className="mb-1 text-center text-3xl font-extrabold">Make Payment</h2>
        <p className="mb-8 text-center text-sm text-gray-600">
          You'll receive a prompt on your phone once you prompt payment
        </p>

        <form
          onSubmit={(e) => {
            e.preventDefault();
            handlePromptPayment();
          }}
          className="space-y-7 text-sm"
        >
          {error && (
            <div className="text-red-600 text-sm mb-4">
              {error}
            </div>
          )}

          {/* 1. PAYMENT METHOD ------------------------------------------------*/}
          <section>
            <p className="mb-3 text-xs font-extrabold uppercase tracking-wide">
              1. Select payment method
            </p>
            <div className="flex items-center gap-6">
              {[
                { value: "MPESA", label: "MPESA" },
                { value: "AIRTEL", label: "Airtel Money", disabled: true  },
              ].map(({ value, label, disabled  }) => (
                <label key={value} className="flex items-center gap-2">
                  <input
                    type="radio"
                    name="paymentMethod"
                    value={value}
                    checked={paymentMethod === value}
                    onChange={(e) => setPaymentMethod(e.target.value)}
                    disabled={disabled}
                    className="h-4 w-4 accent-orange-600"
                  />
                  {label}
                </label>
              ))}
            </div>
          </section>

          {/* 2. PHONE NUMBER --------------------------------------------------*/}
          <section>
            <p className="mb-3 text-xs font-extrabold uppercase tracking-wide">
              2. Phone number
            </p>
            <input
              type="tel"
              required
              value={phoneNumber}
              onChange={(e) => setPhoneNumber(e.target.value)}
              placeholder="+2547••••••••"
              className="w-full border-b-2 border-black pb-1 text-base placeholder:text-gray-400 focus:outline-none"
            />
          </section>

          {/* 3. PAYMENT OPTIONS ----------------------------------------------*/}
          <section>
            <p className="mb-3 text-xs font-extrabold uppercase tracking-wide">
              3. Payment options
            </p>
            <div className="space-y-3">
              {[
                {
                  value: "Monthly",
                  label: `Monthly: KES ${getPeriodAmount("Monthly").toLocaleString()}/car`,
                },
                {
                  value: "Quarterly",
                  label: `Quarterly: KES ${getPeriodAmount("Quarterly").toLocaleString()}/car (10% off)`,
                },
                {
                  value: "Yearly", 
                  label: `Yearly: KES ${getPeriodAmount("Yearly").toLocaleString()}/car (20% off)`,
                },
              ].map(({ value, label }) => (
                <label key={value} className="flex items-center gap-2">
                  <input
                    type="radio"
                    name="paymentOption"
                    value={value}
                    checked={paymentOption === value}
                    onChange={(e) => setPaymentOption(e.target.value)}
                    className="h-4 w-4 accent-orange-600"
                  />
                  {label}
                </label>
              ))}
            </div>
          </section>

          {/* NUMBER OF VEHICLES ---------------------------------------------*/}
          <section>
            <p className="mb-3 text-xs font-extrabold uppercase tracking-wide">
              Number of vehicles:
            </p>
            <div className="flex items-center gap-6">
              <div className="h-12 w-20 rounded border border-gray-300 p-2 text-center text-base bg-gray-50 flex items-center justify-center font-serif">
                {vehicles}
              </div>
              <p className="text-sm font-bold font-serif">
                Total: KES {Math.round((vehicles || 1) * getPeriodAmount(paymentOption)).toLocaleString()}
              </p>
            </div>
          </section>

          {/* CTA -------------------------------------------------------------*/}
          <button
            type="submit"
            disabled={isLoading}
            className="w-full rounded-md bg-orange-600 py-3 text-center text-base font-extrabold uppercase text-white transition hover:bg-orange-700 disabled:opacity-50 font-serif"
          >
            {isLoading ? 'Processing...' : 'Prompt Payment'}
          </button>
        </form>
      </div>
    </div>
  );
};

export default MakePayment;
