import React, { useState, useEffect } from 'react';
import api from '../../../../../services/api';
import { BASE_URL } from '../../../../../services/config';
import { useNavigate } from 'react-router-dom';

const WelcomePopUp = ({ isOpen, onClose, setShowPaymentPlans, partnerId }) => {
  const navigate = useNavigate();
  const [isActivating, setIsActivating] = useState(false);
  const [error, setError] = useState(null);
  const [showPopup, setShowPopup] = useState(false);
  const [vehicles, setVehicles] = useState([]);
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  // Only fetch vehicles when needed
  useEffect(() => {
    const fetchVehicles = async() => {
      try {
        const token = localStorage.getItem('token');
        if(!token) return;
        const response = await api.get(`${BASE_URL}api/vehicles/`,{
          headers:{
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });
        setVehicles(response || []);
      } catch(error) {
        setVehicles([]);
      }
    };

    if (isOpen) {
      fetchVehicles();
    }
  }, [isOpen]);

  const handleActivate = async () => {
    setIsActivating(true);
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found');
      }
      
      const vehicleCount = vehicles.length;

      const response = await api.post(`${BASE_URL}api/subscriptions/activate/`, 
        {
          partner: partnerId,
          plan_type: 'Free',
          vehicle_count: vehicleCount
        },
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );
      
      if (response) {
        setShowPopup(true);
        setTimeout(() => {
          setShowPopup(false);
          setShowPaymentPlans(true);
          onClose();
        }, 3000);
      }
    } catch (error) {
      setError(error.response?.data?.message || 'Failed to activate subscription');
    } finally {
      setIsActivating(false);
    }
  };

  const handleLogout = () => {
    setIsLoggingOut(true);
    setTimeout(() => {
      // Clear all stored data
      localStorage.removeItem('token');
      localStorage.removeItem('profile');
      localStorage.removeItem('userData');
      // Close popup and navigate
      onClose();
      navigate('/login');
      setIsLoggingOut(false);
    }, 3000);
  };

  if (!isOpen) return null;

  return (
    <>
      {showPopup && (
        <div className="fixed top-26 font-serif left-1/2 transform -translate-x-1/2 bg-green-500 text-white p-4 rounded shadow-md z-50">
          Free plan activated successfully
        </div>
      )}
      
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-40">
        <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 font-serif">
          {error && (
            <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded font-serif">
              {error}
            </div>
          )}
          
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-4 font-serif">Welcome to KaDereConnect!</h2>
            
            <p className="text-gray-600 mb-4 font-serif">
              To get started, you can activate our <strong className="font-serif">Free Plan</strong> and explore all features — or come back later.
            </p>

            <div className="flex flex-col gap-4 justify-center mt-6">
              <button
                onClick={handleActivate}
                className="px-6 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 transition-colors disabled:opacity-50 font-serif"
                disabled={isActivating}
              >
                {isActivating ? 'ACTIVATING...' : 'ACTIVATE FREE PLAN'}
              </button>

              <button
                onClick={handleLogout}
                disabled={isLoggingOut}
                className="px-6 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors font-serif disabled:opacity-50"
              >
                {isLoggingOut ? 'LOGGING OUT...' : 'MAYBE LATER'}
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default WelcomePopUp;