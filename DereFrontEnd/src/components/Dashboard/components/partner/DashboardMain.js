import React, { useEffect, useState } from 'react';
import {
  <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer
} from 'recharts';
import api from "../../../../services/api";

const colors = [
  '#3887d6', '#82ca9d', '#ffc658', '#ff8042',
  '#8dd1e1', '#a4de6c', '#d0ed57', '#8884d8',
  '#d88884', '#84d8c6', '#c684d8', '#d6a534',
  '#53a5d6', '#f07c7c', '#f0bf7c'
];


const CustomTooltip = ({ active, payload, label }) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload;
    const vehicleProfits = Object.keys(data)
      .filter(key => key !== 'month' && key !== 'totalProfit')
      .map((key, idx) => ({
        name: key,
        value: data[key],
        color: colors[idx % colors.length]
      }));

    return (
      <div className="bg-white p-3 shadow-lg rounded-md border border-gray-200">
        <p className="font-bold text-gray-800">{label}</p>
        <p className="text-sm text-indigo-600 font-semibold">
          Total Profit: KSH {payload[0].value.toLocaleString()}
        </p>
        <div className="mt-2">
          <p className="text-xs font-semibold text-gray-600">Vehicle Breakdown:</p>
          <ul className="list-disc list-inside text-sm">
            {vehicleProfits.map(v => (
              <li key={v.name} style={{ color: v.color }}>
                {v.name}: KSH {v.value.toLocaleString()}
              </li>
            ))}
          </ul>
        </div>
      </div>
    );
  }
  return null;
};
const DashboardMain = () => {
  const [totalVehicles, setTotalVehicles] = useState(0);
  const [expectedRevenue, setExpectedRevenue] = useState(0);
  const [actualRevenue, setActualRevenue] = useState(0);
  const [completionRate, setCompletionRate] = useState('0%');
  const [monthlyProfitData, setMonthlyProfitData] = useState([]);
  const [currentMonth, setCurrentMonth] = useState('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);

      // Fetch dashboard summary and monthly trends in parallel
      const [summaryResponse, trendsResponse] = await Promise.all([
        api.get('api/dashboard/summary/'),
        api.get('api/dashboard/monthly-profit-trends/')
      ]);

      // Set summary data
      setTotalVehicles(summaryResponse.total_vehicles);
      setExpectedRevenue(summaryResponse.expected_revenue);
      setActualRevenue(summaryResponse.actual_revenue);
      setCompletionRate(summaryResponse.completion_rate);
      setCurrentMonth(summaryResponse.current_month);

      // Set trends data
      setMonthlyProfitData(trendsResponse.monthly_profit_data);

    } catch (error) {
      console.error('Dashboard data fetch error:', error);
      // Set default values on error
      setTotalVehicles(0);
      setExpectedRevenue(0);
      setActualRevenue(0);
      setCompletionRate('0%');
      setMonthlyProfitData([]);
    } finally {
      setLoading(false);
    }
  };


  // Old fetch functions removed - now using optimized dashboard endpoints
  // Old fetchMonthlyProfit function removed - now using optimized endpoint

  if (loading) {
    return (
      <div className="bg-white bg-opacity-50 rounded-xl shadow-lg p-4 sm:p-6 m-2 sm:m-4 w-full sm:w-[90%] font-serif">
        <div className="flex justify-center items-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading dashboard data...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white bg-opacity-50 rounded-xl shadow-lg p-4 sm:p-6 m-2 sm:m-4 w-full sm:w-[90%] font-serif">

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        {[
          { label:'TOTAL VEHICLES', value: totalVehicles },
          { label:`EXPECTED REVENUE (${currentMonth})`, value:`KSH ${parseFloat(expectedRevenue).toLocaleString()}` },
          { label:`ACTUAL REVENUE (${currentMonth})`,   value:`KSH ${parseFloat(actualRevenue).toLocaleString()}` },
          { label:`PAYMENT COMPLETION RATE (${currentMonth})`, value: completionRate }
        ].map(c => (
          <div key={c.label} className="bg-white rounded-lg p-4 shadow">
            <p className="text-center text-xs uppercase font-semibold text-gray-600">{c.label}</p>
            <p className="text-center text-lg sm:text-xl font-bold text-orange-600">{c.value}</p>
          </div>
        ))}
      </div>

      <div className="bg-white rounded-lg p-4 shadow">
        <p className="text-xs uppercase font-bold text-gray-600 mb-4">
          Monthly Profit per Vehicle (Last 6 Months)
        </p>
        <div className="h-64 sm:h-80 overflow-x-auto">
          <div className="min-w-[500px] h-full">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={monthlyProfitData} margin={{ top:10, right:30, left:0, bottom:10 }}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" tick={{ fontSize:12 }} />
                <YAxis tick={{ fontSize:12 }} />
                <Tooltip content={<CustomTooltip />} />
                <Bar dataKey="totalProfit" fill="#3887d6" name="Total Profit" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardMain;
