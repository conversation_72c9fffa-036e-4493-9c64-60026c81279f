import { Dialog } from '@headlessui/react';
import { useState, useEffect } from 'react'; 
import axios from 'axios';
import { BASE_URL } from '../../../../services/config';

const SetPayments = ({ isOpen, onClose, vehicleNumber, vehicleId }) => {
  const [formData, setFormData] = useState({
    daily_amount: '',
    payment_days: {
      Monday: false,
      Tuesday: false,
      Wednesday: false,
      Thursday: false,
      Friday: false,
      Saturday: false,
      Sunday: false
    },
    deadline_time: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [existingSettingId, setExistingSettingId] = useState(null); 

  const handleDayToggle = (day) => {
    setFormData(prev => ({
      ...prev,
      payment_days: {
        ...prev.payment_days,
        [day]: !prev.payment_days[day]
      }
    }));
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      const token = localStorage.getItem("token");
      if (!token) {
        setError("Authentication token not found");
        return;
      }

      const selectedDays = Object.entries(formData.payment_days)
        .filter(([_, selected]) => selected)
        .map(([day]) => day)
        .join(',');

      const paymentData = {
        daily_amount: Number(formData.daily_amount),
        payment_days: selectedDays,
        deadline_time: formData.deadline_time
      };

      let response;
      
      // Use PATCH if updating existing settings, POST if creating new
      if (existingSettingId) {
        response = await axios.patch(
          `${BASE_URL}api/payment-settings/${existingSettingId}/`,
          paymentData,
          {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json',
            }
          }
        );
      } else {
        response = await axios.post(
          `${BASE_URL}api/payment-settings/`,
          paymentData,
          {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json',
            }
          }
        );
      }

      if (response.status === 200 || response.status === 201) {
        setSuccess(existingSettingId ? 'Payment settings have been successfully updated' : 'Payment settings have been successfully created');
        setTimeout(() => {
          onClose();
        }, 2000);
      }
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to set payments');
    } finally {
      setIsSubmitting(false);
    }
  };

  useEffect(() => {
    const fetchPaymentSettings = async () => {
      if (!isOpen) return;

      try {
        const token = localStorage.getItem("token");
        if (!token) {
          setError("Authentication token not found");
          return;
        }

        const response = await axios.get(`${BASE_URL}api/payment-settings/`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (response.status === 200 && response.data) {
          if (response.data.length > 0) {
            // Get the most recent payment setting
            const latestPaymentSetting = response.data[response.data.length - 1];
            
            setExistingSettingId(latestPaymentSetting.id);
            
            const paymentDaysArray = latestPaymentSetting.payment_days?.split(',') || [];

            setFormData({
              daily_amount: latestPaymentSetting.daily_amount?.toString() || '',
              payment_days: {
                Monday: paymentDaysArray.includes('Monday'),
                Tuesday: paymentDaysArray.includes('Tuesday'),
                Wednesday: paymentDaysArray.includes('Wednesday'),
                Thursday: paymentDaysArray.includes('Thursday'),
                Friday: paymentDaysArray.includes('Friday'),
                Saturday: paymentDaysArray.includes('Saturday'),
                Sunday: paymentDaysArray.includes('Sunday')
              },
              deadline_time: latestPaymentSetting.deadline_time || ''
            });
          } else {
            // No existing settings found
            setExistingSettingId(null);
            // Keep the default form state (empty form)
          }
        }
      } catch (err) {
        console.error('Error fetching payment settings:', err);
        setError(err.response?.data?.message || 'Failed to load existing payment settings');
        setExistingSettingId(null);
      }
    };

    if (isOpen) {
      fetchPaymentSettings();
    }
  }, [isOpen]);

  return (
    <>
      
      {(error || success) && (
        <div className="fixed top-5 left-1/2 transform -translate-x-1/2 z-[999]">
          {error && (
            <div className="bg-red-700 border border-red-700 text-white px-4 py-2 rounded shadow-md mb-2 animate-fade-in-out">
              {error}
            </div>
          )}
          {success && (
            <div className="bg-green-700 border border-green-700 text-white px-4 py-2 rounded shadow-md mb-2 animate-fade-in-out">
              {success}
            </div>
          )}
        </div>
      )}

      <Dialog
        open={isOpen}
        onClose={onClose}
        className="relative z-[990]"
      >
        <div className="fixed inset-0 bg-black/30" aria-hidden="true" />
        <div className="fixed inset-0 flex items-center justify-center p-4 overflow-y-auto">
          <Dialog.Panel className="mx-auto w-full max-w-md rounded-lg bg-white p-4 sm:p-6 shadow-xl">
            <div className="relative flex items-center justify-center mb-4">
              <Dialog.Title className="text-xl font-bold text-center">
                Set Payment Settings
              </Dialog.Title>
              <button
                onClick={onClose}
                className="absolute right-0 top-0 text-gray-500 hover:text-gray-700 p-2"
                aria-label="Close dialog"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <form onSubmit={handleSubmit} className="space-y-5">
              <div>
                <label className="block text-xs font-bold text-gray-700 uppercase mb-1">
                  Daily Amount (KSH):
                </label>
                <input
                  type="number"
                  name="daily_amount"
                  value={formData.daily_amount}
                  onChange={handleChange}
                  className="w-full px-4 py-2 rounded-full bg-gray-100 border border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-500"
                  required
                  min="0"
                  step="0.01"
                />
              </div>

              <div>
                <label className="block text-xs font-bold text-gray-700 uppercase mb-1">
                  Payment Days: <span className="text-red-500 text-[10px] italic">(Select days when payment is expected)</span>
                </label>
                <div className="grid grid-cols-2 sm:grid-cols-4 gap-3">
                  {Object.entries(formData.payment_days).map(([day, isSelected]) => (
                    <label
                      key={day}
                      className="flex items-center space-x-2 cursor-pointer"
                    >
                      <input
                        type="checkbox"
                        checked={isSelected}
                        onChange={() => handleDayToggle(day)}
                        className="w-4 h-4 rounded border-gray-300 text-black focus:ring-black checked:bg-black checked:border-black"
                      />
                      <span className="text-sm font-medium text-gray-700">
                        {day}
                      </span>
                    </label>
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-xs font-bold text-gray-700 uppercase mb-1">
                  Payment Deadline Time:
                </label>
                <div className="relative">
                  <input
                    type="time"
                    name="deadline_time"
                    value={formData.deadline_time}
                    onChange={handleChange}
                    className="w-full px-4 py-2 rounded-full bg-gray-100 border border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-500"
                    required
                  />
                  <span className="absolute right-4 top-2.5 text-gray-400 text-lg pointer-events-none">🕒</span>
                </div>
              </div>

              <button
                type="submit"
                className="w-full bg-orange-600 text-white py-2.5 rounded-full font-semibold hover:bg-orange-700 transition-colors disabled:opacity-50"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Saving...' : (formData.daily_amount ? 'Update Settings' : 'Set Settings')}
              </button>
            </form>
          </Dialog.Panel>
        </div>
      </Dialog>
    </>
  );
};

export default SetPayments;