# Database Setup

## Steps to Create a New Database in PostgreSQL

1. **Access the PostgreSQL Shell**:
   ```bash
   sudo -i -u postgres
   psql
   ```

2. **Create a New Database**:
   ```sql
   CREATE DATABASE new_database_name;
   ```
   Replace `new_database_name` with the desired name for your database.

3. **Grant Permissions to a User**:
   ```sql
   GRANT ALL PRIVILEGES ON DATABASE new_database_name TO postgres;
   ```

4. **Exit the PostgreSQL Shell**:
   ```sql
   \q
   ```

5. **Update Django Configuration**:
   Update the `DATABASES` section in your `settings.py` file:
   ```python
   DATABASES = {
       'default': {
           'ENGINE': 'django.db.backends.postgresql',
           'NAME': 'new_database_name',
           'USER': config('DB_USER'),
           'PASSWORD': config('DB_PASSWORD'),
           'HOST': config('DB_HOST'),
           'PORT': config('DB_PORT'),
       }
   }
   ```

6. **Run Migrations**:
   ```bash
   python manage.py migrate
   ```