# Document Expiry System - Complete Enhancement

## Overview
This document outlines the comprehensive enhancements made to the document expiry validation and notification system in the KadeReconnect backend application. The changes include improved validation logic, professional HTML email templates, and support for expired document notifications.

## Table of Contents
1. [Changes Made](#changes-made)
2. [API Endpoints](#api-endpoints)
3. [Email Notification System](#email-notification-system)
4. [Management Commands](#management-commands)
5. [Testing Guide](#testing-guide)
6. [Troubleshooting](#troubleshooting)

---

## Changes Made

### 1. Document Expiry Date Validation Updates

**Files Modified**: 
- `DereBackEnd/akuko_api/serializers.py`

**Change Summary**: Updated validation logic to allow expiry dates to be set for today or in the future, instead of requiring them to be strictly in the future.

#### Before:
```python
if value <= timezone.now().date():
    raise serializers.ValidationError(
        "License expiry date must be in the future."
    )
```

#### After:
```python
if value < timezone.now().date():
    raise serializers.ValidationError(
        "License expiry date cannot be in the past."
    )
```

#### Affected Document Types:
- **Driver Documents**:
  - Driving License (`license_expiry_date`)
  - PSV License (`psv_expiry_date`)
  - Good Conduct Certificate (`good_conduct_expiry_date`)

- **Vehicle Documents**:
  - Vehicle Registration (`vehicle_expiry_date`)
  - NTSA Inspection (`ntsa_expiry_date`)
  - Insurance (`insurance_expiry_date`)

### 2. Enhanced Email Template System

**Files Modified/Added**:
- `DereBackEnd/akuko_api/email_templates.py` (enhanced)
- `DereBackEnd/akuko_api/tasks.py` (updated notification functions)

**New Features**:
- Professional HTML email templates with KadeReconnect branding
- Responsive design with proper styling
- Urgency-based color coding and messaging
- Document-specific icons and formatting
- Call-to-action buttons linking to dashboards

### 3. Expired Document Notifications

**Enhancement**: Added support for notifying users about documents that have already expired.

**Change**: Updated notification periods from `[30, 7, 1]` to `[30, 7, 1, 0]` days before expiry.
- `0 days` = Documents that expired today

### 4. Immediate Notification Delivery

**Files Modified**:
- `DereBackEnd/akuko_api/tasks.py` (notification functions)
- `DereBackEnd/akuko_api/email_templates.py` (enhanced subscription templates)

**Changes**:
- **Document Expiry**: Notifications are now sent immediately when the system detects expiring/expired documents
- **Subscription Expiry**: Enhanced to support 7, 3, 1 days before expiry and when expired
- **Email Delivery**: Both document and subscription notifications use immediate email delivery instead of queued notifications

### 5. Smart Notification Logic

**Enhancement**: Implemented memory-based notification rules:
- Drivers with partners do not receive job notification emails
- This prevents duplicate notifications and reduces email spam

---

## API Endpoints

### Driver Registration/Update Endpoints

#### POST `/api/drivers/`
#### PUT/PATCH `/api/drivers/{id}/`

**Request Body**: No structural changes, but validation behavior updated.

**Example Request**:
```json
{
    "first_name": "John",
    "last_name": "Doe",
    "license_number": "DL123456",
    "license_expiry_date": "2024-07-23",  // ✅ Can now be today's date
    "psv_license_number": "PSV789",
    "psv_expiry_date": "2024-07-23",      // ✅ Can now be today's date
    "good_conduct_number": "GC456",
    "good_conduct_expiry_date": "2024-07-23"  // ✅ Can now be today's date
}
```

**Response Changes**:
- **Success**: Same response structure
- **Validation Error**: Updated error messages

**New Error Messages**:
```json
{
    "license_expiry_date": ["License expiry date cannot be in the past."],
    "psv_expiry_date": ["PSV license expiry date cannot be in the past."],
    "good_conduct_expiry_date": ["Good conduct certificate expiry date cannot be in the past."]
}
```

### Vehicle Registration/Update Endpoints

#### POST `/api/vehicles/`
#### PUT/PATCH `/api/vehicles/{id}/`

**Example Request**:
```json
{
    "registration_number": "KCA123A",
    "vehicle_expiry_date": "2024-07-23",    // ✅ Can now be today's date
    "ntsa_expiry_date": "2024-07-23",       // ✅ Can now be today's date
    "insurance_expiry_date": "2024-07-23"   // ✅ Can now be today's date
}
```

**New Error Messages**:
```json
{
    "vehicle_expiry_date": ["Vehicle registration expiry date cannot be in the past."],
    "ntsa_expiry_date": ["NTSA inspection expiry date cannot be in the past."],
    "insurance_expiry_date": ["Insurance expiry date cannot be in the past."]
}
```

---

## Email Notification System

### Enhanced HTML Email Templates

#### Template Features:
- **Responsive Design**: Works on desktop and mobile devices
- **KadeReconnect Branding**: Consistent with company identity
- **Urgency-Based Styling**: Color-coded based on expiry timeline
- **Interactive Elements**: Call-to-action buttons and links
- **Document Icons**: Visual indicators for different document types

#### Urgency Levels and Styling:

| Days Before Expiry | Urgency Level | Color | Icon | Header Background |
|-------------------|---------------|-------|------|-------------------|
| 30+ days | REMINDER | Blue (#17a2b8) | 📋 | Blue gradient |
| 7 days | IMPORTANT | Yellow (#ffc107) | 📅 | Yellow gradient |
| 1 day | URGENT | Orange (#fd7e14) | ⚠️ | Orange gradient |
| 0 days (expired) | EXPIRED | Red (#dc3545) | 🚨 | Red gradient |

### Document Expiry Notifications

#### Schedule
- **Daily Check Time**: 4:00 AM (04:00)
- **Frequency**: Once per day
- **Duplicate Prevention**: 24-hour cache prevents multiple notifications for same document/user
- **Warning Periods**: 7 days, 1 day before expiry, expiring today (0 days), and expired (-7 days)

### Subscription Expiry Notifications

#### Schedule
- **Delivery**: Immediate notification when subscriptions are detected as expiring/expired
- **Frequency**: Real-time checks through the notification service
- **Warning Periods**: 7 days, 3 days, 1 day before expiry, and expired (0 days)

#### Notification Recipients

**Driver Document Expiry**:
- **Primary**: Driver (via email)
- **Secondary**: Partner (if driver has a partner and partner doesn't have their own partner)

**Vehicle Document Expiry**:
- **Primary**: Partner (vehicle owner)
- **Secondary**: Driver (if vehicle is assigned to a driver and driver doesn't have a partner)

#### Document Type Icons:
- 🪪 Driving License
- 🚌 PSV License  
- 📜 Good Conduct Certificate
- 📋 Vehicle Registration
- 🔍 NTSA Inspection
- 🛡️ Insurance

### Duplicate Prevention
- Notifications are not sent if the same notification was already sent to the same recipient within the last 24 hours
- This prevents spam and ensures users don't receive multiple notifications for the same expiry event

---

## Management Commands

### Manual Document Expiry Check
```bash
python manage.py check_document_expiry
```

**Options**:
- `--force`: Force check regardless of scheduled time
- `--days 30 7 1 0`: Specify custom warning periods (default: 30, 7, 1, 0)

**Example Usage**:
```bash
# Run immediate check with default periods
python manage.py check_document_expiry --force

# Run with custom warning periods
python manage.py check_document_expiry --force --days 14 3 1 0

# Run only for expired documents
python manage.py check_document_expiry --force --days 0
```

### Notification Service
```bash
python manage.py run_notification_service
```

This command runs the continuous notification service that:
- Checks for document expiry daily at 4:00 AM with duplicate prevention
- Checks for subscription expiry with 7, 3, 1 day warnings plus expired notifications
- Processes pending notifications every 5 minutes
- Handles payment deadlines and job expiry
- Provides detailed logging and error handling
- Sends immediate email notifications with 24-hour duplicate prevention

---

## Testing Guide

### 1. Test Validation Changes

**Test Case 1**: Set expiry date to today ✅
```bash
curl -X POST http://localhost:8000/api/drivers/ \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "first_name": "Test",
    "last_name": "Driver",
    "license_expiry_date": "2024-07-23"
  }'
```
**Expected**: Should succeed (no validation error)

**Test Case 2**: Set expiry date to yesterday ❌
```bash
curl -X POST http://localhost:8000/api/drivers/ \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "first_name": "Test",
    "last_name": "Driver",
    "license_expiry_date": "2024-07-22"
  }'
```
**Expected**: Should fail with "License expiry date cannot be in the past."

### 2. Test Notification System

**Manual Trigger**:
```bash
python manage.py check_document_expiry --force
```

**Check Notification Records**:
```python
from akuko_api.models import Notification
notifications = Notification.objects.filter(
    subject__icontains="document"
).order_by('-created_at')

for n in notifications[:5]:
    print(f"{n.created_at}: {n.subject} -> {n.recipient}")
    print(f"Status: {n.status}")
    print("---")
```

**Test HTML Email Rendering**:
```python
from akuko_api.email_templates import get_driver_document_expiry_template
from akuko_api.models import Driver

# Get a test driver
driver = Driver.objects.first()
expiring_docs = ["Driving License (expires: 2024-07-24)"]

# Test different urgency levels
for days_before in [30, 7, 1, 0]:
    subject, html = get_driver_document_expiry_template(driver, expiring_docs, days_before)
    print(f"Days before: {days_before}")
    print(f"Subject: {subject}")
    print("---")
```

### 3. Test Email Templates

**Create Test Data**:
```python
# Create test driver with expiring documents
from akuko_api.models import Driver, User
from datetime import date, timedelta

user = User.objects.create(
    email="<EMAIL>",
    first_name="Test",
    last_name="Driver"
)

driver = Driver.objects.create(
    user=user,
    license_expiry_date=date.today() + timedelta(days=1),  # Expires tomorrow
    psv_expiry_date=date.today() + timedelta(days=7),      # Expires in 7 days
)
```

**Trigger Notifications**:
```bash
python manage.py check_document_expiry --force --days 1 7
```

---

## Email Content Examples

### Driver Document Expiry Email (Urgent - 1 Day)

**Subject**: `KadeReconnect - URGENT: Document Expiry Alert`

**HTML Content Structure**:
```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- Responsive email styles -->
</head>
<body>
    <!-- Header with orange gradient background -->
    <div class="header" style="background: linear-gradient(135deg, #fd7e14 0%, #e55a00 100%); color: white;">
        <h1>⚠️ URGENT: Document Alert</h1>
        <p>Action Required - KadeReconnect</p>
    </div>

    <!-- Personal greeting -->
    <p>Dear John Doe,</p>

    <!-- Urgency message -->
    <p>Your documents expire tomorrow and require immediate attention:</p>

    <!-- Document list with icons -->
    <div class="content-box" style="border-left: 4px solid #fd7e14;">
        <h3>📋 Documents Requiring Attention:</h3>
        <ul style="list-style: none;">
            <li>🪪 Driving License (expires: 2024-07-24)</li>
            <li>🚌 PSV License (expires: 2024-07-24)</li>
        </ul>
    </div>

    <!-- Instructions -->
    <div class="content-box">
        <h3>📝 How to Update Your Documents:</h3>
        <ol>
            <li>Log in to your KadeReconnect account</li>
            <li>Go to your profile section</li>
            <li>Upload the renewed documents</li>
            <li>Wait for verification confirmation</li>
        </ol>
    </div>

    <!-- Call to action button -->
    <div style="text-align: center; margin: 30px 0;">
        <a href="https://app.kadereconnect.com/driverdashboard"
           style="background-color: #fd7e14; color: white; padding: 12px 24px;
                  text-decoration: none; border-radius: 5px; display: inline-block;">
            Renew Today
        </a>
    </div>

    <!-- Warning box -->
    <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px;">
        <p><strong>⚠️ Important:</strong>
        Ensure your documents are renewed before expiry to avoid service interruptions.
        </p>
    </div>

    <!-- Support information -->
    <p>Need help? Contact <NAME_EMAIL></p>
</body>
</html>
```

### Vehicle Document Expiry Email (Expired - 0 Days)

**Subject**: `KadeReconnect - EXPIRED: Vehicle Document Alert`

**Key Features**:
- Red gradient header with 🚨 icon
- Vehicle registration number prominently displayed
- Immediate action required messaging
- Partner-specific instructions
- Legal compliance warnings

---

## Troubleshooting

### Common Issues

#### 1. Notifications not being sent
**Symptoms**: No notification records created or emails not delivered

**Debugging Steps**:
```bash
# Check if the notification service is running
ps aux | grep run_notification_service

# Check recent notification records
python manage.py shell -c "
from akuko_api.models import Notification
from django.utils import timezone
from datetime import timedelta

recent = timezone.now() - timedelta(hours=24)
notifications = Notification.objects.filter(created_at__gte=recent)
print(f'Notifications in last 24h: {notifications.count()}')
for n in notifications:
    print(f'{n.created_at}: {n.subject} -> {n.recipient} [{n.status}]')
"
```

#### 2. Validation errors on valid dates
**Symptoms**: API returns validation errors for today's date

**Debugging Steps**:
```python
from django.utils import timezone
print(f"Current date: {timezone.now().date()}")
print(f"Current timezone: {timezone.get_current_timezone()}")

# Test validation logic
test_date = timezone.now().date()
if test_date < timezone.now().date():
    print("Validation would fail")
else:
    print("Validation would pass")
```

#### 3. HTML emails not rendering properly
**Symptoms**: Emails appear as plain text or broken formatting

**Debugging Steps**:
```python
# Test template generation
from akuko_api.email_templates import get_driver_document_expiry_template
from akuko_api.models import Driver

driver = Driver.objects.first()
subject, html = get_driver_document_expiry_template(
    driver,
    ["Test Document (expires: 2024-07-24)"],
    1
)

# Save to file for inspection
with open('/tmp/test_email.html', 'w') as f:
    f.write(html)

print("HTML saved to /tmp/test_email.html")
```

### Support Commands

```bash
# Check service status
ps aux | grep run_notification_service

# View recent notifications
python manage.py shell -c "
from akuko_api.models import Notification
recent = Notification.objects.filter(
    created_at__date='2024-07-23'
).order_by('-created_at')[:10]
for n in recent:
    print(f'{n.created_at}: {n.subject} -> {n.recipient} [{n.status}]')
"

# Force notification check
python manage.py check_document_expiry --force

# Test specific urgency level
python manage.py check_document_expiry --force --days 1  # Only urgent (1 day)
python manage.py check_document_expiry --force --days 0  # Only expired
```

---

## Summary

## Key Improvements Summary

The enhanced notification system now provides:

### Document Expiry Notifications:
1. **Immediate Delivery**: Notifications sent instantly when expiring/expired documents are detected
2. **Professional HTML Templates**: Urgency-based styling with KadeReconnect branding
3. **Comprehensive Coverage**: 30, 7, 1 days before expiry + expired (0 days) notifications
4. **Flexible Validation**: Allows setting expiry dates for today or future dates

### Subscription Expiry Notifications:
1. **Enhanced Warning System**: 7, 3, 1 days before expiry + expired notifications
2. **Urgency-Based Templates**: Color-coded emails based on expiry timeline
3. **Immediate Delivery**: Real-time notifications without queuing delays
4. **Professional Styling**: Consistent with KadeReconnect brand standards

### System Features:
1. **Smart Delivery**: Prevents duplicate notifications with 24-hour cache and respects user relationships
2. **Scheduled Processing**: Daily document expiry checks at 4:00 AM to prevent spam
3. **Duplicate Prevention**: Cache-based system prevents multiple notifications for same document/user per day
4. **Easy Management**: Command-line tools for testing and maintenance
5. **Detailed Logging**: Comprehensive logging for monitoring and debugging
6. **Production Ready**: Improved user experience with professional communication standards

### Notification Delivery Changes:
- **Document Expiry**: Changed from queued to immediate email delivery
- **Subscription Expiry**: Enhanced from 3-day only to 7, 3, 1 day + expired notifications
- **Email Quality**: Professional HTML templates with urgency-based styling
- **User Experience**: Clear, actionable notifications with proper call-to-action buttons

## 📅 Notification Timeline

### Document Expiry Notifications:
- **30 days before**: Blue reminder - "REMINDER: Documents expire in 30 days"
- **7 days before**: Yellow important notice - "IMPORTANT: Documents expire in 1 week"
- **1 day before**: Orange urgent alert - "URGENT: Documents expire tomorrow"
- **Expiring today**: Red critical alert - "CRITICAL: Documents expire today"
- **Expired**: Red critical alert - "EXPIRED: Documents have expired"

### Subscription Expiry Notifications:
- **7 days before**: Blue reminder - "REMINDER: Subscription expires in 1 week"
- **3 days before**: Yellow important notice - "IMPORTANT: Subscription expires in 3 days"
- **1 day before**: Orange urgent alert - "URGENT: Subscription expires tomorrow"
- **Expired**: Red critical alert - "EXPIRED: Subscription has expired"
