#!/usr/bin/env python
"""
Test script for the Driver Rating System
This script tests the rating calculation functionality
"""

import os
import sys
import django
from datetime import datetime, timedelta, time
from decimal import Decimal

# Setup Django environment
sys.path.append('/home/<USER>/dere/DereBackEnd')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'akukoproject.settings')
django.setup()

from django.utils import timezone
from akuko_api.models import User, Partner, Driver, Vehicle, PaymentSettings, Revenue, DriverRating
from akuko_api.rating_utils import calculate_driver_rating, get_payment_deadline, calculate_payment_status


def create_test_data():
    """Create test data for rating system"""
    print("Creating test data...")
    
    # Create test user and partner
    partner_user = User.objects.create_user(
        email='<EMAIL>',
        password='testpass123',
        role='partner',
        first_name='Test',
        last_name='Partner'
    )
    
    partner = Partner.objects.create(
        user=partner_user,
        first_name='Test',
        last_name='Partner',
        email='<EMAIL>'
    )
    
    # Create test driver
    driver_user = User.objects.create_user(
        email='<EMAIL>',
        password='testpass123',
        role='driver',
        first_name='Test',
        last_name='Driver'
    )
    
    driver = Driver.objects.create(
        user=driver_user,
        first_name='Test',
        last_name='Driver',
        email='<EMAIL>',
        partner=partner,
        date_of_birth=datetime(1990, 1, 1).date()
    )
    
    # Create test vehicle
    vehicle = Vehicle.objects.create(
        partner=partner,
        driver=driver,
        registration_number='TEST123',
        status='active'
    )
    
    # Create payment settings
    payment_settings = PaymentSettings.objects.create(
        partner=partner,
        vehicle=vehicle,
        daily_amount=Decimal('1000.00'),
        payment_days='Monday,Wednesday,Friday',
        deadline_time=time(17, 0)  # 5:00 PM
    )
    
    print(f"Created test data:")
    print(f"- Partner: {partner}")
    print(f"- Driver: {driver}")
    print(f"- Vehicle: {vehicle}")
    print(f"- Payment Settings: {payment_settings}")
    
    return partner, driver, vehicle, payment_settings


def test_payment_deadline_calculation():
    """Test payment deadline calculation"""
    print("\n=== Testing Payment Deadline Calculation ===")
    
    partner, driver, vehicle, payment_settings = create_test_data()
    
    # Test Monday (payment day)
    monday = datetime(2024, 1, 1).date()  # This is a Monday
    deadline = get_payment_deadline(vehicle, monday)
    print(f"Monday deadline: {deadline}")
    
    # Test Tuesday (non-payment day)
    tuesday = datetime(2024, 1, 2).date()
    deadline = get_payment_deadline(vehicle, tuesday)
    print(f"Tuesday deadline: {deadline}")
    
    # Test Wednesday (payment day)
    wednesday = datetime(2024, 1, 3).date()
    deadline = get_payment_deadline(vehicle, wednesday)
    print(f"Wednesday deadline: {deadline}")


def test_rating_calculation():
    """Test rating calculation with different payment scenarios"""
    print("\n=== Testing Rating Calculation ===")
    
    partner, driver, vehicle, payment_settings = create_test_data()
    
    # Create test revenue records
    base_date = datetime(2024, 1, 1).date()  # Monday
    
    # Scenario 1: On-time payment (Monday)
    revenue1 = Revenue.objects.create(
        driver=driver,
        vehicle=vehicle,
        date=base_date,
        amount=Decimal('1000.00'),
        created_at=timezone.make_aware(datetime.combine(base_date, time(16, 0)))  # Before deadline
    )
    
    # Scenario 2: Late payment (Wednesday)
    revenue2 = Revenue.objects.create(
        driver=driver,
        vehicle=vehicle,
        date=base_date + timedelta(days=2),  # Wednesday
        amount=Decimal('1000.00'),
        created_at=timezone.make_aware(datetime.combine(base_date + timedelta(days=2), time(18, 0)))  # After deadline
    )
    
    # Scenario 3: No payment for Friday (will be handled by rating calculation)
    
    print(f"Created revenue records:")
    print(f"- Revenue 1 (Monday, on-time): {revenue1}")
    print(f"- Revenue 2 (Wednesday, late): {revenue2}")
    
    # Calculate rating
    rating = calculate_driver_rating(driver, vehicle, base_date + timedelta(days=4))  # Calculate up to Friday
    
    print(f"\nCalculated rating: {rating}")
    print(f"Rating score: {rating.rating_score}")
    print(f"Total points: {rating.total_points}")
    print(f"Total payment days: {rating.total_payment_days}")
    print(f"Is active: {rating.is_active}")
    
    # Test payment status calculation
    status1, points1 = calculate_payment_status(revenue1)
    status2, points2 = calculate_payment_status(revenue2)
    
    print(f"\nPayment status analysis:")
    print(f"Revenue 1: {status1} - {points1} points")
    print(f"Revenue 2: {status2} - {points2} points")


def test_consecutive_non_payment():
    """Test consecutive non-payment scenario"""
    print("\n=== Testing Consecutive Non-Payment ===")
    
    partner, driver, vehicle, payment_settings = create_test_data()
    
    # Create payment settings for daily payments to test consecutive non-payment
    payment_settings.payment_days = 'Monday,Tuesday,Wednesday,Thursday,Friday'
    payment_settings.save()
    
    base_date = datetime(2024, 1, 1).date()  # Monday
    
    # Make one payment on Monday
    Revenue.objects.create(
        driver=driver,
        vehicle=vehicle,
        date=base_date,
        amount=Decimal('1000.00'),
        created_at=timezone.make_aware(datetime.combine(base_date, time(16, 0)))
    )
    
    # Skip Tuesday, Wednesday, Thursday (3 consecutive non-payments)
    # Calculate rating after Thursday
    rating = calculate_driver_rating(driver, vehicle, base_date + timedelta(days=3))  # Thursday
    
    print(f"Rating after 3 consecutive non-payments:")
    print(f"Rating score: {rating.rating_score}")
    print(f"Is active: {rating.is_active}")
    print(f"Consecutive non-payment days: {rating.consecutive_non_payment_days}")


def cleanup_test_data():
    """Clean up test data"""
    print("\n=== Cleaning up test data ===")
    
    # Delete test data
    DriverRating.objects.filter(driver__email='<EMAIL>').delete()
    Revenue.objects.filter(driver__email='<EMAIL>').delete()
    PaymentSettings.objects.filter(partner__email='<EMAIL>').delete()
    Vehicle.objects.filter(registration_number='TEST123').delete()
    Driver.objects.filter(email='<EMAIL>').delete()
    Partner.objects.filter(email='<EMAIL>').delete()
    User.objects.filter(email__in=['<EMAIL>', '<EMAIL>']).delete()
    
    print("Test data cleaned up successfully!")


def main():
    """Run all tests"""
    print("Starting Driver Rating System Tests...")
    
    try:
        test_payment_deadline_calculation()
        test_rating_calculation()
        test_consecutive_non_payment()
        
        print("\n=== All Tests Completed Successfully! ===")
        
    except Exception as e:
        print(f"\nError during testing: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        cleanup_test_data()


if __name__ == '__main__':
    main()
