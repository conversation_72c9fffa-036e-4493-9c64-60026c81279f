# Setting Up Scheduled Notifications

This section provides instructions for setting up scheduled notifications for both local development and production environments.

## For Local Development

To run the notification service locally, follow these steps:

1. Open one terminal and start the Django development server:

   ```bash
   python manage.py runserver
   ```

2. Open another terminal and run the notification service:
   ```bash
   python manage.py run_notification_service
   ```

## For Production

In a production environment, you can use a cron job to schedule the notification service.

### Steps to Set Up a Cron Job

1. Open the crontab editor:

   ```bash
   crontab -e
   ```

2. Add the following line to schedule the notification service to run every 5 minutes:

   ```bash
   */5 * * * * cd path_to_project/DereBackEnd && path_to_project/DereBackEnd/venv/bin/python manage.py send_pending_notifications >> path_to_project/DereBackEnd/logs/notifications.log 2>&1
   ```

   - Replace `path_to_project/DereBackEnd` with the path to your project directory.
   - Ensure that the `logs` directory exists to store the log files.

### Create the Logs Directory

To ensure the logs directory exists, run the following command:

```bash
mkdir -p path_to_project/DereBackEnd/logs
```

This will create the necessary directory structure if it does not already exist.

By following these steps, you can successfully set up scheduled notifications for both development and production environments.
