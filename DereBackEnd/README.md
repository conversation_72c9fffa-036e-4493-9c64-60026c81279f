# KadereConnect DereBackEnd

This document provides instructions for setting up and running the KadereConnect DereBackEnd project on your local machine.

## Prerequisites

Before you begin, ensure you have the following installed:

* **Python:** Python 3.x is required.
* **PostgreSQL:** PostgreSQL is used as the database.
* **pip:** Python package installer.

## Installation and Setup

1.  **Clone the Repository:**

    ```bash
    git clone <repository_url>
    cd DereBackEnd
    ```

2.  **Install PostgreSQL:**

    * Update your system's package list:

        ```bash
        sudo apt update
        ```

    * Install PostgreSQL and its contrib package:

        ```bash
        sudo apt install postgresql postgresql-contrib
        ```

    * Start the PostgreSQL service:

        ```bash
        sudo systemctl start postgresql.service
        ```

3.  **Configure PostgreSQL:**

    * Access the PostgreSQL shell as the `postgres` user:

        ```bash
        sudo -u postgres psql
        ```

    * Create the database and user:

        ```sql
        CREATE DATABASE usersDB;
        CREATE USER sammy WITH PASSWORD 'pa$$word';
        GRANT ALL PRIVILEGES ON DATABASE usersDB TO sammy;
        ALTER ROLE sammy SET client_encoding TO 'utf8';
        \q
        ```

4.  **Install pip (if not already installed):**

    * On Debian/Ubuntu-based systems:

        ```bash
        sudo apt install python3-pip
        ```

    * On macOS (using Homebrew):

        ```bash
        brew install python3
        ```

    * On Windows, pip is usually included with Python installations. Ensure python is added to your path.

5.  **Create a Virtual Environment:**

    * Create a virtual environment to isolate project dependencies:

        ```bash
        python3 -m venv venv
        ```

    * Activate the virtual environment:

        ```bash
        source venv/bin/activate  # On Linux/macOS
        venv\Scripts\activate  # On Windows
        ```

6.  **Install Project Dependencies:**

    * Install the required Python packages from `requirements.txt`:

        ```bash
        pip install -r requirements.txt
        ```

7.  **Configure Database Settings:**

    * In the `DereBackEnd/settings.py` file, update the `DATABASES` setting with your PostgreSQL credentials:

        ```python
        DATABASES = {
            'default': {
                'ENGINE': 'django.db.backends.postgresql',
                'NAME': 'usersDB',
                'USER': 'sammy',
                'PASSWORD': 'pa$$word',
                'HOST': 'localhost',
                'PORT': '5432',
            }
        }
        ```

8.  **Make Migrations:**
    * Create migrations based on changes to your models:
        ```bash
        python manage.py makemigrations
        ```

9.  **Run Migrations:**

    * Apply database migrations to create the necessary tables:

        ```bash
        python manage.py migrate
        ```

10. **Run the Development Server:**

    * Start the Django development server:

        ```bash
        python manage.py runserver
        ```

    * You can now access the application at `http://127.0.0.1:8000/` in your web browser.

## Populating Data

* To populate initial data from the `akuko_api/fixtures/db.json` file, run:

    ```bash
    python manage.py loaddata akuko_api/fixtures/db.json
    ```

## Deleting Duplicate Entries

* To delete duplicate entries in the `vehiclemodel` table based on `name` and `make`, run:

    ```bash
    python manage.py delete_duplicates akuko_api.vehiclemodel name make
    ```

## Notes

* Ensure that PostgreSQL is running before attempting to run migrations or the development server.
* The provided PostgreSQL credentials (`usersDB`, `sammy`, `pa$$word`) are for development purposes. Consider using more secure credentials in a production environment.
* If you encounter any issues, check the Django documentation and PostgreSQL documentation for troubleshooting tips.
* Remember to deactivate the virtual environment when you're finished:

    ```bash
    deactivate
    ```
