#!/usr/bin/env python3
"""
Test script for the enhanced driver rating dashboard endpoints
"""

import requests
import json
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:8000/api"
DRIVER_EMAIL = "<EMAIL>"
DRIVER_PASSWORD = "password123"
PARTNER_EMAIL = "<EMAIL>"
PARTNER_PASSWORD = "password123"

def login_user(email, password):
    """Login and get access token"""
    response = requests.post(f"{BASE_URL}/auth/login/", {
        "email": email,
        "password": password
    })
    
    if response.status_code == 200:
        data = response.json()
        return data.get('access_token')
    else:
        print(f"Login failed: {response.status_code} - {response.text}")
        return None

def test_driver_rating_dashboard():
    """Test the enhanced driver rating dashboard"""
    print("=== Testing Driver Rating Dashboard ===")
    
    # Login as driver
    token = login_user(DRIVER_EMAIL, DRIVER_PASSWORD)
    if not token:
        print("Failed to login as driver")
        return
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # Get driver ID (assuming we can get it from profile or list)
    response = requests.get(f"{BASE_URL}/drivers/", headers=headers)
    if response.status_code != 200:
        print(f"Failed to get drivers: {response.status_code}")
        return
    
    drivers = response.json()
    if not drivers or len(drivers) == 0:
        print("No drivers found")
        return
    
    driver_id = drivers[0]['id']  # Use first driver
    
    # Test driver rating dashboard
    response = requests.get(f"{BASE_URL}/drivers/{driver_id}/rating/", headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        print("✅ Driver Rating Dashboard Response:")
        print(f"   Driver: {data.get('driver_name')} ({data.get('driver_code')})")
        print(f"   Current Rating: {data.get('current_rating')} ({data.get('rating_level')})")
        print(f"   Vehicle: {data.get('vehicle')}")
        print(f"   Age Group: {data.get('age_group')}")
        print(f"   Payment Days: {data.get('payment_days')}")
        print(f"   On-time Payments: {data.get('on_time_payments')}")
        print(f"   Late Payments: {data.get('late_payments')}")
        print(f"   Missed Payments: {data.get('missed_payments')}")
        print(f"   Success Rate: {data.get('success_rate')}%")
        print(f"   Total Points: {data.get('total_points')}")
        
        # Print recent payment history
        if data.get('recent_payment_history'):
            print("   Recent Payments:")
            for payment in data['recent_payment_history'][:3]:  # Show first 3
                print(f"     {payment['date']}: {payment['status']} ({payment['points']} points)")
        
        # Print rating trend
        if data.get('rating_trend'):
            print("   Rating Trend:")
            for week in data['rating_trend']:
                print(f"     {week['week']}: {week['rating']}")
                
    else:
        print(f"❌ Failed to get driver rating: {response.status_code} - {response.text}")

def test_partner_performance_report():
    """Test the partner performance report"""
    print("\n=== Testing Partner Performance Report ===")
    
    # Login as partner
    token = login_user(PARTNER_EMAIL, PARTNER_PASSWORD)
    if not token:
        print("Failed to login as partner")
        return
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # Test partner performance report
    response = requests.get(f"{BASE_URL}/drivers/performance_report/", headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        print("✅ Partner Performance Report Response:")
        print(f"   Partner: {data.get('partner_name')}")
        print(f"   Total Drivers: {data.get('total_drivers')}")
        print(f"   Active Drivers: {data.get('active_drivers')}")
        
        # Print driver performance summary
        if data.get('drivers'):
            print("   Driver Performance:")
            for driver in data['drivers'][:3]:  # Show first 3 drivers
                print(f"     {driver['driver_name']}: {driver['current_rating']} ({driver['rating_level']})")
                print(f"       Vehicle: {driver['vehicle']}, Age: {driver['age_group']}")
                print(f"       Payments: {driver['on_time_payments']} on-time, {driver['late_payments']} late, {driver['missed_payments']} missed")
                print(f"       Success Rate: {driver['success_rate']}%, Status: {driver['status']}")
                print()
                
    else:
        print(f"❌ Failed to get performance report: {response.status_code} - {response.text}")

def test_payment_history():
    """Test enhanced payment history endpoint with pagination and filtering"""
    print("\n=== Testing Enhanced Payment History ===")

    # Login as driver
    token = login_user(DRIVER_EMAIL, DRIVER_PASSWORD)
    if not token:
        print("Failed to login as driver")
        return

    headers = {"Authorization": f"Bearer {token}"}

    # Get driver ID
    response = requests.get(f"{BASE_URL}/drivers/", headers=headers)
    if response.status_code != 200:
        print(f"Failed to get drivers: {response.status_code}")
        return

    drivers = response.json()
    if not drivers or len(drivers) == 0:
        print("No drivers found")
        return

    driver_id = drivers[0]['id']

    # Test basic payment history with pagination
    params = {'page': 1, 'page_size': 10, 'days': 30}
    response = requests.get(f"{BASE_URL}/drivers/{driver_id}/payment_history/",
                          headers=headers, params=params)

    if response.status_code == 200:
        data = response.json()
        print("✅ Enhanced Payment History Response:")
        print(f"   Driver: {data.get('driver_name')} ({data.get('driver_code')})")
        print(f"   Total Points All Time: {data.get('total_points_all_time')}")

        # Pagination info
        pagination = data.get('pagination', {})
        print(f"   Pagination: Page {pagination.get('current_page')} of {pagination.get('total_pages')}")
        print(f"   Total Records: {pagination.get('total_records')}")

        # Summary with enhanced stats
        summary = data.get('summary', {})
        print(f"   Summary: {summary.get('total_payments')} payments, "
              f"{summary.get('success_rate')}% success rate")
        print(f"   Vehicles Used: {summary.get('vehicles_used', [])}")

        # Show recent payments with vehicle info
        if data.get('payment_history'):
            print("   Recent Payments:")
            for payment in data['payment_history'][:3]:
                vehicle = payment.get('vehicle', {})
                print(f"     {payment['formatted_date']}: {payment['status']} "
                      f"(${payment['amount']}, {payment['points']} pts) - {vehicle.get('registration', 'N/A')}")
    else:
        print(f"❌ Failed to get payment history: {response.status_code} - {response.text}")

    # Test advanced filtering
    print("\n   Testing Advanced Filtering:")
    filter_params = {
        'page': 1,
        'page_size': 5,
        'status': 'on_time',
        'min_amount': 1000,
        'start_date': '2024-01-01',
        'end_date': '2024-12-31'
    }

    response = requests.get(f"{BASE_URL}/drivers/{driver_id}/payment_history/",
                          headers=headers, params=filter_params)

    if response.status_code == 200:
        data = response.json()
        filters = data.get('filters_applied', {})
        print(f"   ✅ Filtered Results: {len(data.get('payment_history', []))} records")
        print(f"   Applied Filters: {filters}")
    else:
        print(f"   ❌ Failed to get filtered results: {response.status_code}")

def test_rating_trend():
    """Test rating trend endpoint"""
    print("\n=== Testing Rating Trend ===")

    # Login as driver
    token = login_user(DRIVER_EMAIL, DRIVER_PASSWORD)
    if not token:
        print("Failed to login as driver")
        return

    headers = {"Authorization": f"Bearer {token}"}

    # Get driver ID
    response = requests.get(f"{BASE_URL}/drivers/", headers=headers)
    if response.status_code != 200:
        print(f"Failed to get drivers: {response.status_code}")
        return

    drivers = response.json()
    if not drivers or len(drivers) == 0:
        print("No drivers found")
        return

    driver_id = drivers[0]['id']

    # Test rating trend
    params = {'weeks': 8, 'period': 'weekly'}
    response = requests.get(f"{BASE_URL}/drivers/{driver_id}/rating_trend/",
                          headers=headers, params=params)

    if response.status_code == 200:
        data = response.json()
        print("✅ Rating Trend Response:")
        print(f"   Driver: {data.get('driver_name')}")
        print(f"   Current Rating: {data.get('current_rating')}")
        print(f"   Trend Direction: {data.get('trend_direction')}")
        print(f"   Weeks Analyzed: {data.get('weeks_analyzed')}")

        # Show trend data
        if data.get('trend_data'):
            print("   Weekly Trend:")
            for week in data['trend_data'][-4:]:  # Show last 4 weeks
                print(f"     {week['week_label']}: {week['rating']} "
                      f"({week['payments_made']} payments)")
    else:
        print(f"❌ Failed to get rating trend: {response.status_code} - {response.text}")

def test_partner_payment_history_report():
    """Test partner payment history report"""
    print("\n=== Testing Partner Payment History Report ===")

    # Login as partner
    token = login_user(PARTNER_EMAIL, PARTNER_PASSWORD)
    if not token:
        print("Failed to login as partner")
        return

    headers = {"Authorization": f"Bearer {token}"}

    # Test payment history report
    params = {'days': 30, 'limit_per_driver': 5}
    response = requests.get(f"{BASE_URL}/drivers/payment_history_report/",
                          headers=headers, params=params)

    if response.status_code == 200:
        data = response.json()
        print("✅ Partner Payment History Report Response:")
        print(f"   Partner: {data.get('partner_name')}")
        print(f"   Period: {data.get('period', {}).get('days')} days")
        print(f"   Drivers with Payments: {data.get('summary', {}).get('total_drivers_with_payments')}")
        print(f"   Total Payments: {data.get('summary', {}).get('total_payments')}")
        print(f"   Overall Success Rate: {data.get('summary', {}).get('overall_success_rate')}%")

        # Show driver summaries
        if data.get('drivers'):
            print("   Driver Payment Summaries:")
            for driver in data['drivers'][:2]:  # Show first 2 drivers
                print(f"     {driver['driver_name']}: {driver['total_payments']} payments, "
                      f"{driver['success_rate']}% success rate")
    else:
        print(f"❌ Failed to get payment history report: {response.status_code} - {response.text}")

def test_with_filters():
    """Test performance report with filters"""
    print("\n=== Testing Performance Report with Filters ===")

    # Login as partner
    token = login_user(PARTNER_EMAIL, PARTNER_PASSWORD)
    if not token:
        print("Failed to login as partner")
        return

    headers = {"Authorization": f"Bearer {token}"}

    # Test with filters
    params = {
        'status': 'active',
        'age_group': '26_39'
    }

    response = requests.get(f"{BASE_URL}/drivers/performance_report/",
                          headers=headers, params=params)

    if response.status_code == 200:
        data = response.json()
        print("✅ Filtered Performance Report Response:")
        print(f"   Filters Applied: {data.get('filters_applied')}")
        print(f"   Matching Drivers: {data.get('total_drivers')}")
    else:
        print(f"❌ Failed to get filtered report: {response.status_code} - {response.text}")

def test_calculate_rating():
    """Test manual rating calculation endpoint"""
    print("\n=== Testing Manual Rating Calculation ===")

    # Login as partner (assuming partners can trigger calculations)
    token = login_user(PARTNER_EMAIL, PARTNER_PASSWORD)
    if not token:
        print("Failed to login as partner")
        return

    headers = {"Authorization": f"Bearer {token}"}

    # Get a driver ID
    response = requests.get(f"{BASE_URL}/drivers/", headers=headers)
    if response.status_code != 200:
        print(f"Failed to get drivers: {response.status_code}")
        return

    drivers = response.json()
    if not drivers or len(drivers) == 0:
        print("No drivers found")
        return

    driver_id = drivers[0]['id']

    # Test manual rating calculation
    calculation_data = {
        "recalculate_from_date": "2024-01-01"
    }

    response = requests.post(f"{BASE_URL}/drivers/{driver_id}/calculate_rating/",
                           headers=headers, json=calculation_data)

    if response.status_code == 200:
        data = response.json()
        print("✅ Rating Calculation Response:")
        print(f"   Driver: {data.get('driver_name')}")
        print(f"   Calculation Triggered: {data.get('calculation_triggered')}")
        print(f"   Overall Rating: {data.get('overall_rating')}")
        print(f"   Total Points All Time: {data.get('total_points_all_time')}")
        print(f"   Vehicles Processed: {data.get('vehicles_processed')}")

        # Show vehicle results
        if data.get('vehicle_results'):
            print("   Vehicle Results:")
            for result in data['vehicle_results']:
                print(f"     {result['vehicle_registration']}: Rating {result.get('current_rating', 'N/A')}, "
                      f"Points {result.get('total_points', 'N/A')}")
    else:
        print(f"❌ Failed to calculate rating: {response.status_code} - {response.text}")

def test_enhanced_dashboard():
    """Test enhanced driver dashboard with vehicle history"""
    print("\n=== Testing Enhanced Driver Dashboard ===")

    # Login as driver
    token = login_user(DRIVER_EMAIL, DRIVER_PASSWORD)
    if not token:
        print("Failed to login as driver")
        return

    headers = {"Authorization": f"Bearer {token}"}

    # Get driver ID
    response = requests.get(f"{BASE_URL}/drivers/", headers=headers)
    if response.status_code != 200:
        print(f"Failed to get drivers: {response.status_code}")
        return

    drivers = response.json()
    if not drivers or len(drivers) == 0:
        print("No drivers found")
        return

    driver_id = drivers[0]['id']

    # Test enhanced dashboard
    response = requests.get(f"{BASE_URL}/drivers/{driver_id}/rating/", headers=headers)

    if response.status_code == 200:
        data = response.json()
        print("✅ Enhanced Dashboard Response:")
        print(f"   Driver: {data.get('driver_name')} ({data.get('driver_code')})")
        print(f"   Current Rating: {data.get('current_rating')} ({data.get('rating_level')})")
        print(f"   Points (Period): {data.get('total_points_period')}")
        print(f"   Points (All Time): {data.get('total_points_all_time')}")
        print(f"   Total Vehicles: {data.get('total_vehicles')}")
        print(f"   Active Vehicles: {data.get('active_vehicles')}")

        # Show vehicle history
        if data.get('vehicle_history'):
            print("   Vehicle History:")
            for vehicle in data['vehicle_history']:
                status = "Active" if vehicle.get('is_active') else "Inactive"
                print(f"     {vehicle['registration']} ({vehicle['make']}): {status}, "
                      f"Points: {vehicle.get('total_points', 0)}")

        # Show rating calculation info
        calc_info = data.get('rating_calculation_info', {})
        if calc_info:
            print(f"   Calculation Method: {calc_info.get('calculation_method')}")
            print(f"   Points: On-time={calc_info.get('points_on_time')}, "
                  f"Late={calc_info.get('points_late')}")
    else:
        print(f"❌ Failed to get enhanced dashboard: {response.status_code} - {response.text}")

if __name__ == "__main__":
    print("Testing Enhanced Driver Rating Dashboard Endpoints")
    print("=" * 50)
    
    try:
        test_enhanced_dashboard()
        test_driver_rating_dashboard()
        test_partner_performance_report()
        test_payment_history()
        test_rating_trend()
        test_partner_payment_history_report()
        test_calculate_rating()
        test_with_filters()

        print("\n" + "=" * 60)
        print("All Enhanced Endpoint Testing Completed!")
        print("\nEndpoints tested:")
        print("✓ Enhanced Driver Dashboard (with vehicle history & total points)")
        print("✓ Driver Rating Dashboard")
        print("✓ Partner Performance Report (with pagination & advanced filtering)")
        print("✓ Payment History (with pagination & advanced filtering)")
        print("✓ Rating Trend")
        print("✓ Partner Payment History Report")
        print("✓ Manual Rating Calculation")
        print("✓ Advanced Filtering & Sorting")

        print("\n🎉 Enhanced Features Tested:")
        print("• Pagination support")
        print("• Advanced filtering (date, vehicle, amount, status)")
        print("• Total points tracking (period + all-time)")
        print("• Vehicle history and switching")
        print("• Manual rating calculation triggers")
        print("• Sorting and ordering")

    except Exception as e:
        print(f"Error during testing: {e}")
        import traceback
        traceback.print_exc()
