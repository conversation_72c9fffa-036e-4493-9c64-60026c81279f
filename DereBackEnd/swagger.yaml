swagger: '2.0'
info:
  title: Akuko API
  description: API documentation for Akuko project
  termsOfService: https://www.example.com/terms/
  contact:
    email: <EMAIL>
  license:
    name: MIT License
  version: v1
host: 127.0.0.1:8000
schemes:
- http
basePath: /api
consumes:
- application/json
produces:
- application/json
securityDefinitions:
  Basic:
    type: basic
security:
- Basic: []
paths:
  /applications/:
    get:
      operationId: applications_list
      description: ''
      parameters: []
      responses:
        '200':
          description: ''
          schema:
            type: array
            items:
              $ref: '#/definitions/JobApplication'
      tags:
      - applications
    post:
      operationId: applications_create
      description: ''
      parameters:
      - name: data
        in: body
        required: true
        schema:
          $ref: '#/definitions/JobApplication'
      responses:
        '201':
          description: ''
          schema:
            $ref: '#/definitions/JobApplication'
      tags:
      - applications
    parameters: []
  /applications/{id}/:
    get:
      operationId: applications_read
      description: ''
      parameters: []
      responses:
        '200':
          description: ''
          schema:
            $ref: '#/definitions/JobApplication'
      tags:
      - applications
    put:
      operationId: applications_update
      description: ''
      parameters:
      - name: data
        in: body
        required: true
        schema:
          $ref: '#/definitions/JobApplication'
      responses:
        '200':
          description: ''
          schema:
            $ref: '#/definitions/JobApplication'
      tags:
      - applications
    patch:
      operationId: applications_partial_update
      description: ''
      parameters:
      - name: data
        in: body
        required: true
        schema:
          $ref: '#/definitions/JobApplication'
      responses:
        '200':
          description: ''
          schema:
            $ref: '#/definitions/JobApplication'
      tags:
      - applications
    parameters:
    - name: id
      in: path
      description: A unique integer value identifying this job application.
      required: true
      type: integer
  /applications/{id}/hire_driver/:
    post:
      operationId: applications_hire_driver
      description: Hire a driver and link them to the partner
      parameters:
      - name: data
        in: body
        required: true
        schema:
          $ref: '#/definitions/JobApplication'
      responses:
        '201':
          description: ''
          schema:
            $ref: '#/definitions/JobApplication'
      tags:
      - applications
    parameters:
    - name: id
      in: path
      description: A unique integer value identifying this job application.
      required: true
      type: integer
  /applications/{id}/remove_by_partner/:
    post:
      operationId: applications_remove_by_partner
      description: ''
      parameters:
      - name: data
        in: body
        required: true
        schema:
          $ref: '#/definitions/JobApplication'
      responses:
        '201':
          description: ''
          schema:
            $ref: '#/definitions/JobApplication'
      tags:
      - applications
    parameters:
    - name: id
      in: path
      description: A unique integer value identifying this job application.
      required: true
      type: integer
  /applications/{id}/unhire_driver/:
    post:
      operationId: applications_unhire_driver
      description: Unhire a driver and unlink them from the partner
      parameters:
      - name: data
        in: body
        required: true
        schema:
          $ref: '#/definitions/JobApplication'
      responses:
        '201':
          description: ''
          schema:
            $ref: '#/definitions/JobApplication'
      tags:
      - applications
    parameters:
    - name: id
      in: path
      description: A unique integer value identifying this job application.
      required: true
      type: integer
  /applications/{id}/withdraw_by_driver/:
    post:
      operationId: applications_withdraw_by_driver
      description: ''
      parameters:
      - name: data
        in: body
        required: true
        schema:
          $ref: '#/definitions/JobApplication'
      responses:
        '201':
          description: ''
          schema:
            $ref: '#/definitions/JobApplication'
      tags:
      - applications
    parameters:
    - name: id
      in: path
      description: A unique integer value identifying this job application.
      required: true
      type: integer
  /drivers/:
    get:
      operationId: drivers_list
      description: ''
      parameters: []
      responses:
        '200':
          description: ''
          schema:
            type: array
            items:
              $ref: '#/definitions/Driver'
      consumes:
      - multipart/form-data
      - application/x-www-form-urlencoded
      tags:
      - drivers
    post:
      operationId: drivers_create
      description: ''
      parameters:
      - name: first_name
        in: formData
        required: false
        type: string
        maxLength: 30
        x-nullable: true
      - name: last_name
        in: formData
        required: false
        type: string
        maxLength: 30
        x-nullable: true
      - name: email
        in: formData
        required: false
        type: string
        format: email
        maxLength: 255
        x-nullable: true
      - name: id_number
        in: formData
        required: false
        type: string
        maxLength: 20
        x-nullable: true
      - name: partner
        in: formData
        required: false
        type: integer
        x-nullable: true
      - name: date_of_birth
        in: formData
        required: false
        type: string
        format: date
        x-nullable: true
      - name: gender
        in: formData
        required: false
        type: string
        enum:
        - M
        - F
        x-nullable: true
      - name: mobile_number
        in: formData
        required: false
        type: string
        maxLength: 15
        x-nullable: true
      - name: password
        in: formData
        required: false
        type: string
        maxLength: 128
        x-nullable: true
      - name: confirm_password
        in: formData
        required: false
        type: string
        minLength: 1
      - name: vehicle_type
        in: formData
        required: true
        type: integer
      - name: work_area
        in: formData
        required: true
        type: integer
      - name: psv_photo
        in: formData
        required: false
        type: file
        x-nullable: true
      - name: id_photo
        in: formData
        required: false
        type: file
        x-nullable: true
      - name: license_photo
        in: formData
        required: false
        type: file
        x-nullable: true
      - name: good_conduct_photo
        in: formData
        required: false
        type: file
        x-nullable: true
      responses:
        '201':
          description: ''
          schema:
            $ref: '#/definitions/Driver'
      consumes:
      - multipart/form-data
      - application/x-www-form-urlencoded
      tags:
      - drivers
    parameters: []
  /drivers/{id}/:
    get:
      operationId: drivers_read
      description: ''
      parameters: []
      responses:
        '200':
          description: ''
          schema:
            $ref: '#/definitions/Driver'
      consumes:
      - multipart/form-data
      - application/x-www-form-urlencoded
      tags:
      - drivers
    put:
      operationId: drivers_update
      description: ''
      parameters:
      - name: first_name
        in: formData
        required: false
        type: string
        maxLength: 30
        x-nullable: true
      - name: last_name
        in: formData
        required: false
        type: string
        maxLength: 30
        x-nullable: true
      - name: email
        in: formData
        required: false
        type: string
        format: email
        maxLength: 255
        x-nullable: true
      - name: id_number
        in: formData
        required: false
        type: string
        maxLength: 20
        x-nullable: true
      - name: partner
        in: formData
        required: false
        type: integer
        x-nullable: true
      - name: date_of_birth
        in: formData
        required: false
        type: string
        format: date
        x-nullable: true
      - name: gender
        in: formData
        required: false
        type: string
        enum:
        - M
        - F
        x-nullable: true
      - name: mobile_number
        in: formData
        required: false
        type: string
        maxLength: 15
        x-nullable: true
      - name: password
        in: formData
        required: false
        type: string
        maxLength: 128
        x-nullable: true
      - name: confirm_password
        in: formData
        required: false
        type: string
        minLength: 1
      - name: vehicle_type
        in: formData
        required: true
        type: integer
      - name: work_area
        in: formData
        required: true
        type: integer
      - name: psv_photo
        in: formData
        required: false
        type: file
        x-nullable: true
      - name: id_photo
        in: formData
        required: false
        type: file
        x-nullable: true
      - name: license_photo
        in: formData
        required: false
        type: file
        x-nullable: true
      - name: good_conduct_photo
        in: formData
        required: false
        type: file
        x-nullable: true
      responses:
        '200':
          description: ''
          schema:
            $ref: '#/definitions/Driver'
      consumes:
      - multipart/form-data
      - application/x-www-form-urlencoded
      tags:
      - drivers
    patch:
      operationId: drivers_partial_update
      description: ''
      parameters:
      - name: first_name
        in: formData
        required: false
        type: string
        maxLength: 30
        x-nullable: true
      - name: last_name
        in: formData
        required: false
        type: string
        maxLength: 30
        x-nullable: true
      - name: email
        in: formData
        required: false
        type: string
        format: email
        maxLength: 255
        x-nullable: true
      - name: id_number
        in: formData
        required: false
        type: string
        maxLength: 20
        x-nullable: true
      - name: partner
        in: formData
        required: false
        type: integer
        x-nullable: true
      - name: date_of_birth
        in: formData
        required: false
        type: string
        format: date
        x-nullable: true
      - name: gender
        in: formData
        required: false
        type: string
        enum:
        - M
        - F
        x-nullable: true
      - name: mobile_number
        in: formData
        required: false
        type: string
        maxLength: 15
        x-nullable: true
      - name: password
        in: formData
        required: false
        type: string
        maxLength: 128
        x-nullable: true
      - name: confirm_password
        in: formData
        required: false
        type: string
        minLength: 1
      - name: vehicle_type
        in: formData
        required: true
        type: integer
      - name: work_area
        in: formData
        required: true
        type: integer
      - name: psv_photo
        in: formData
        required: false
        type: file
        x-nullable: true
      - name: id_photo
        in: formData
        required: false
        type: file
        x-nullable: true
      - name: license_photo
        in: formData
        required: false
        type: file
        x-nullable: true
      - name: good_conduct_photo
        in: formData
        required: false
        type: file
        x-nullable: true
      responses:
        '200':
          description: ''
          schema:
            $ref: '#/definitions/Driver'
      consumes:
      - multipart/form-data
      - application/x-www-form-urlencoded
      tags:
      - drivers
    delete:
      operationId: drivers_delete
      description: ''
      parameters: []
      responses:
        '204':
          description: ''
      consumes:
      - multipart/form-data
      - application/x-www-form-urlencoded
      tags:
      - drivers
    parameters:
    - name: id
      in: path
      description: A unique integer value identifying this driver.
      required: true
      type: integer
  /jobs/:
    get:
      operationId: jobs_list
      description: ''
      parameters: []
      responses:
        '200':
          description: ''
          schema:
            type: array
            items:
              $ref: '#/definitions/Job'
      consumes:
      - multipart/form-data
      - application/x-www-form-urlencoded
      tags:
      - jobs
    post:
      operationId: jobs_create
      description: ''
      parameters:
      - name: vehicle_make
        in: formData
        required: true
        type: integer
      - name: vehicle_model
        in: formData
        required: true
        type: integer
      - name: preferred_work_area
        in: formData
        required: true
        type: integer
      - name: requirements
        in: formData
        required: true
        type: string
        minLength: 1
      - name: vehicle_photo
        in: formData
        required: false
        type: file
        x-nullable: true
      - name: work_days
        in: formData
        required: true
        type: string
        maxLength: 100
        minLength: 1
      - name: min_age
        in: formData
        required: false
        type: integer
        maximum: 2147483647
        minimum: -2147483648
        x-nullable: true
      - name: max_age
        in: formData
        required: false
        type: integer
        maximum: 2147483647
        minimum: -2147483648
        x-nullable: true
      - name: status
        in: formData
        required: false
        type: string
        enum:
        - open
        - closed
      responses:
        '201':
          description: ''
          schema:
            $ref: '#/definitions/Job'
      consumes:
      - multipart/form-data
      - application/x-www-form-urlencoded
      tags:
      - jobs
    parameters: []
  /jobs/{id}/:
    get:
      operationId: jobs_read
      description: ''
      parameters: []
      responses:
        '200':
          description: ''
          schema:
            $ref: '#/definitions/Job'
      consumes:
      - multipart/form-data
      - application/x-www-form-urlencoded
      tags:
      - jobs
    put:
      operationId: jobs_update
      description: ''
      parameters:
      - name: vehicle_make
        in: formData
        required: true
        type: integer
      - name: vehicle_model
        in: formData
        required: true
        type: integer
      - name: preferred_work_area
        in: formData
        required: true
        type: integer
      - name: requirements
        in: formData
        required: true
        type: string
        minLength: 1
      - name: vehicle_photo
        in: formData
        required: false
        type: file
        x-nullable: true
      - name: work_days
        in: formData
        required: true
        type: string
        maxLength: 100
        minLength: 1
      - name: min_age
        in: formData
        required: false
        type: integer
        maximum: 2147483647
        minimum: -2147483648
        x-nullable: true
      - name: max_age
        in: formData
        required: false
        type: integer
        maximum: 2147483647
        minimum: -2147483648
        x-nullable: true
      - name: status
        in: formData
        required: false
        type: string
        enum:
        - open
        - closed
      responses:
        '200':
          description: ''
          schema:
            $ref: '#/definitions/Job'
      consumes:
      - multipart/form-data
      - application/x-www-form-urlencoded
      tags:
      - jobs
    patch:
      operationId: jobs_partial_update
      description: ''
      parameters:
      - name: vehicle_make
        in: formData
        required: true
        type: integer
      - name: vehicle_model
        in: formData
        required: true
        type: integer
      - name: preferred_work_area
        in: formData
        required: true
        type: integer
      - name: requirements
        in: formData
        required: true
        type: string
        minLength: 1
      - name: vehicle_photo
        in: formData
        required: false
        type: file
        x-nullable: true
      - name: work_days
        in: formData
        required: true
        type: string
        maxLength: 100
        minLength: 1
      - name: min_age
        in: formData
        required: false
        type: integer
        maximum: 2147483647
        minimum: -2147483648
        x-nullable: true
      - name: max_age
        in: formData
        required: false
        type: integer
        maximum: 2147483647
        minimum: -2147483648
        x-nullable: true
      - name: status
        in: formData
        required: false
        type: string
        enum:
        - open
        - closed
      responses:
        '200':
          description: ''
          schema:
            $ref: '#/definitions/Job'
      consumes:
      - multipart/form-data
      - application/x-www-form-urlencoded
      tags:
      - jobs
    delete:
      operationId: jobs_delete
      description: ''
      parameters: []
      responses:
        '204':
          description: ''
      consumes:
      - multipart/form-data
      - application/x-www-form-urlencoded
      tags:
      - jobs
    parameters:
    - name: id
      in: path
      required: true
      type: string
  /jobs/{id}/close_job/:
    post:
      operationId: jobs_close_job
      description: ''
      parameters:
      - name: vehicle_make
        in: formData
        required: true
        type: integer
      - name: vehicle_model
        in: formData
        required: true
        type: integer
      - name: preferred_work_area
        in: formData
        required: true
        type: integer
      - name: requirements
        in: formData
        required: true
        type: string
        minLength: 1
      - name: vehicle_photo
        in: formData
        required: false
        type: file
        x-nullable: true
      - name: work_days
        in: formData
        required: true
        type: string
        maxLength: 100
        minLength: 1
      - name: min_age
        in: formData
        required: false
        type: integer
        maximum: 2147483647
        minimum: -2147483648
        x-nullable: true
      - name: max_age
        in: formData
        required: false
        type: integer
        maximum: 2147483647
        minimum: -2147483648
        x-nullable: true
      - name: status
        in: formData
        required: false
        type: string
        enum:
        - open
        - closed
      responses:
        '201':
          description: ''
          schema:
            $ref: '#/definitions/Job'
      consumes:
      - multipart/form-data
      - application/x-www-form-urlencoded
      tags:
      - jobs
    parameters:
    - name: id
      in: path
      required: true
      type: string
  /partner-drivers/:
    get:
      operationId: partner-drivers_list
      description: ''
      parameters: []
      responses:
        '200':
          description: ''
          schema:
            type: array
            items:
              $ref: '#/definitions/Driver'
      consumes:
      - multipart/form-data
      - application/x-www-form-urlencoded
      tags:
      - partner-drivers
    post:
      operationId: partner-drivers_create
      description: ''
      parameters:
      - name: first_name
        in: formData
        required: false
        type: string
        maxLength: 30
        x-nullable: true
      - name: last_name
        in: formData
        required: false
        type: string
        maxLength: 30
        x-nullable: true
      - name: email
        in: formData
        required: false
        type: string
        format: email
        maxLength: 255
        x-nullable: true
      - name: id_number
        in: formData
        required: false
        type: string
        maxLength: 20
        x-nullable: true
      - name: partner
        in: formData
        required: false
        type: integer
        x-nullable: true
      - name: date_of_birth
        in: formData
        required: false
        type: string
        format: date
        x-nullable: true
      - name: gender
        in: formData
        required: false
        type: string
        enum:
        - M
        - F
        x-nullable: true
      - name: mobile_number
        in: formData
        required: false
        type: string
        maxLength: 15
        x-nullable: true
      - name: password
        in: formData
        required: false
        type: string
        maxLength: 128
        x-nullable: true
      - name: confirm_password
        in: formData
        required: false
        type: string
        minLength: 1
      - name: vehicle_type
        in: formData
        required: true
        type: integer
      - name: work_area
        in: formData
        required: true
        type: integer
      - name: psv_photo
        in: formData
        required: false
        type: file
        x-nullable: true
      - name: id_photo
        in: formData
        required: false
        type: file
        x-nullable: true
      - name: license_photo
        in: formData
        required: false
        type: file
        x-nullable: true
      - name: good_conduct_photo
        in: formData
        required: false
        type: file
        x-nullable: true
      responses:
        '201':
          description: ''
          schema:
            $ref: '#/definitions/Driver'
      consumes:
      - multipart/form-data
      - application/x-www-form-urlencoded
      tags:
      - partner-drivers
    parameters: []
  /partner-drivers/{id}/:
    get:
      operationId: partner-drivers_read
      description: ''
      parameters: []
      responses:
        '200':
          description: ''
          schema:
            $ref: '#/definitions/Driver'
      consumes:
      - multipart/form-data
      - application/x-www-form-urlencoded
      tags:
      - partner-drivers
    put:
      operationId: partner-drivers_update
      description: ''
      parameters:
      - name: first_name
        in: formData
        required: false
        type: string
        maxLength: 30
        x-nullable: true
      - name: last_name
        in: formData
        required: false
        type: string
        maxLength: 30
        x-nullable: true
      - name: email
        in: formData
        required: false
        type: string
        format: email
        maxLength: 255
        x-nullable: true
      - name: id_number
        in: formData
        required: false
        type: string
        maxLength: 20
        x-nullable: true
      - name: partner
        in: formData
        required: false
        type: integer
        x-nullable: true
      - name: date_of_birth
        in: formData
        required: false
        type: string
        format: date
        x-nullable: true
      - name: gender
        in: formData
        required: false
        type: string
        enum:
        - M
        - F
        x-nullable: true
      - name: mobile_number
        in: formData
        required: false
        type: string
        maxLength: 15
        x-nullable: true
      - name: password
        in: formData
        required: false
        type: string
        maxLength: 128
        x-nullable: true
      - name: confirm_password
        in: formData
        required: false
        type: string
        minLength: 1
      - name: vehicle_type
        in: formData
        required: true
        type: integer
      - name: work_area
        in: formData
        required: true
        type: integer
      - name: psv_photo
        in: formData
        required: false
        type: file
        x-nullable: true
      - name: id_photo
        in: formData
        required: false
        type: file
        x-nullable: true
      - name: license_photo
        in: formData
        required: false
        type: file
        x-nullable: true
      - name: good_conduct_photo
        in: formData
        required: false
        type: file
        x-nullable: true
      responses:
        '200':
          description: ''
          schema:
            $ref: '#/definitions/Driver'
      consumes:
      - multipart/form-data
      - application/x-www-form-urlencoded
      tags:
      - partner-drivers
    patch:
      operationId: partner-drivers_partial_update
      description: ''
      parameters:
      - name: first_name
        in: formData
        required: false
        type: string
        maxLength: 30
        x-nullable: true
      - name: last_name
        in: formData
        required: false
        type: string
        maxLength: 30
        x-nullable: true
      - name: email
        in: formData
        required: false
        type: string
        format: email
        maxLength: 255
        x-nullable: true
      - name: id_number
        in: formData
        required: false
        type: string
        maxLength: 20
        x-nullable: true
      - name: partner
        in: formData
        required: false
        type: integer
        x-nullable: true
      - name: date_of_birth
        in: formData
        required: false
        type: string
        format: date
        x-nullable: true
      - name: gender
        in: formData
        required: false
        type: string
        enum:
        - M
        - F
        x-nullable: true
      - name: mobile_number
        in: formData
        required: false
        type: string
        maxLength: 15
        x-nullable: true
      - name: password
        in: formData
        required: false
        type: string
        maxLength: 128
        x-nullable: true
      - name: confirm_password
        in: formData
        required: false
        type: string
        minLength: 1
      - name: vehicle_type
        in: formData
        required: true
        type: integer
      - name: work_area
        in: formData
        required: true
        type: integer
      - name: psv_photo
        in: formData
        required: false
        type: file
        x-nullable: true
      - name: id_photo
        in: formData
        required: false
        type: file
        x-nullable: true
      - name: license_photo
        in: formData
        required: false
        type: file
        x-nullable: true
      - name: good_conduct_photo
        in: formData
        required: false
        type: file
        x-nullable: true
      responses:
        '200':
          description: ''
          schema:
            $ref: '#/definitions/Driver'
      consumes:
      - multipart/form-data
      - application/x-www-form-urlencoded
      tags:
      - partner-drivers
    delete:
      operationId: partner-drivers_delete
      description: ''
      parameters: []
      responses:
        '204':
          description: ''
      consumes:
      - multipart/form-data
      - application/x-www-form-urlencoded
      tags:
      - partner-drivers
    parameters:
    - name: id
      in: path
      required: true
      type: string
  /partners/:
    get:
      operationId: partners_list
      description: ''
      parameters: []
      responses:
        '200':
          description: ''
          schema:
            type: array
            items:
              $ref: '#/definitions/Partner'
      consumes:
      - multipart/form-data
      - application/x-www-form-urlencoded
      tags:
      - partners
    post:
      operationId: partners_create
      description: ''
      parameters:
      - name: first_name
        in: formData
        required: false
        type: string
        maxLength: 30
        x-nullable: true
      - name: last_name
        in: formData
        required: false
        type: string
        maxLength: 30
        x-nullable: true
      - name: email
        in: formData
        required: false
        type: string
        format: email
        maxLength: 255
        x-nullable: true
      - name: id_number
        in: formData
        required: false
        type: string
        maxLength: 20
        x-nullable: true
      - name: company_number
        in: formData
        required: false
        type: string
        maxLength: 20
        x-nullable: true
      - name: company_name
        in: formData
        required: false
        type: string
        maxLength: 100
        x-nullable: true
      - name: password
        in: formData
        required: false
        type: string
        maxLength: 128
        x-nullable: true
      - name: confirm_password
        in: formData
        required: false
        type: string
        minLength: 1
      - name: mobile_number
        in: formData
        required: false
        type: string
        maxLength: 15
        x-nullable: true
      - name: id_photo
        in: formData
        required: false
        type: file
        x-nullable: true
      responses:
        '201':
          description: ''
          schema:
            $ref: '#/definitions/Partner'
      consumes:
      - multipart/form-data
      - application/x-www-form-urlencoded
      tags:
      - partners
    parameters: []
  /partners/{id}/:
    get:
      operationId: partners_read
      description: ''
      parameters: []
      responses:
        '200':
          description: ''
          schema:
            $ref: '#/definitions/Partner'
      consumes:
      - multipart/form-data
      - application/x-www-form-urlencoded
      tags:
      - partners
    put:
      operationId: partners_update
      description: ''
      parameters:
      - name: first_name
        in: formData
        required: false
        type: string
        maxLength: 30
        x-nullable: true
      - name: last_name
        in: formData
        required: false
        type: string
        maxLength: 30
        x-nullable: true
      - name: email
        in: formData
        required: false
        type: string
        format: email
        maxLength: 255
        x-nullable: true
      - name: id_number
        in: formData
        required: false
        type: string
        maxLength: 20
        x-nullable: true
      - name: company_number
        in: formData
        required: false
        type: string
        maxLength: 20
        x-nullable: true
      - name: company_name
        in: formData
        required: false
        type: string
        maxLength: 100
        x-nullable: true
      - name: password
        in: formData
        required: false
        type: string
        maxLength: 128
        x-nullable: true
      - name: confirm_password
        in: formData
        required: false
        type: string
        minLength: 1
      - name: mobile_number
        in: formData
        required: false
        type: string
        maxLength: 15
        x-nullable: true
      - name: id_photo
        in: formData
        required: false
        type: file
        x-nullable: true
      responses:
        '200':
          description: ''
          schema:
            $ref: '#/definitions/Partner'
      consumes:
      - multipart/form-data
      - application/x-www-form-urlencoded
      tags:
      - partners
    patch:
      operationId: partners_partial_update
      description: ''
      parameters:
      - name: first_name
        in: formData
        required: false
        type: string
        maxLength: 30
        x-nullable: true
      - name: last_name
        in: formData
        required: false
        type: string
        maxLength: 30
        x-nullable: true
      - name: email
        in: formData
        required: false
        type: string
        format: email
        maxLength: 255
        x-nullable: true
      - name: id_number
        in: formData
        required: false
        type: string
        maxLength: 20
        x-nullable: true
      - name: company_number
        in: formData
        required: false
        type: string
        maxLength: 20
        x-nullable: true
      - name: company_name
        in: formData
        required: false
        type: string
        maxLength: 100
        x-nullable: true
      - name: password
        in: formData
        required: false
        type: string
        maxLength: 128
        x-nullable: true
      - name: confirm_password
        in: formData
        required: false
        type: string
        minLength: 1
      - name: mobile_number
        in: formData
        required: false
        type: string
        maxLength: 15
        x-nullable: true
      - name: id_photo
        in: formData
        required: false
        type: file
        x-nullable: true
      responses:
        '200':
          description: ''
          schema:
            $ref: '#/definitions/Partner'
      consumes:
      - multipart/form-data
      - application/x-www-form-urlencoded
      tags:
      - partners
    delete:
      operationId: partners_delete
      description: ''
      parameters: []
      responses:
        '204':
          description: ''
      consumes:
      - multipart/form-data
      - application/x-www-form-urlencoded
      tags:
      - partners
    parameters:
    - name: id
      in: path
      description: A unique integer value identifying this partner.
      required: true
      type: integer
  /password-reset-confirm/:
    post:
      operationId: password-reset-confirm_create
      description: ''
      parameters: []
      responses:
        '201':
          description: ''
      tags:
      - password-reset-confirm
    parameters: []
  /password-reset/:
    post:
      operationId: password-reset_create
      description: ''
      parameters: []
      responses:
        '201':
          description: ''
      tags:
      - password-reset
    parameters: []
  /test-email/:
    post:
      operationId: test-email_create
      description: ''
      parameters: []
      responses:
        '201':
          description: ''
      tags:
      - test-email
    parameters: []
  /token/:
    post:
      operationId: token_create
      description: ''
      parameters:
      - name: data
        in: body
        required: true
        schema:
          $ref: '#/definitions/CustomTokenObtainPair'
      responses:
        '201':
          description: ''
          schema:
            $ref: '#/definitions/CustomTokenObtainPair'
      tags:
      - token
    parameters: []
  /token/refresh/:
    post:
      operationId: token_refresh_create
      description: |-
        Takes a refresh type JSON web token and returns an access type JSON web
        token if the refresh token is valid.
      parameters:
      - name: data
        in: body
        required: true
        schema:
          $ref: '#/definitions/TokenRefresh'
      responses:
        '201':
          description: ''
          schema:
            $ref: '#/definitions/TokenRefresh'
      tags:
      - token
    parameters: []
  /users/me/:
    get:
      operationId: users_me_list
      description: ''
      parameters: []
      responses:
        '200':
          description: ''
      tags:
      - users
    parameters: []
  /vehicle-makes/:
    get:
      operationId: vehicle-makes_list
      description: ''
      parameters: []
      responses:
        '200':
          description: ''
          schema:
            type: array
            items:
              $ref: '#/definitions/VehicleMake'
      tags:
      - vehicle-makes
    parameters: []
  /vehicle-makes/{id}/:
    get:
      operationId: vehicle-makes_read
      description: ''
      parameters: []
      responses:
        '200':
          description: ''
          schema:
            $ref: '#/definitions/VehicleMake'
      tags:
      - vehicle-makes
    parameters:
    - name: id
      in: path
      description: A unique integer value identifying this vehicle make.
      required: true
      type: integer
  /vehicle-models/:
    get:
      operationId: vehicle-models_list
      description: ''
      parameters: []
      responses:
        '200':
          description: ''
          schema:
            type: array
            items:
              $ref: '#/definitions/VehicleModel'
      tags:
      - vehicle-models
    parameters: []
  /vehicle-models/{id}/:
    get:
      operationId: vehicle-models_read
      description: ''
      parameters: []
      responses:
        '200':
          description: ''
          schema:
            $ref: '#/definitions/VehicleModel'
      tags:
      - vehicle-models
    parameters:
    - name: id
      in: path
      required: true
      type: string
  /vehicle-types/:
    get:
      operationId: vehicle-types_list
      description: ''
      parameters: []
      responses:
        '200':
          description: ''
          schema:
            type: array
            items:
              $ref: '#/definitions/VehicleType'
      tags:
      - vehicle-types
    post:
      operationId: vehicle-types_create
      description: ''
      parameters:
      - name: data
        in: body
        required: true
        schema:
          $ref: '#/definitions/VehicleType'
      responses:
        '201':
          description: ''
          schema:
            $ref: '#/definitions/VehicleType'
      tags:
      - vehicle-types
    parameters: []
  /vehicle-types/{id}/:
    get:
      operationId: vehicle-types_read
      description: ''
      parameters: []
      responses:
        '200':
          description: ''
          schema:
            $ref: '#/definitions/VehicleType'
      tags:
      - vehicle-types
    put:
      operationId: vehicle-types_update
      description: ''
      parameters:
      - name: data
        in: body
        required: true
        schema:
          $ref: '#/definitions/VehicleType'
      responses:
        '200':
          description: ''
          schema:
            $ref: '#/definitions/VehicleType'
      tags:
      - vehicle-types
    patch:
      operationId: vehicle-types_partial_update
      description: ''
      parameters:
      - name: data
        in: body
        required: true
        schema:
          $ref: '#/definitions/VehicleType'
      responses:
        '200':
          description: ''
          schema:
            $ref: '#/definitions/VehicleType'
      tags:
      - vehicle-types
    delete:
      operationId: vehicle-types_delete
      description: ''
      parameters: []
      responses:
        '204':
          description: ''
      tags:
      - vehicle-types
    parameters:
    - name: id
      in: path
      description: A unique integer value identifying this vehicle type.
      required: true
      type: integer
  /verify-email/{verification_code}/:
    get:
      operationId: verify-email_read
      description: ''
      parameters: []
      responses:
        '200':
          description: ''
      tags:
      - verify-email
    parameters:
    - name: verification_code
      in: path
      required: true
      type: string
  /work-areas/:
    get:
      operationId: work-areas_list
      description: ''
      parameters: []
      responses:
        '200':
          description: ''
          schema:
            type: array
            items:
              $ref: '#/definitions/WorkArea'
      tags:
      - work-areas
    post:
      operationId: work-areas_create
      description: ''
      parameters:
      - name: data
        in: body
        required: true
        schema:
          $ref: '#/definitions/WorkArea'
      responses:
        '201':
          description: ''
          schema:
            $ref: '#/definitions/WorkArea'
      tags:
      - work-areas
    parameters: []
  /work-areas/{id}/:
    get:
      operationId: work-areas_read
      description: ''
      parameters: []
      responses:
        '200':
          description: ''
          schema:
            $ref: '#/definitions/WorkArea'
      tags:
      - work-areas
    put:
      operationId: work-areas_update
      description: ''
      parameters:
      - name: data
        in: body
        required: true
        schema:
          $ref: '#/definitions/WorkArea'
      responses:
        '200':
          description: ''
          schema:
            $ref: '#/definitions/WorkArea'
      tags:
      - work-areas
    patch:
      operationId: work-areas_partial_update
      description: ''
      parameters:
      - name: data
        in: body
        required: true
        schema:
          $ref: '#/definitions/WorkArea'
      responses:
        '200':
          description: ''
          schema:
            $ref: '#/definitions/WorkArea'
      tags:
      - work-areas
    delete:
      operationId: work-areas_delete
      description: ''
      parameters: []
      responses:
        '204':
          description: ''
      tags:
      - work-areas
    parameters:
    - name: id
      in: path
      description: A unique integer value identifying this work area.
      required: true
      type: integer
definitions:
  Job:
    required:
    - vehicle_make
    - vehicle_model
    - preferred_work_area
    - requirements
    - work_days
    type: object
    properties:
      id:
        title: ID
        type: integer
        readOnly: true
      vehicle_make:
        title: Vehicle make
        type: integer
      vehicle_model:
        title: Vehicle model
        type: integer
      preferred_work_area:
        title: Preferred work area
        type: integer
      requirements:
        title: Requirements
        type: string
        minLength: 1
      vehicle_photo:
        title: Vehicle photo
        type: string
        readOnly: true
        x-nullable: true
        format: uri
      work_days:
        title: Work days
        type: string
        maxLength: 100
        minLength: 1
      min_age:
        title: Min age
        type: integer
        maximum: 2147483647
        minimum: -2147483648
        x-nullable: true
      max_age:
        title: Max age
        type: integer
        maximum: 2147483647
        minimum: -2147483648
        x-nullable: true
      status:
        title: Status
        type: string
        enum:
        - open
        - closed
  Driver:
    required:
    - vehicle_type
    - work_area
    type: object
    properties:
      id:
        title: ID
        type: integer
        readOnly: true
      first_name:
        title: First name
        type: string
        maxLength: 30
        x-nullable: true
      last_name:
        title: Last name
        type: string
        maxLength: 30
        x-nullable: true
      email:
        title: Email
        type: string
        format: email
        maxLength: 255
        x-nullable: true
      id_number:
        title: Id number
        type: string
        maxLength: 20
        x-nullable: true
      partner:
        title: Partner
        type: integer
        x-nullable: true
      date_of_birth:
        title: Date of birth
        type: string
        format: date
        x-nullable: true
      gender:
        title: Gender
        type: string
        enum:
        - M
        - F
        x-nullable: true
      mobile_number:
        title: Mobile number
        type: string
        maxLength: 15
        x-nullable: true
      password:
        title: Password
        type: string
        maxLength: 128
        x-nullable: true
      confirm_password:
        title: Confirm password
        type: string
        minLength: 1
      vehicle_type:
        title: Vehicle type
        type: integer
      work_area:
        title: Work area
        type: integer
      psv_photo:
        title: Psv photo
        type: string
        readOnly: true
        x-nullable: true
        format: uri
      id_photo:
        title: Id photo
        type: string
        readOnly: true
        x-nullable: true
        format: uri
      license_photo:
        title: License photo
        type: string
        readOnly: true
        x-nullable: true
        format: uri
      good_conduct_photo:
        title: Good conduct photo
        type: string
        readOnly: true
        x-nullable: true
        format: uri
  JobApplication:
    type: object
    properties:
      id:
        title: ID
        type: integer
        readOnly: true
      job:
        $ref: '#/definitions/Job'
      driver:
        $ref: '#/definitions/Driver'
      status:
        title: Status
        type: string
        enum:
        - active
        - rejected
        - withdrawn
        - hired
        - unhired
      status_display:
        title: Status display
        type: string
        readOnly: true
      applied_at:
        title: Applied at
        type: string
        format: date-time
        readOnly: true
      removed_by_partner:
        title: Removed by partner
        type: boolean
      withdrawn_by_driver:
        title: Withdrawn by driver
        type: boolean
  Partner:
    type: object
    properties:
      id:
        title: ID
        type: integer
        readOnly: true
      first_name:
        title: First name
        type: string
        maxLength: 30
        x-nullable: true
      last_name:
        title: Last name
        type: string
        maxLength: 30
        x-nullable: true
      email:
        title: Email
        type: string
        format: email
        maxLength: 255
        x-nullable: true
      id_number:
        title: Id number
        type: string
        maxLength: 20
        x-nullable: true
      company_number:
        title: Company number
        type: string
        maxLength: 20
        x-nullable: true
      company_name:
        title: Company name
        type: string
        maxLength: 100
        x-nullable: true
      password:
        title: Password
        type: string
        maxLength: 128
        x-nullable: true
      confirm_password:
        title: Confirm password
        type: string
        minLength: 1
      mobile_number:
        title: Mobile number
        type: string
        maxLength: 15
        x-nullable: true
      id_photo:
        title: Id photo
        type: string
        readOnly: true
        x-nullable: true
        format: uri
  CustomTokenObtainPair:
    required:
    - role
    - email
    - password
    type: object
    properties:
      role:
        title: Role
        type: string
        minLength: 1
      email:
        title: Email
        type: string
        minLength: 1
      password:
        title: Password
        type: string
        minLength: 1
  TokenRefresh:
    required:
    - refresh
    type: object
    properties:
      refresh:
        title: Refresh
        type: string
        minLength: 1
      access:
        title: Access
        type: string
        readOnly: true
        minLength: 1
  VehicleMake:
    required:
    - name
    type: object
    properties:
      id:
        title: ID
        type: integer
        readOnly: true
      name:
        title: Name
        type: string
        maxLength: 100
        minLength: 1
  VehicleModel:
    required:
    - name
    - make
    type: object
    properties:
      id:
        title: ID
        type: integer
        readOnly: true
      name:
        title: Name
        type: string
        maxLength: 100
        minLength: 1
      make:
        $ref: '#/definitions/VehicleMake'
  VehicleType:
    required:
    - name
    type: object
    properties:
      id:
        title: ID
        type: integer
        readOnly: true
      name:
        title: Name
        type: string
        maxLength: 100
        minLength: 1
  WorkArea:
    required:
    - name
    type: object
    properties:
      id:
        title: ID
        type: integer
        readOnly: true
      name:
        title: Name
        type: string
        maxLength: 100
        minLength: 1
