# Driver Flagging System Documentation

## Overview

The Driver Flagging System is a comprehensive feature that allows partners to flag drivers for suspicious behavior, false information, or other violations. The system includes automatic account locking, notification systems, and administrative controls.

## Business Rules

### Flagging Rules
- **One Flag Per Partner**: Each partner can flag a driver only once
- **Time Window**: Only flags within the last 6 months count toward account locking
- **Automatic Locking**: 
  - 2 different partners flagging = 6-month temporary lock
  - 3+ different partners flagging = permanent ban

### Account Locking Types
1. **Temporary Lock**: 6-month suspension, automatically expires
2. **Permanent Lock**: Indefinite ban, requires admin intervention to unlock

## API Endpoints

### 1. Flag a Driver
**Endpoint**: `POST /api/drivers/{driver_id}/flag_driver/`
**Permission**: Partners only
**Description**: Flag a driver for violations

**Request Body**:
```json
{
    "reason": "Driver provided false information about vehicle ownership"
}
```

**Response**:
```json
{
    "message": "Driver flagged successfully.",
    "flag_id": 123,
    "driver_id": 456,
    "driver_name": "<PERSON>",
    "reason": "Driver provided false information...",
    "flagged_at": "2024-01-15T10:30:00Z",
    "total_flags": 2,
    "unique_partners_flagged": 2
}
```

### 2. Get Driver Flag Status
**Endpoint**: `GET /api/drivers/{driver_id}/flag_status/`
**Permission**: Partners (own drivers), Admins (all drivers)
**Description**: Get flagging status and history for a driver

**Response**:
```json
{
    "driver_id": 456,
    "driver_name": "John Doe",
    "is_flagged": true,
    "total_flags": 2,
    "unique_partners_flagged": 2,
    "is_locked": true,
    "lock_type": "temporary",
    "locked_at": "2024-01-15T10:30:00Z",
    "lock_expires_at": "2024-07-15T10:30:00Z",
    "flag_details": [
        {
            "flag_id": 123,
            "partner_name": "ABC Transport",
            "reason": "False information",
            "flagged_at": "2024-01-15T10:30:00Z",
            "is_active": true
        }
    ]
}
```

### 3. Get Flagged Drivers List
**Endpoint**: `GET /api/drivers/flagged_drivers/`
**Permission**: Partners (own flagged drivers), Admins (all flagged drivers)
**Description**: Get paginated list of flagged drivers

**Query Parameters**:
- `page`: Page number (default: 1)
- `page_size`: Items per page (default: 20, max: 100)
- `lock_status`: Filter by lock status (`all`, `locked`, `unlocked`)
- `min_flags`: Minimum number of flags (default: 1)

**Response**:
```json
{
    "summary": {
        "total_flagged_drivers": 15,
        "locked_drivers": 8,
        "unlocked_drivers": 7
    },
    "pagination": {
        "current_page": 1,
        "page_size": 20,
        "total_pages": 1,
        "total_records": 15,
        "has_next": false,
        "has_previous": false
    },
    "flagged_drivers": [
        {
            "driver_id": 456,
            "driver_name": "John Doe",
            "driver_code": "DRV-0456",
            "email": "<EMAIL>",
            "total_flags": 2,
            "unique_partners_flagged": 2,
            "is_locked": true,
            "lock_type": "temporary",
            "locked_at": "2024-01-15T10:30:00Z",
            "lock_expires_at": "2024-07-15T10:30:00Z",
            "recent_flags": [
                {
                    "partner_name": "ABC Transport",
                    "reason": "False information",
                    "flagged_at": "2024-01-15T10:30:00Z"
                }
            ]
        }
    ]
}
```

### 4. Get Flagging Summary
**Endpoint**: `GET /api/drivers/flagging_summary/`
**Permission**: Partners only
**Description**: Get comprehensive flagging statistics for the partner

**Query Parameters**:
- `months_back`: Number of months to analyze (default: 6, max: 24)

**Response**:
```json
{
    "partner_id": 123,
    "partner_name": "ABC Transport",
    "summary_period": {
        "months_back": 6,
        "start_date": "2023-07-15",
        "end_date": "2024-01-15"
    },
    "flagging_statistics": {
        "total_flags_created": 5,
        "active_flags": 4,
        "resolved_flags": 1,
        "unique_drivers_flagged": 4,
        "resolution_rate": 20.0
    },
    "driver_lock_impact": {
        "total_locked_drivers": 2,
        "temporary_locks": 1,
        "permanent_locks": 1,
        "locked_drivers_details": [...]
    },
    "recent_activity": {
        "last_30_days_flags": 2,
        "recent_flags": [...]
    },
    "monthly_breakdown": [...],
    "top_flagging_reasons": [
        {"reason": "False information about vehicle", "count": 3},
        {"reason": "Suspicious behavior", "count": 2}
    ]
}
```

## Administrative Endpoints

### 1. Driver Flag Management
**Endpoint**: `GET /api/driver-flags/`
**Permission**: Admins only
**Description**: Get paginated list of all driver flags with filtering

**Query Parameters**:
- `page`: Page number
- `page_size`: Items per page
- `driver_id`: Filter by driver ID
- `partner_id`: Filter by partner ID
- `is_active`: Filter by active status (`true`/`false`)
- `start_date`: Filter by creation date (YYYY-MM-DD)
- `end_date`: Filter by creation date (YYYY-MM-DD)
- `search`: Search in flag reasons

### 2. Resolve Flag
**Endpoint**: `POST /api/driver-flags/{flag_id}/resolve_flag/`
**Permission**: Admins only
**Description**: Mark a flag as resolved

**Request Body**:
```json
{
    "resolution_notes": "Flag resolved after investigation - no violation found"
}
```

### 3. Unlock Driver Account
**Endpoint**: `POST /api/driver-flags/unlock_driver/`
**Permission**: Admins only
**Description**: Manually unlock a driver account

**Request Body**:
```json
{
    "driver_id": 456,
    "unlock_reason": "Appeal approved after review"
}
```

## Database Models

### DriverFlag Model
```python
class DriverFlag(models.Model):
    driver = models.ForeignKey(Driver, on_delete=models.CASCADE, related_name='flags')
    partner = models.ForeignKey(Partner, on_delete=models.CASCADE, related_name='driver_flags')
    reason = models.TextField()  # Mandatory reason for flagging
    created_at = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)
    resolved_at = models.DateTimeField(null=True, blank=True)
    resolved_by = models.ForeignKey(User, null=True, blank=True, on_delete=models.SET_NULL)
    resolution_notes = models.TextField(null=True, blank=True)
    flagged_by_user = models.ForeignKey(User, null=True, blank=True, on_delete=models.SET_NULL)
```

### Driver Model Extensions
```python
class Driver(models.Model):
    # ... existing fields ...
    is_locked = models.BooleanField(default=False)
    lock_type = models.CharField(max_length=20, choices=[('temporary', 'Temporary'), ('permanent', 'Permanent')], null=True, blank=True)
    locked_at = models.DateTimeField(null=True, blank=True)
    lock_expires_at = models.DateTimeField(null=True, blank=True)
    lock_reason = models.TextField(null=True, blank=True)
```

## Notification System

### Email Notifications
- **Driver Flagged**: Sent to driver when flagged
- **Account Locked**: Sent to driver when account is locked
- **Account Unlocked**: Sent to driver when account is unlocked (admin action)

### In-System Notifications
- Job application responses include flagging information
- Driver dashboard shows lock status and expiry dates

## Integration Points

### Job Application System
- Drivers with active flags see warning messages
- Partners see driver flag information in job applications
- Locked drivers cannot apply for jobs

### Authentication System
- Locked drivers cannot log in
- Custom error messages based on lock type and expiry

## Error Handling

### Common Error Responses
- **403 Forbidden**: Insufficient permissions
- **404 Not Found**: Driver/Partner not found
- **400 Bad Request**: Missing required fields or invalid data
- **409 Conflict**: Attempting to flag driver already flagged by same partner

## Security Considerations

1. **Access Control**: Partners can only flag drivers applying to their jobs
2. **Rate Limiting**: Prevent abuse of flagging system
3. **Audit Trail**: All flagging actions are logged with timestamps and user information
4. **Data Privacy**: Flag reasons are only visible to relevant parties

## Monitoring and Analytics

### Key Metrics
- Total flags created per month
- Flag resolution rate
- Average time to resolution
- Most common flagging reasons
- Driver lock rates by partner

### Reporting
- Monthly flagging reports for admins
- Partner-specific flagging summaries
- Driver appeal tracking

## Implementation Details

### Automatic Lock Logic
```python
def _check_and_apply_driver_lock(self, driver):
    """Check if driver should be locked based on flag count"""
    unique_partners_count = DriverFlag.get_unique_partners_flagged_count(driver)

    if unique_partners_count >= 3:
        # Permanent lock for 3+ different partners
        driver.apply_lock('permanent', 'Flagged by 3 or more different partners')
    elif unique_partners_count >= 2:
        # Temporary lock for 2 different partners
        driver.apply_lock('temporary', 'Flagged by 2 different partners')
```

### Signal Integration
```python
@receiver(post_save, sender=DriverFlag)
def send_flag_notification(sender, instance, created, **kwargs):
    """Send notification when driver is flagged"""
    if created:
        send_driver_flag_notification(instance)
```

### Permission Classes
```python
class CanFlagDriverPermission(BasePermission):
    """Custom permission for driver flagging"""
    def has_permission(self, request, view):
        return request.user.role == 'partner'

    def has_object_permission(self, request, view, obj):
        # Partners can only flag drivers who applied to their jobs
        return JobApplication.objects.filter(
            driver=obj,
            job__partner=request.user.partner
        ).exists()
```

## Testing Guidelines

### Unit Tests
- Test flagging business logic
- Test automatic locking mechanisms
- Test permission controls
- Test notification sending

### Integration Tests
- Test complete flagging workflow
- Test job application integration
- Test authentication integration

### API Tests
- Test all endpoint responses
- Test pagination functionality
- Test filtering and search
- Test error handling

## Deployment Considerations

### Database Migrations
- Run migrations for DriverFlag model
- Update existing Driver model with lock fields
- Create necessary indexes for performance

### Configuration
- Set up email templates for notifications
- Configure notification settings
- Set up monitoring alerts

### Performance Optimization
- Index on (driver, is_active, created_at)
- Index on (partner, created_at)
- Use select_related for related objects
- Implement caching for frequently accessed data

## Troubleshooting

### Common Issues
1. **Driver can't log in**: Check if account is locked
2. **Partner can't flag driver**: Verify driver applied to partner's job
3. **Notifications not sent**: Check email configuration
4. **Performance issues**: Review database indexes

### Debug Commands
```bash
# Check driver lock status
python manage.py shell -c "from akuko_api.models import Driver; print(Driver.objects.get(id=123).is_account_locked())"

# List active flags for driver
python manage.py shell -c "from akuko_api.models import DriverFlag; print(DriverFlag.objects.filter(driver_id=123, is_active=True))"

# Unlock driver manually
python manage.py shell -c "from akuko_api.models import Driver; Driver.objects.get(id=123).unlock_account('Manual unlock')"
```

## Future Enhancements

### Planned Features
- Appeal system for drivers
- Automated flag expiry
- Machine learning for fraud detection
- Integration with external verification services
- Mobile app notifications

### API Versioning
- Current version: v1
- Backward compatibility maintained
- Deprecation notices for old endpoints

## Support and Maintenance

### Contact Information
- Technical Support: <EMAIL>
- API Documentation: /api/docs/
- Status Page: status.kadereconnect.com

### Maintenance Schedule
- Regular database cleanup of old flags
- Monthly performance reviews
- Quarterly security audits
