info:
  description: ''
  title: ''
  version: ''
openapi: 3.0.0
paths:
  /api/applications/:
    get:
      operationId: applications_list
      tags:
      - applications
    post:
      operationId: applications_create
      tags:
      - applications
  /api/applications/{id}/:
    get:
      operationId: applications_read
      parameters:
      - in: path
        name: id
        required: true
        schema:
          description: A unique integer value identifying this job application.
          title: ID
          type: integer
      tags:
      - applications
    patch:
      operationId: applications_partial_update
      parameters:
      - in: path
        name: id
        required: true
        schema:
          description: A unique integer value identifying this job application.
          title: ID
          type: integer
      tags:
      - applications
    put:
      operationId: applications_update
      parameters:
      - in: path
        name: id
        required: true
        schema:
          description: A unique integer value identifying this job application.
          title: ID
          type: integer
      tags:
      - applications
  /api/applications/{id}/remove_by_partner/:
    post:
      operationId: applications_remove_by_partner_0
      parameters:
      - in: path
        name: id
        required: true
        schema:
          description: A unique integer value identifying this job application.
          title: ID
          type: integer
      tags:
      - applications
  /api/applications/{id}/withdraw_by_driver/:
    post:
      operationId: applications_withdraw_by_driver_0
      parameters:
      - in: path
        name: id
        required: true
        schema:
          description: A unique integer value identifying this job application.
          title: ID
          type: integer
      tags:
      - applications
  /api/drivers/:
    get:
      operationId: drivers_list
      tags:
      - drivers
    post:
      operationId: drivers_create
      tags:
      - drivers
  /api/drivers/{id}/:
    delete:
      operationId: drivers_delete
      parameters:
      - in: path
        name: id
        required: true
        schema:
          description: A unique integer value identifying this driver.
          title: ID
          type: integer
      tags:
      - drivers
    get:
      operationId: drivers_read
      parameters:
      - in: path
        name: id
        required: true
        schema:
          description: A unique integer value identifying this driver.
          title: ID
          type: integer
      tags:
      - drivers
    patch:
      operationId: drivers_partial_update
      parameters:
      - in: path
        name: id
        required: true
        schema:
          description: A unique integer value identifying this driver.
          title: ID
          type: integer
      tags:
      - drivers
    put:
      operationId: drivers_update
      parameters:
      - in: path
        name: id
        required: true
        schema:
          description: A unique integer value identifying this driver.
          title: ID
          type: integer
      tags:
      - drivers
  /api/jobs/:
    get:
      operationId: jobs_list
      tags:
      - jobs
    post:
      operationId: jobs_create
      tags:
      - jobs
  /api/jobs/{id}/:
    delete:
      operationId: jobs_delete
      parameters:
      - in: path
        name: id
        required: true
        schema:
          description: ''
          title: ''
          type: string
      tags:
      - jobs
    get:
      operationId: jobs_read
      parameters:
      - in: path
        name: id
        required: true
        schema:
          description: ''
          title: ''
          type: string
      tags:
      - jobs
    patch:
      operationId: jobs_partial_update
      parameters:
      - in: path
        name: id
        required: true
        schema:
          description: ''
          title: ''
          type: string
      tags:
      - jobs
    put:
      operationId: jobs_update
      parameters:
      - in: path
        name: id
        required: true
        schema:
          description: ''
          title: ''
          type: string
      tags:
      - jobs
  /api/jobs/{id}/close_job/:
    post:
      operationId: jobs_close_job
      parameters:
      - in: path
        name: id
        required: true
        schema:
          description: ''
          title: ''
          type: string
      tags:
      - jobs
  /api/partners/:
    get:
      operationId: partners_list
      tags:
      - partners
    post:
      operationId: partners_create
      tags:
      - partners
  /api/partners/{id}/:
    delete:
      operationId: partners_delete
      parameters:
      - in: path
        name: id
        required: true
        schema:
          description: A unique integer value identifying this partner.
          title: ID
          type: integer
      tags:
      - partners
    get:
      operationId: partners_read
      parameters:
      - in: path
        name: id
        required: true
        schema:
          description: A unique integer value identifying this partner.
          title: ID
          type: integer
      tags:
      - partners
    patch:
      operationId: partners_partial_update
      parameters:
      - in: path
        name: id
        required: true
        schema:
          description: A unique integer value identifying this partner.
          title: ID
          type: integer
      tags:
      - partners
    put:
      operationId: partners_update
      parameters:
      - in: path
        name: id
        required: true
        schema:
          description: A unique integer value identifying this partner.
          title: ID
          type: integer
      tags:
      - partners
  /api/password-reset-confirm/:
    post:
      operationId: password-reset-confirm_create
      tags:
      - password-reset-confirm
  /api/password-reset/:
    post:
      operationId: password-reset_create
      tags:
      - password-reset
  /api/test-email/:
    post:
      operationId: test-email_create
      tags:
      - test-email
  /api/token/:
    post:
      operationId: token_create
      tags:
      - token
  /api/vehicle-makes/:
    get:
      operationId: vehicle-makes_list
      tags:
      - vehicle-makes
  /api/vehicle-makes/{id}/:
    get:
      operationId: vehicle-makes_read
      parameters:
      - in: path
        name: id
        required: true
        schema:
          description: A unique integer value identifying this vehicle make.
          title: ID
          type: integer
      tags:
      - vehicle-makes
  /api/vehicle-models/:
    get:
      operationId: vehicle-models_list
      tags:
      - vehicle-models
  /api/vehicle-models/{id}/:
    get:
      operationId: vehicle-models_read
      parameters:
      - in: path
        name: id
        required: true
        schema:
          description: ''
          title: ''
          type: string
      tags:
      - vehicle-models
  /api/vehicle-types/:
    get:
      operationId: vehicle-types_list
      tags:
      - vehicle-types
    post:
      operationId: vehicle-types_create
      tags:
      - vehicle-types
  /api/vehicle-types/{id}/:
    delete:
      operationId: vehicle-types_delete
      parameters:
      - in: path
        name: id
        required: true
        schema:
          description: A unique integer value identifying this vehicle type.
          title: ID
          type: integer
      tags:
      - vehicle-types
    get:
      operationId: vehicle-types_read
      parameters:
      - in: path
        name: id
        required: true
        schema:
          description: A unique integer value identifying this vehicle type.
          title: ID
          type: integer
      tags:
      - vehicle-types
    patch:
      operationId: vehicle-types_partial_update
      parameters:
      - in: path
        name: id
        required: true
        schema:
          description: A unique integer value identifying this vehicle type.
          title: ID
          type: integer
      tags:
      - vehicle-types
    put:
      operationId: vehicle-types_update
      parameters:
      - in: path
        name: id
        required: true
        schema:
          description: A unique integer value identifying this vehicle type.
          title: ID
          type: integer
      tags:
      - vehicle-types
  /api/verify-email/{verification_code}/:
    get:
      operationId: verify-email_read
      parameters:
      - in: path
        name: verification_code
        required: true
        schema:
          description: ''
          title: ''
          type: string
      tags:
      - verify-email
  /api/work-areas/:
    get:
      operationId: work-areas_list
      tags:
      - work-areas
    post:
      operationId: work-areas_create
      tags:
      - work-areas
  /api/work-areas/{id}/:
    delete:
      operationId: work-areas_delete
      parameters:
      - in: path
        name: id
        required: true
        schema:
          description: A unique integer value identifying this work area.
          title: ID
          type: integer
      tags:
      - work-areas
    get:
      operationId: work-areas_read
      parameters:
      - in: path
        name: id
        required: true
        schema:
          description: A unique integer value identifying this work area.
          title: ID
          type: integer
      tags:
      - work-areas
    patch:
      operationId: work-areas_partial_update
      parameters:
      - in: path
        name: id
        required: true
        schema:
          description: A unique integer value identifying this work area.
          title: ID
          type: integer
      tags:
      - work-areas
    put:
      operationId: work-areas_update
      parameters:
      - in: path
        name: id
        required: true
        schema:
          description: A unique integer value identifying this work area.
          title: ID
          type: integer
      tags:
      - work-areas
servers:
- url: ''
