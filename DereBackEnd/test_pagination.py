#!/usr/bin/env python
"""
Test script to verify pagination implementation for reports.
This script tests the pagination functionality of the report views.
"""

import os
import sys
import django
from django.conf import settings

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'akukoproject.settings')
django.setup()

# Add testserver to ALLOWED_HOSTS for testing
from django.conf import settings
if 'testserver' not in settings.ALLOWED_HOSTS:
    settings.ALLOWED_HOSTS.append('testserver')

from rest_framework.test import APIClient
from django.contrib.auth import get_user_model
from akuko_api.models import Partner, Vehicle, Revenue, Expenditure, VehicleMake, VehicleModel, WorkArea
from decimal import Decimal
from datetime import date, timedelta

User = get_user_model()

def create_test_data():
    """Create test data for pagination testing."""
    print("Creating test data...")
    
    # Create a test user and partner
    user, created = User.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'first_name': 'Test',
            'last_name': 'User',
            'role': 'partner',
            'email_verified': True
        }
    )
    
    partner, created = Partner.objects.get_or_create(
        user=user,
        defaults={
            'id_number': '12345678',
            'company_number': 'TEST123',
            'company_name': 'Test Company',
            'mobile_number': '254700000000'
        }
    )
    
    # Create vehicle make, model, and work area
    make, created = VehicleMake.objects.get_or_create(name='Toyota')
    model, created = VehicleModel.objects.get_or_create(name='Corolla', make=make)
    work_area = WorkArea.objects.filter(name='Nairobi').first()
    if not work_area:
        work_area = WorkArea.objects.create(name='Nairobi')
    
    # Create test vehicles
    vehicles = []
    for i in range(3):
        vehicle, created = Vehicle.objects.get_or_create(
            registration_number=f'TEST{i+1}',
            defaults={
                'partner': partner,
                'vehicle_make': make,
                'vehicle_model': model,
                'year_of_manufacture': 2020,
                'status': 'active',
                'work_days': 'Monday-Friday',
                'preferred_work_area': work_area
            }
        )
        vehicles.append(vehicle)
    
    # Create test revenue and expenditure records (more than 10 to test pagination)
    today = date.today()
    for i in range(15):
        test_date = today - timedelta(days=i)
        vehicle = vehicles[i % len(vehicles)]
        
        # Create revenue
        Revenue.objects.get_or_create(
            vehicle=vehicle,
            date=test_date,
            defaults={
                'amount': Decimal('1000.00') + Decimal(str(i * 10)),
                'confirmation_message': f'Test payment {i+1}',
                'deleted': False
            }
        )
        
        # Create expenditure
        Expenditure.objects.get_or_create(
            vehicle=vehicle,
            date=test_date,
            defaults={
                'partner': partner,
                'amount': Decimal('500.00') + Decimal(str(i * 5)),
                'item_name': 'Fuel',
                'description': f'Test expenditure {i+1}',
                'quantity': 1,
                'deleted': False
            }
        )
    
    print(f"Created test data: {len(vehicles)} vehicles, 15 revenue records, 15 expenditure records")
    return user, partner, vehicles

def test_financial_report_pagination():
    """Test FinancialReportView pagination."""
    print("\n=== Testing FinancialReportView Pagination ===")

    user, partner, vehicles = create_test_data()
    client = APIClient()
    client.force_authenticate(user=user)

    # Test first page
    response = client.get('/api/financial-report/?page=1&page_size=5')

    print(f"Status Code: {response.status_code}")

    if response.status_code == 200:
        data = response.data
        print(f"Total Count: {data.get('count', 'N/A')}")
        print(f"Total Pages: {data.get('total_pages', 'N/A')}")
        print(f"Current Page: {data.get('current_page', 'N/A')}")
        print(f"Page Size: {data.get('page_size', 'N/A')}")
        print(f"Records in this page: {len(data.get('detailed_report', []))}")
        print(f"Has Next: {'Yes' if data.get('next') else 'No'}")
        print(f"Has Previous: {'Yes' if data.get('previous') else 'No'}")

        # Test second page
        response2 = client.get('/api/financial-report/?page=2&page_size=5')

        if response2.status_code == 200:
            data2 = response2.data
            print(f"\nSecond page - Current Page: {data2.get('current_page', 'N/A')}")
            print(f"Second page - Records: {len(data2.get('detailed_report', []))}")

        return True
    else:
        print(f"Error: {response.status_code} - {response.content}")
        return False

def test_profit_report_pagination():
    """Test ProfitReportView pagination."""
    print("\n=== Testing ProfitReportView Pagination ===")

    user, partner, vehicles = create_test_data()
    client = APIClient()
    client.force_authenticate(user=user)

    response = client.get('/api/profit-report/?page=1&page_size=2')

    print(f"Status Code: {response.status_code}")

    if response.status_code == 200:
        data = response.data
        print(f"Total Count: {data.get('count', 'N/A')}")
        print(f"Total Pages: {data.get('total_pages', 'N/A')}")
        print(f"Current Page: {data.get('current_page', 'N/A')}")
        print(f"Records in this page: {len(data.get('results', []))}")
        return True
    else:
        print(f"Error: {response.status_code} - {response.content}")
        return False

def test_daily_payment_report_pagination():
    """Test DailyPaymentReportView pagination."""
    print("\n=== Testing DailyPaymentReportView Pagination ===")

    user, partner, vehicles = create_test_data()
    client = APIClient()
    client.force_authenticate(user=user)

    response = client.get('/api/daily-payment-report/?page=1&page_size=2')

    print(f"Status Code: {response.status_code}")

    if response.status_code == 200:
        data = response.data
        print(f"Total Count: {data.get('count', 'N/A')}")
        print(f"Total Pages: {data.get('total_pages', 'N/A')}")
        print(f"Current Page: {data.get('current_page', 'N/A')}")
        print(f"Records in this page: {len(data.get('results', []))}")
        return True
    else:
        print(f"Error: {response.status_code} - {response.content}")
        return False

def main():
    """Run all pagination tests."""
    print("Starting Pagination Tests...")
    
    try:
        # Test all report views
        financial_test = test_financial_report_pagination()
        profit_test = test_profit_report_pagination()
        daily_payment_test = test_daily_payment_report_pagination()
        
        print("\n=== Test Results ===")
        print(f"FinancialReportView: {'PASS' if financial_test else 'FAIL'}")
        print(f"ProfitReportView: {'PASS' if profit_test else 'FAIL'}")
        print(f"DailyPaymentReportView: {'PASS' if daily_payment_test else 'FAIL'}")
        
        if all([financial_test, profit_test, daily_payment_test]):
            print("\n✅ All pagination tests PASSED!")
            return True
        else:
            print("\n❌ Some pagination tests FAILED!")
            return False
            
    except Exception as e:
        print(f"\n❌ Test execution failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
