from rest_framework import serializers
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from django.utils import timezone
from .models import JobApplication, Driver, NotificationSubscriber, Partner, User, Job, VehicleMake, VehicleModel, WorkArea, VehicleType, PasswordResetToken, Vehicle, Notification, Expenditure, Revenue, PaymentSettings, PaymentTransaction, Subscription, DriverRating, DriverFlag
from django.core.mail import send_mail
from django.conf import settings
from django.contrib.auth.tokens import default_token_generator
from django.utils.encoding import force_bytes
from django.utils.http import urlsafe_base64_encode, urlsafe_base64_decode
from django.conf import settings
# from django.contrib.auth.models import User
from django.utils.crypto import get_random_string
import random
import string
import logging
from .email_templates import (
    get_verification_email_template,
    get_driver_added_email_template,
    get_driver_hired_email_template,
    get_driver_rejected_email_template,
    get_password_reset_email_template,
    get_subscription_success_email_template
)


def generate_random_password(length=8):
    """Generate a random password."""
    characters = string.ascii_letters + string.digits
    return ''.join(random.choice(characters) for _ in range(length))


# email verification Serializer
def send_verification_email(user):
    """Send an email verification link to the user."""
    subject = "Verify your email"
    html_message = get_verification_email_template(user)

    # Plain text fallback
    role = 'partner' if user.role == 'partner' else 'driver'
    verification_link = f"{settings.FRONTEND_URL}/verify-email/{user.verification_code}/?role={role}"
    plain_message = f"Hi {user.first_name},\n\nPlease verify your email by clicking on the link below:\n{verification_link}"

    send_mail(
        subject=subject,
        message=plain_message,
        html_message=html_message,
        from_email=settings.DEFAULT_FROM_EMAIL,
        recipient_list=[user.email]
    )


def send_driver_added_email(driver, partner_name, password):
    """Send an email to the driver when added by a partner."""
    subject = "You've been added as a driver"
    html_message = get_driver_added_email_template(driver, partner_name, password)

    # Plain text fallback
    plain_message = f"""
    Hi {driver.first_name},

    You have been added as a driver by {partner_name}.

    Your login email: {driver.email}
    Your temporary password: {password}

    Please confirm your email and change your password after logging in.

    Click the link below to verify your email:
    {settings.FRONTEND_URL}/verify-email/{driver.user.verification_code}/?role=driver
    """

    send_mail(
        subject=subject,
        message=plain_message,
        html_message=html_message,
        from_email=settings.DEFAULT_FROM_EMAIL,
        recipient_list=[driver.email]
    )


def send_driver_hired_email(driver, partner, job):
    """Send email notification when a driver is hired."""
    subject = "Congratulations! You've been hired"
    html_message = get_driver_hired_email_template(driver, partner, job)

    # Plain text fallback
    plain_message = f"""
    Hi {driver.user.first_name},

    Congratulations! You have been hired by {partner.user.first_name} {partner.user.last_name} for the following job:

    Job Details:
    - Vehicle: {job.vehicle_make.name} {job.vehicle_model.name}
    - Work Area: {job.preferred_work_area.name}

    Please contact your employer for further instructions.

    Best regards,
    The KadereConnect Team
    """

    try:
        # Create notification record
        notification = Notification.objects.create(
            subject=subject,
            type="email",
            recipient=driver.user.email,
            status="pending",
            message=html_message
        )

        # Send email
        send_mail(
            subject=subject,
            message=plain_message,
            html_message=html_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[driver.user.email]
        )
        notification.mark_as_sent()

        # Log success
        logger = logging.getLogger(__name__)
        logger.info(f"Hire notification email sent to {driver.user.email}")

    except Exception as e:
        # Log error
        logger = logging.getLogger(__name__)
        logger.error(f"Failed to send hire notification email: {str(e)}")

        if 'notification' in locals():
            notification.mark_as_failed()


def send_driver_rejected_email(driver, partner, job):
    """Send email notification when a driver's application is rejected."""
    subject = "Update on your job application"
    html_message = get_driver_rejected_email_template(driver, partner, job)

    # Plain text fallback
    plain_message = f"""
    Hi {driver.user.first_name},

    We regret to inform you that your application for the job with {partner.user.first_name} {partner.user.last_name} has been rejected.

    Job Details:
    - Vehicle: {job.vehicle_make.name} {job.vehicle_model.name}
    - Work Area: {job.preferred_work_area.name}

    Keep applying! There are many other opportunities available.

    Best regards,
    The KadereConnect Team
    """

    try:
        # Create notification record
        notification = Notification.objects.create(
            subject=subject,
            type="email",
            recipient=driver.user.email,
            status="pending",
            message=html_message
        )

        # Send email
        send_mail(
            subject=subject,
            message=plain_message,
            html_message=html_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[driver.user.email]
        )
        notification.mark_as_sent()

        # Log success
        logger = logging.getLogger(__name__)
        logger.info(f"Rejection notification email sent to {driver.user.email}")

    except Exception as e:
        # Log error
        logger = logging.getLogger(__name__)
        logger.error(f"Failed to send rejection notification email: {str(e)}")

        if 'notification' in locals():
            notification.mark_as_failed()


# # login user view serializer
class CustomTokenObtainPairSerializer(TokenObtainPairSerializer):
    role = serializers.CharField()

    def validate(self, attrs):
        email = attrs.get('email').lower()
        password = attrs.get('password')
        role = attrs.get('role')

        try:
            user = User.objects.get(email__iexact=email)
        except User.DoesNotExist:
            raise serializers.ValidationError("No user found with this email")

        # Check if password is correct
        if not user.check_password(password):
            raise serializers.ValidationError("Incorrect password!")

        # Check if the role matches the user role
        if user.role != role:
            raise serializers.ValidationError("Role does not match the email!")

        # Check if email is verified
        if not user.email_verified:
            raise serializers.ValidationError(
                "Email not verified. A verification link was sent to your email. Please check your inbox!")

        # Check if driver account is locked (for driver users)
        if user.role == 'driver':
            try:
                driver = user.driver
                if driver.is_account_locked():
                    if driver.lock_type == 'permanent':
                        raise serializers.ValidationError(
                            "Your account has been permanently banned due to partner flagging. Please contact support if you believe this is an error."
                        )
                    elif driver.lock_type == 'temporary':
                        lock_expires = driver.lock_expires_at.strftime('%B %d, %Y') if driver.lock_expires_at else 'N/A'
                        raise serializers.ValidationError(
                            f"Your account has been temporarily locked until {lock_expires} due to partner flagging. Please contact support if you believe this is an error."
                        )
                    else:
                        raise serializers.ValidationError(
                            "Your account has been locked. Please contact support."
                        )
            except Driver.DoesNotExist:
                # If driver profile doesn't exist, allow login (they can create profile later)
                pass

        # If all checks pass, proceed to return the tokens
        data = super().validate(attrs)
        data['role'] = user.role
        # Pass email_verified status in the response for frontend if needed
        data['email_verified'] = user.email_verified

        return data


# Driver Flag Serializer
class DriverFlagSerializer(serializers.ModelSerializer):
    driver_name = serializers.SerializerMethodField()
    partner_name = serializers.SerializerMethodField()
    flagged_by_name = serializers.SerializerMethodField()
    resolved_by_name = serializers.SerializerMethodField()
    days_since_flagged = serializers.SerializerMethodField()

    class Meta:
        model = DriverFlag
        fields = [
            'id', 'driver', 'driver_name', 'partner', 'partner_name',
            'reason', 'created_at', 'is_active', 'flagged_by_user',
            'flagged_by_name', 'resolved_at', 'resolved_by', 'resolved_by_name',
            'resolution_notes', 'days_since_flagged'
        ]
        read_only_fields = ['created_at', 'resolved_at']

    def get_driver_name(self, obj):
        return f"{obj.driver.first_name} {obj.driver.last_name}".strip()

    def get_partner_name(self, obj):
        return obj.partner.company_name

    def get_flagged_by_name(self, obj):
        if obj.flagged_by_user:
            return f"{obj.flagged_by_user.first_name} {obj.flagged_by_user.last_name}".strip()
        return "Unknown"

    def get_resolved_by_name(self, obj):
        if obj.resolved_by:
            return f"{obj.resolved_by.first_name} {obj.resolved_by.last_name}".strip()
        return None

    def get_days_since_flagged(self, obj):
        from django.utils import timezone
        return (timezone.now() - obj.created_at).days


# user serializer
class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['id', 'email', 'role', 'first_name',
                  'last_name', "email_verified", "notification_subscribe"]

# Vehicle Type serializer
class VehicleTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = VehicleType
        fields = '__all__'


# Vehicle Make serializer
class VehicleMakeSerializer(serializers.ModelSerializer):
    class Meta:
        model = VehicleMake
        fields = ['id', 'name']

# Vehicle Model serializer
class VehicleModelSerializer(serializers.ModelSerializer):
    make = VehicleMakeSerializer()

    class Meta:
        model = VehicleModel
        fields = ['id', 'name', 'make']

# Work Area serializer
class WorkAreaSerializer(serializers.ModelSerializer):
    class Meta:
        model = WorkArea
        fields = ['id', 'name']


# Job serializer
class JobSerializer(serializers.ModelSerializer):
    vehicle_make = serializers.PrimaryKeyRelatedField(
        queryset=VehicleMake.objects.all())
    vehicle_model = serializers.PrimaryKeyRelatedField(
        queryset=VehicleModel.objects.all())
    preferred_work_area = serializers.PrimaryKeyRelatedField(
        queryset=WorkArea.objects.all())

    class Meta:
        model = Job
        fields = ['id', 'vehicle_make', 'vehicle_model', 'preferred_work_area',
                  'requirements', 'vehicle_photo', 'work_days', 'min_age', 'max_age', 'status']

    def validate(self, data):
        if data.get('min_age') is not None and data['min_age'] < 18:
            raise serializers.ValidationError(
                "Minimum age must be at least 18.")

        if data.get('min_age') and data.get('max_age') and data['min_age'] > data['max_age']:
            raise serializers.ValidationError(
                "Min age cannot be greater than max age.")

        return data


#  DriverSerializer
class DriverSerializer(serializers.ModelSerializer):
    confirm_password = serializers.CharField(write_only=True, required=False)
    work_area = serializers.PrimaryKeyRelatedField(
        queryset=WorkArea.objects.all(), required=False, allow_null=True)
    vehicle_type = serializers.PrimaryKeyRelatedField(
        queryset=VehicleType.objects.all(), required=False, allow_null=True)

    class Meta:
        model = Driver
        fields = [
            'id', 'first_name', 'last_name', 'email', 'id_number', 'partner', 'date_of_birth',
            'gender', 'mobile_number', 'password', 'confirm_password',
            'vehicle_type', 'work_area', 'psv_photo', 'id_photo',
            'license_photo', 'good_conduct_photo', 'uber_trips_screenshot',
            'license_expiry_date', 'psv_expiry_date', 'good_conduct_expiry_date'
        ]
        extra_kwargs = {
            'password': {'write_only': True, 'required': False},
            'confirm_password': {'write_only': True, 'required': False}
        }

    def validate(self, data):
        if self.instance:  # When updating
            if 'email' in data and User.objects.filter(email=data['email']).exclude(pk=self.instance.pk).exists():
                raise serializers.ValidationError(
                    "A user with this email already exists.")
        else:  # When creating
            if User.objects.filter(email=data.get('email')).exists():
                raise serializers.ValidationError(
                    "A user with this email already exists.")

            if not data.get('partner'):  # Self-registration case
                if not data.get('password') or not data.get('confirm_password'):
                    raise serializers.ValidationError(
                        "Password and Confirm Password are required.")
                if data['password'] != data['confirm_password']:
                    raise serializers.ValidationError(
                        "Passwords do not match.")

        return data

    def validate_uber_trips_screenshot(self, value):
        """Validate uber trips screenshot file"""
        if value:
            from .utils import validate_file_extension, validate_file_size
            validate_file_extension(value)
            validate_file_size(value)
        return value

    def validate_license_expiry_date(self, value):
        """Validate license expiry date"""
        if value:
            from django.utils import timezone
            if value < timezone.now().date():
                raise serializers.ValidationError(
                    "License expiry date cannot be in the past."
                )
        return value

    def validate_psv_expiry_date(self, value):
        """Validate PSV license expiry date"""
        if value:
            from django.utils import timezone
            if value < timezone.now().date():
                raise serializers.ValidationError(
                    "PSV license expiry date cannot be in the past."
                )
        return value

    def validate_good_conduct_expiry_date(self, value):
        """Validate good conduct certificate expiry date"""
        if value:
            from django.utils import timezone
            if value < timezone.now().date():
                raise serializers.ValidationError(
                    "Good conduct certificate expiry date cannot be in the past."
                )
        return value

    def create(self, validated_data):
        # confirmed_password = validated_data.pop('confirm_password', None)
        partner = validated_data.get('partner')

        # If added by a partner, generate a random password
        if partner:
            password = generate_random_password()
        else:
            password = validated_data['password']

        user = User.objects.create(
            email=validated_data['email'],
            first_name=validated_data['first_name'],
            last_name=validated_data['last_name'],
            role='driver',
            date_joined=timezone.now()
        )
        user.set_password(password)
        user.save()

        driver = Driver.objects.create(user=user, **validated_data)

        if partner:
            # Create notification record
            notification = Notification.objects.create(
                subject="You've been added as a driver",
                type="email",
                recipient=driver.email,
                status="pending",
                message=f"Hi {driver.first_name},\n\nYou have been added as a driver by {partner.first_name} {partner.last_name}. Your login email: {driver.email}. Your temporary password: {password}.\n\nPlease confirm your email and change your password after logging in.\n\nClick the link below to verify your email:\n{settings.FRONTEND_URL}/verify-email/{user.verification_code}/?role=driver"
            )

            # Send email
            try:
                send_driver_added_email(
                    driver, partner.first_name + " " + partner.last_name, password)
                notification.mark_as_sent()
            except Exception as e:
                notification.mark_as_failed()
                print(f"Failed to send driver added email: {str(e)}")
        else:
            # Create notification record
            notification = Notification.objects.create(
                subject="Verify your email",
                type="email",
                recipient=user.email,
                status="pending",
                message=f"Hi {user.first_name},\n\nPlease verify your email by clicking on the link below:\n{settings.FRONTEND_URL}/verify-email/{user.verification_code}/?role=driver"
            )

            # Send verification email
            try:
                send_verification_email(user)
                notification.mark_as_sent()
            except Exception as e:
                notification.mark_as_failed()
                print(f"Failed to send verification email: {str(e)}")

        return driver

    def update(self, instance, validated_data):
        user = instance.user

        if 'email' in validated_data:
            user.email = validated_data['email']
        if 'first_name' in validated_data:
            user.first_name = validated_data['first_name']
        if 'last_name' in validated_data:
            user.last_name = validated_data['last_name']

        if 'password' in validated_data:
            user.set_password(validated_data['password'])

        user.save()

        for attr, value in validated_data.items():
            setattr(instance, attr, value)

        instance.save()
        return instance


# PartnerSerializer
class PartnerSerializer(serializers.ModelSerializer):
    confirm_password = serializers.CharField(write_only=True, required=False)

    class Meta:
        model = Partner
        fields = [
            'id', 'first_name', 'last_name', 'email', 'id_number', 'company_number',
            'company_name', 'password', 'confirm_password', 'mobile_number', 'id_photo'
        ]
        extra_kwargs = {
            'password': {'write_only': True, 'required': False},
            'confirm_password': {'write_only': True, 'required': False}
        }

    def validate(self, data):
        if self.instance:
            if 'email' in data and User.objects.filter(email=data['email']).exclude(pk=self.instance.pk).exists():
                raise serializers.ValidationError(
                    "A user with this email already exists.")
        else:  #
            if User.objects.filter(email=data.get('email')).exists():
                raise serializers.ValidationError(
                    "A user with this email already exists.")

            if data['password'] != data['confirm_password']:
                raise serializers.ValidationError("Passwords do not match.")

        return data

    def create(self, validated_data):
        validated_data.pop('confirm_password', None)

        user = User.objects.create(
            email=validated_data['email'],
            first_name=validated_data['first_name'],
            last_name=validated_data['last_name'],
            role='partner',
            date_joined=timezone.now()
        )
        user.set_password(validated_data['password'])
        user.save()

        partner = Partner.objects.create(
            user=user,
            **validated_data
        )

        # Create notification record
        notification = Notification.objects.create(
            subject="Verify your email",
            type="email",
            recipient=user.email,
            status="pending",
            message=f"Hi {user.first_name},\n\nPlease verify your email by clicking on the link below:\n{settings.FRONTEND_URL}/verify-email/{user.verification_code}/?role=partner"
        )

        # Send verification email
        try:
            send_verification_email(user)
            notification.mark_as_sent()
        except Exception as e:
            notification.mark_as_failed()
            print(f"Failed to send verification email: {str(e)}")

        return partner

    def update(self, instance, validated_data):
        user = instance.user

        if 'email' in validated_data:
            user.email = validated_data['email']
        if 'first_name' in validated_data:
            user.first_name = validated_data['first_name']
        if 'last_name' in validated_data:
            user.last_name = validated_data['last_name']

        if 'password' in validated_data:
            user.set_password(validated_data['password'])

        user.save()

        # Update the partner-specific fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)

        instance.save()
        return instance

# Job Application Serializer
class JobApplicationSerializer(serializers.ModelSerializer):
    driver = DriverSerializer(read_only=True)
    job = JobSerializer(read_only=True)

    # Custom method to display the correct status
    status_display = serializers.SerializerMethodField()

    # Flagging information for partners
    driver_flag_info = serializers.SerializerMethodField()

    class Meta:
        model = JobApplication
        fields = ['id', 'job', 'driver', 'status', 'status_display',
                  'applied_at', 'removed_by_partner', 'withdrawn_by_driver', 'driver_flag_info']
        read_only_fields = ['applied_at', 'job', 'driver']

    def get_status_display(self, obj):
        """
        Dynamically display the correct status:
        - 'Rejected' when removed_by_partner is True
        - 'Withdrawn' when withdrawn_by_driver is True
        """
        if obj.withdrawn_by_driver:
            return 'Withdrawn'
        if obj.removed_by_partner:
            return 'Rejected'
        return obj.status

    def get_driver_flag_info(self, obj):
        """
        Get flagging information for the driver (only visible to partners and admins)
        """
        request = self.context.get('request')
        if not request or not request.user.is_authenticated:
            return None

        # Only show flag info to partners and admins
        if request.user.role not in ['partner', 'admin']:
            return None

        try:
            from .models import DriverFlag
            from datetime import timedelta
            from django.utils import timezone

            driver = obj.driver

            # Get active flags within last 6 months
            cutoff_date = timezone.now() - timedelta(days=180)
            active_flags = DriverFlag.objects.filter(
                driver=driver,
                is_active=True,
                created_at__gte=cutoff_date
            ).select_related('partner')

            if not active_flags.exists():
                return {
                    'is_flagged': False,
                    'total_flags': 0,
                    'unique_partners_flagged': 0,
                    'is_locked': driver.is_account_locked() if hasattr(driver, 'is_account_locked') else False
                }

            # Prepare flag details
            flag_details = []
            for flag in active_flags[:3]:  # Show only recent 3 flags
                flag_info = {
                    'reason': flag.reason[:100] + '...' if len(flag.reason) > 100 else flag.reason,
                    'flagged_at': flag.created_at.isoformat(),
                    'days_ago': (timezone.now() - flag.created_at).days
                }

                # Only show partner name to admins
                if request.user.role == 'admin':
                    flag_info['partner_name'] = flag.partner.company_name
                else:
                    flag_info['partner_name'] = 'Anonymous Partner'

                flag_details.append(flag_info)

            total_flags = active_flags.count()
            unique_partners = DriverFlag.get_unique_partners_flagged_count(driver)

            return {
                'is_flagged': True,
                'total_flags': total_flags,
                'unique_partners_flagged': unique_partners,
                'is_locked': driver.is_account_locked() if hasattr(driver, 'is_account_locked') else False,
                'lock_type': getattr(driver, 'lock_type', None),
                'recent_flags': flag_details,
                'warning_level': self._get_flag_warning_level(unique_partners)
            }

        except Exception as e:
            # Return basic info if there's an error
            return {
                'is_flagged': False,
                'total_flags': 0,
                'unique_partners_flagged': 0,
                'is_locked': False,
                'error': 'Unable to load flag information'
            }

    def _get_flag_warning_level(self, unique_partners_count):
        """Get warning level based on number of unique partners who flagged"""
        if unique_partners_count >= 3:
            return 'critical'  # Permanent ban
        elif unique_partners_count >= 2:
            return 'high'      # Temporary lock
        elif unique_partners_count >= 1:
            return 'medium'    # Flagged but not locked
        else:
            return 'low'       # No flags


# Forgot password
class PasswordResetSerializer(serializers.Serializer):
    email = serializers.EmailField()

    def validate_email(self, value):
        try:
            # Check if user exists with this email
            User.objects.get(email=value)
        except User.DoesNotExist:
            raise serializers.ValidationError(
                "No user found with this email address.")
        return value

    def send_reset_email(self, user):
        # Generate a token
        token = get_random_string(length=32)
        PasswordResetToken.objects.create(
            user=user, token=token)  # Save the token
        print(f"Token sent to {user.email}: {token}")

        uid = urlsafe_base64_encode(force_bytes(user.pk))
        reset_link = f"{settings.FRONTEND_URL}/reset-password/{uid}/{token}"

        # Get HTML email template
        html_message = get_password_reset_email_template(user, reset_link)

        # Plain text fallback
        plain_message = f"Hi {user.first_name},\n\nClick the link below to reset your password:\n{reset_link}\n\nIf you did not request a password reset, please ignore this email."

        # Create notification record
        notification = Notification.objects.create(
            subject="Password Reset Request",
            type="email",
            recipient=user.email,
            status="pending",
            message=html_message
        )

        # Send email
        try:
            send_mail(
                subject="Password Reset Request",
                message=plain_message,
                html_message=html_message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[user.email],
            )
            notification.mark_as_sent()
        except Exception as e:
            notification.mark_as_failed()
            print(f"Failed to send password reset email: {str(e)}")

    def save(self):
        email = self.validated_data['email']
        user = User.objects.get(email=email)
        self.send_reset_email(user)

# confirm password
class PasswordResetConfirmSerializer(serializers.Serializer):
    uid = serializers.CharField()
    token = serializers.CharField()
    password = serializers.CharField(write_only=True)
    confirm_password = serializers.CharField(write_only=True)

    def validate(self, data):
        # Validate passwords match
        if data['password'] != data['confirm_password']:
            raise serializers.ValidationError("Passwords do not match.")

        # Decode UID and get user
        try:
            uid = urlsafe_base64_decode(data['uid']).decode()  # Decode the UID
            user = User.objects.get(pk=uid)  # Get user from UID
            data['user'] = user
        except (TypeError, ValueError, OverflowError, User.DoesNotExist):
            raise serializers.ValidationError("Invalid UID.")

        # Validate token
        try:
            reset_token = PasswordResetToken.objects.get(
                user=user, token=data['token'])
            if reset_token.is_expired():
                raise serializers.ValidationError("Expired token.")
        except PasswordResetToken.DoesNotExist:
            raise serializers.ValidationError("Invalid token.")

        return data

    def save(self):
        user = self.validated_data['user']
        password = self.validated_data['password']
        user.set_password(password)
        user.save()

# Vehicle Serializer
class VehicleSerializer(serializers.ModelSerializer):
    driver = DriverSerializer(read_only=True)
    partner = PartnerSerializer(read_only=True)
    registration_number = serializers.CharField(required=False, allow_blank=True)
    work_days = serializers.CharField(required=False, allow_blank=True)

    class Meta:
        model = Vehicle
        fields = '__all__'

    # def validate_vehicle_expiry_date(self, value):
    #     """Validate vehicle registration expiry date"""
    #     if value:
    #         from django.utils import timezone
    #         if value < timezone.now().date():
    #             raise serializers.ValidationError(
    #                 "Vehicle registration expiry date cannot be in the past."
    #             )
    #     return value

    def validate_ntsa_expiry_date(self, value):
        """Validate NTSA inspection expiry date"""
        if value:
            from django.utils import timezone
            if value < timezone.now().date():
                raise serializers.ValidationError(
                    "NTSA inspection expiry date cannot be in the past."
                )
        return value

    def validate_insurance_expiry_date(self, value):
        """Validate insurance expiry date"""
        if value:
            from django.utils import timezone
            if value < timezone.now().date():
                raise serializers.ValidationError(
                    "Insurance expiry date cannot be in the past."
                )
        return value

#Notification serializer
class NotificationSubscriberSerializer(serializers.ModelSerializer):
    """Serializer for notification subscription settings"""
    email = serializers.EmailField(source='user.email', read_only=True)

    class Meta:
        model = NotificationSubscriber
        fields = ['user', 'email', 'subscribed_at']
        read_only_fields = ['user', 'email', 'subscribed_at']


# Expenditure Serializer
class ExpenditureSerializer(serializers.ModelSerializer):
    vehicle_registration = serializers.CharField(
        source='vehicle.registration_number', read_only=True)

    class Meta:
        model = Expenditure
        fields = ['id', 'vehicle', 'vehicle_registration', 'date', 'item_name',
                  'description', 'quantity', 'amount',  'partner', 'deleted', 'created_at',]
        read_only_fields = ['partner', 'vehicle_registration']

# Revenue Serializer
class RevenueSerializer(serializers.ModelSerializer):
    # driver = DriverSerializer(read_only=True)

    class Meta:
        model = Revenue
        fields = ['id', 'vehicle', 'driver',
                  'date', 'amount', 'confirmation_message',  'deleted','created_at',]
        read_only_fields = ['driver']


# Add PaymentSettingsSerializer
class PaymentSettingsSerializer(serializers.ModelSerializer):
    class Meta:
        model = PaymentSettings
        fields = ['id', 'partner', 'vehicle',
                  'daily_amount', 'payment_days', 'deadline_time']
        read_only_fields = ['id', 'partner']

    def validate_payment_days(self, value):
        valid_days = [day[0] for day in PaymentSettings.DAYS_OF_WEEK]
        days = value.split(',')
        for day in days:
            if day not in valid_days:
                raise serializers.ValidationError(
                    f"Invalid day: {day}. Must be one of {valid_days}.")
        return value

    def validate_daily_amount(self, value):
        if value <= 0:
            raise serializers.ValidationError(
                "Daily amount must be a positive value.")
        return value

# # # Financial reports serializer
class FinancialReportSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    type = serializers.CharField()
    date = serializers.DateField()
    vehicle = serializers.CharField(allow_null=True)
    amount = serializers.DecimalField(max_digits=10, decimal_places=2)
    description = serializers.CharField(allow_blank=True, allow_null=True)
    deleted = serializers.CharField(allow_blank=True, allow_null=True)
    item_name = serializers.CharField(allow_blank=True, allow_null=True)
    confirmation_message = serializers.CharField(allow_blank=True, allow_null=True)

# Profit report serializer
class ProfitReportSerializer(serializers.Serializer):
    vehicle = serializers.CharField(source='vehicle.registration_number')
    profit = serializers.DecimalField(max_digits=10, decimal_places=2)

    class Meta:
        fields = ['vehicle', 'profit']


class DailyPaymentReportSerializer(serializers.Serializer):
    vehicle = serializers.CharField(source='vehicle.registration_number')
    driver_name = serializers.SerializerMethodField()
    amount_paid = serializers.DecimalField(
        max_digits=10, decimal_places=2, allow_null=True)
    confirmation_message = serializers.CharField(allow_null=True)
    status = serializers.CharField()  # "Paid" (green) or "Unpaid" (red)

    class Meta:
        fields = ['vehicle', 'driver_name', 'amount_paid',
                  'confirmation_message', 'status']

    def get_driver_name(self, obj):
        driver = obj['driver']
        return f"{driver.first_name} {driver.last_name}" if driver else "No Driver Assigned"

#enquiry Serializer
class EnquirySerializer(serializers.Serializer):
    first_name = serializers.CharField(max_length=30, required=True)
    last_name = serializers.CharField(max_length=30, required=True)
    phone_number = serializers.CharField(max_length=15, required=True)
    email = serializers.EmailField(required=True)
    message = serializers.CharField(required=True)

#Payment Transactions
class PaymentTransactionSerializer(serializers.ModelSerializer):
    user = serializers.PrimaryKeyRelatedField(queryset=User.objects.all())
    subscription = serializers.PrimaryKeyRelatedField(queryset=Subscription.objects.all(), required=False, allow_null=True)

    class Meta:
        model = PaymentTransaction
        fields = ['id', 'user', 'subscription', 'transaction_type', 'amount', 'phone_number', 'transaction_id', 'status', 'reference_id', 'created_at', 'updated_at', 'mpesa_receipt_number', 'transaction_date']
        read_only_fields = ['transaction_id', 'status', 'created_at', 'updated_at', 'mpesa_receipt_number', 'transaction_date']

    def validate_phone_number(self, value):
        if not value.startswith('254'):
            raise serializers.ValidationError("Phone number must start with '254'.")
        return value

    def validate_amount(self, value):
        if value <= 0:
            raise serializers.ValidationError("Amount must be greater than 0.")
        return int(value)  # Ensure integer for Daraja API

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation['user'] = instance.user.id  # Serialize user as ID
        if instance.subscription:
            representation['subscription'] = instance.subscription.id  # Serialize subscription as ID
        return representation

 #Subscription Serializer   
class SubscriptionSerializer(serializers.ModelSerializer):
    partner = serializers.PrimaryKeyRelatedField(queryset=Partner.objects.all())
    latest_payment = serializers.SerializerMethodField()

    class Meta:
        model = Subscription
        fields = ['id', 'partner', 'plan_type', 'billing_cycle', 'vehicle_count', 'amount', 'start_date', 'expiry_date', 'status', 'created_at', 'updated_at', 'latest_payment']
        read_only_fields = ['amount', 'start_date', 'expiry_date', 'status', 'created_at', 'updated_at']

    def validate(self, data):
        if Subscription.objects.filter(partner=data['partner'], status='active').exists():
            raise serializers.ValidationError("Partner already has an active subscription.")

        if data.get('plan_type') != 'Free' and not data.get('billing_cycle'):
            raise serializers.ValidationError("Billing cycle required for paid plans.")

        return data

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation['partner'] = instance.partner.id  # Ensure partner is ID
        return representation
    
    def get_latest_payment(self, obj):
        # Get the latest successful payment for this subscription
        payment = PaymentTransaction.objects.filter(
            reference_id=obj.id,
            transaction_type='subscription',
            status='success'
        ).order_by('-transaction_date').first()
        return PaymentTransactionSerializer(payment).data if payment else None

def send_subscription_success_email(partner, subscription, transaction=None):
    """Send email notification when a subscription is successfully activated."""
    
    logger = logging.getLogger(__name__)
    logger.info(f"Starting subscription success email for subscription {subscription.id} to {partner.user.email}")
    
    # Don't check for duplicates for now - let's ensure the email is sent
    subject = f"🎉 Your {subscription.plan_type} Plan is Now Active!"
    
    try:
        # Generate HTML message
        html_message = get_subscription_success_email_template(partner, subscription, transaction)
        logger.info(f"HTML template generated successfully for subscription {subscription.id}")
        
        # Plain text fallback
        plan_details = f"{subscription.plan_type} Plan"
        if subscription.plan_type != 'Free':
            plan_details += f" - {subscription.billing_cycle}"
        
        payment_info = ""
        if transaction and subscription.plan_type != 'Free':
            payment_info = f"""
Payment Details:
- Receipt Number: {transaction.mpesa_receipt_number or 'N/A'}
- Amount Paid: KSh {subscription.amount}
- Transaction Date: {transaction.transaction_date.strftime('%B %d, %Y at %I:%M %p') if transaction.transaction_date else 'N/A'}
"""
        elif subscription.plan_type == 'Free':
            payment_info = "Your free plan has been activated successfully. No payment required!"

        plain_message = f"""
Hi {partner.user.first_name},

Congratulations! Your subscription has been activated successfully.

{payment_info}

Subscription Details:
- Plan: {plan_details}
- Vehicle Limit: {subscription.vehicle_count} vehicle{'s' if subscription.vehicle_count != 1 else ''}
- Start Date: {subscription.start_date.strftime('%B %d, %Y') if subscription.start_date else 'Today'}
- Expires: {subscription.expiry_date.strftime('%B %d, %Y') if subscription.expiry_date else 'N/A'}

What's Next?
- Post unlimited job listings
- Access advanced driver management tools
- Track vehicle performance and expenses
- Generate detailed financial reports
- 24/7 priority customer support

Visit your dashboard: {settings.FRONTEND_URL}/partnerdashboard

Need help? Contact us:
Email: <EMAIL>
Phone: +254 700 000 000

Best regards,
The KadereConnect Team
"""

        # Create notification record
        notification = Notification.objects.create(
            subject=subject,
            type="email",
            recipient=partner.user.email,
            status="pending",
            message=html_message
        )
        logger.info(f"Notification record created for subscription {subscription.id} (ID: {notification.id})")

        # Send email
        logger.info(f"Sending email for subscription {subscription.id} to {partner.user.email}")
        logger.info(f"Email subject: {subject}")
        logger.info(f"From email: {settings.DEFAULT_FROM_EMAIL}")
        
        send_mail(
            subject=subject,
            message=plain_message,
            html_message=html_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[partner.user.email],
            fail_silently=False
        )
        
        # Mark as sent
        notification.mark_as_sent()
        logger.info(f"SUCCESS: Subscription success email sent to {partner.user.email} for subscription {subscription.id}")
        return True

    except Exception as e:
        logger.error(f"FAILED: Subscription success email for subscription {subscription.id}: {str(e)}")
        logger.error(f"Error type: {type(e).__name__}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        
        if 'notification' in locals():
            notification.mark_as_failed()
        
        return False


# Driver Rating Serializer
class DriverRatingSerializer(serializers.ModelSerializer):
    driver_name = serializers.SerializerMethodField()
    driver_age_group = serializers.SerializerMethodField()
    vehicle_registration = serializers.CharField(source='vehicle.registration_number', read_only=True)
    partner_name = serializers.SerializerMethodField()

    class Meta:
        model = DriverRating
        fields = [
            'id', 'driver', 'driver_name', 'driver_age_group', 'vehicle',
            'vehicle_registration', 'partner', 'partner_name', 'rating_score',
            'total_points', 'total_payment_days', 'last_payment_date',
            'consecutive_non_payment_days', 'is_active', 'calculation_start_date',
            'last_updated', 'created_at'
        ]
        read_only_fields = [
            'rating_score', 'total_points', 'total_payment_days',
            'last_payment_date', 'consecutive_non_payment_days',
            'calculation_start_date', 'last_updated', 'created_at'
        ]

    def get_driver_name(self, obj):
        if obj.driver:
            return f"{obj.driver.first_name} {obj.driver.last_name}".strip()
        return "Unknown Driver"

    def get_driver_age_group(self, obj):
        if obj.driver:
            from .rating_utils import get_driver_age_group
            return get_driver_age_group(obj.driver)
        return "unknown"

    def get_partner_name(self, obj):
        if obj.partner:
            return f"{obj.partner.first_name} {obj.partner.last_name}".strip()
        return "Unknown Partner"


# Driver Rating Report Serializer (for filtered reports)
class DriverRatingReportSerializer(serializers.Serializer):
    driver_id = serializers.IntegerField()
    driver_name = serializers.CharField()
    driver_age_group = serializers.CharField()
    vehicle_id = serializers.IntegerField()
    vehicle_registration = serializers.CharField()
    partner_id = serializers.IntegerField()
    partner_name = serializers.CharField()
    rating_score = serializers.DecimalField(max_digits=4, decimal_places=2)
    total_points = serializers.DecimalField(max_digits=10, decimal_places=2)
    total_payment_days = serializers.IntegerField()
    last_payment_date = serializers.DateField(allow_null=True)
    consecutive_non_payment_days = serializers.IntegerField()
    is_active = serializers.BooleanField()
    last_updated = serializers.DateTimeField()

    class Meta:
        fields = [
            'driver_id', 'driver_name', 'driver_age_group', 'vehicle_id',
            'vehicle_registration', 'partner_id', 'partner_name', 'rating_score',
            'total_points', 'total_payment_days', 'last_payment_date',
            'consecutive_non_payment_days', 'is_active', 'last_updated'
        ]