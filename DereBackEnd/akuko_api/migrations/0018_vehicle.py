# Generated by Django 5.0.7 on 2025-03-24 07:10

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('akuko_api', '0017_jobapplication_hired_at_jobapplication_un_hired_at'),
    ]

    operations = [
        migrations.CreateModel(
            name='Vehicle',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('registration_number', models.CharField(max_length=20, unique=True)),
                ('work_days', models.CharField(max_length=100)),
                ('ntsa_inspection_doc', models.FileField(blank=True, null=True, upload_to='photos/ntsa/')),
                ('ntsa_expiry_date', models.DateField(blank=True, null=True)),
                ('insurance_doc', models.FileField(blank=True, null=True, upload_to='photos/insurance/')),
                ('insurance_expiry_date', models.DateField(blank=True, null=True)),
                ('logbook', models.FileField(blank=True, null=True, upload_to='photos/logbook/')),
                ('lease_agreement', models.FileField(blank=True, null=True, upload_to='photos/lease/')),
                ('car_picture', models.ImageField(blank=True, null=True, upload_to='photos/vehicles/')),
                ('driver', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='vehicle', to='akuko_api.driver')),
                ('partner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='vehicles', to='akuko_api.partner')),
                ('preferred_work_area', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='akuko_api.workarea')),
                ('vehicle_make', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='akuko_api.vehiclemake')),
                ('vehicle_model', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='akuko_api.vehiclemodel')),
            ],
        ),
    ]
