# Generated by Django 5.0.7 on 2024-10-11 17:09

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('email', models.EmailField(max_length=255, unique=True)),
                ('first_name', models.CharField(blank=True, max_length=30, null=True)),
                ('last_name', models.CharField(blank=True, max_length=30, null=True)),
                ('role', models.CharField(blank=True, max_length=10, null=True)),
                ('is_active', models.Bo<PERSON>anField(default=True)),
                ('date_joined', models.DateTimeField(auto_now_add=True, null=True)),
                ('password', models.CharField(blank=True, max_length=128, null=True)),
                ('email_verified', models.BooleanField(default=False)),
                ('verification_code', models.CharField(default=uuid.uuid4, editable=False, max_length=36, unique=True)),
                ('is_staff', models.BooleanField(default=False)),
                ('is_superuser', models.BooleanField(default=False)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='Job',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('open', 'Open'), ('closed', 'Closed')], default='open', max_length=10)),
                ('requirements', models.TextField()),
                ('vehicle_photo', models.ImageField(blank=True, null=True, upload_to='photos/vehicles/')),
                ('work_days', models.CharField(max_length=100)),
                ('min_age', models.IntegerField(blank=True, null=True)),
                ('max_age', models.IntegerField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='VehicleMake',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
            ],
        ),
        migrations.CreateModel(
            name='VehicleType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
            ],
        ),
        migrations.CreateModel(
            name='WorkArea',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
            ],
        ),
        migrations.CreateModel(
            name='Driver',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('first_name', models.CharField(blank=True, max_length=30, null=True)),
                ('last_name', models.CharField(blank=True, max_length=30, null=True)),
                ('email', models.EmailField(blank=True, max_length=255, null=True)),
                ('id_number', models.CharField(blank=True, max_length=20, null=True)),
                ('date_of_birth', models.DateField(blank=True, null=True)),
                ('gender', models.CharField(blank=True, choices=[('M', 'Male'), ('F', 'Female')], max_length=10, null=True)),
                ('mobile_number', models.CharField(blank=True, max_length=15, null=True)),
                ('password', models.CharField(blank=True, max_length=128, null=True)),
                ('confirm_password', models.CharField(blank=True, max_length=128, null=True)),
                ('psv_photo', models.ImageField(blank=True, null=True, upload_to='photos/psv/')),
                ('id_photo', models.ImageField(blank=True, null=True, upload_to='photos/id/')),
                ('license_photo', models.ImageField(blank=True, null=True, upload_to='photos/license/')),
                ('good_conduct_photo', models.ImageField(blank=True, null=True, upload_to='photos/good_conduct/')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('vehicle_type', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='akuko_api.vehicletype')),
                ('work_area', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='akuko_api.workarea')),
            ],
        ),
        migrations.CreateModel(
            name='JobApplication',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('Pending', 'Pending'), ('Accepted', 'Accepted'), ('Rejected', 'Rejected')], default='Pending', max_length=10)),
                ('applied_at', models.DateTimeField(auto_now_add=True)),
                ('removed_by_partner', models.BooleanField(default=False)),
                ('withdrawn_by_driver', models.BooleanField(default=False)),
                ('driver', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='applications', to='akuko_api.driver')),
                ('job', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='applications', to='akuko_api.job')),
            ],
        ),
        migrations.CreateModel(
            name='Partner',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('first_name', models.CharField(blank=True, max_length=30, null=True)),
                ('last_name', models.CharField(blank=True, max_length=30, null=True)),
                ('email', models.EmailField(blank=True, max_length=255, null=True)),
                ('id_number', models.CharField(blank=True, max_length=20, null=True)),
                ('company_number', models.CharField(blank=True, max_length=20, null=True)),
                ('company_name', models.CharField(blank=True, max_length=100, null=True)),
                ('password', models.CharField(blank=True, max_length=128, null=True)),
                ('confirm_password', models.CharField(blank=True, max_length=128, null=True)),
                ('id_photo', models.ImageField(blank=True, null=True, upload_to='photos/id/')),
                ('car_photo', models.ImageField(blank=True, null=True, upload_to='photos/car/')),
                ('user', models.OneToOneField(null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('vehicle_type', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='akuko_api.vehicletype')),
                ('preferred_work_area', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='akuko_api.workarea')),
            ],
        ),
        migrations.AddField(
            model_name='job',
            name='partner',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='jobs', to='akuko_api.partner'),
        ),
        migrations.AddField(
            model_name='job',
            name='vehicle_make',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='akuko_api.vehiclemake'),
        ),
        migrations.CreateModel(
            name='VehicleModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('make', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='models', to='akuko_api.vehiclemake')),
            ],
        ),
        migrations.AddField(
            model_name='job',
            name='vehicle_model',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='akuko_api.vehiclemodel'),
        ),
        migrations.AddField(
            model_name='job',
            name='preferred_work_area',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='akuko_api.workarea'),
        ),
    ]
