# Generated by Django 5.0.7 on 2025-04-27 04:46

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('akuko_api', '0027_expenditure'),
    ]

    operations = [
        migrations.CreateModel(
            name='PaymentSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('daily_amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('payment_days', models.CharField(max_length=100)),
                ('deadline_time', models.TimeField()),
                ('partner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payment_settings', to='akuko_api.partner')),
                ('vehicle', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='payment_settings', to='akuko_api.vehicle')),
            ],
        ),
        migrations.CreateModel(
            name='Revenue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('confirmation_message', models.TextField()),
                ('driver', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='akuko_api.driver')),
                ('vehicle', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='akuko_api.vehicle')),
            ],
        ),
    ]
