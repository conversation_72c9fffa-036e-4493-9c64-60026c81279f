# Generated by Django 5.0.7 on 2025-05-07 05:23

import datetime
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('akuko_api', '0030_expenditure_description'),
    ]

    operations = [
        migrations.AddField(
            model_name='revenue',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=datetime.datetime(2025, 5, 7, 5, 23, 36, 888918, tzinfo=datetime.timezone.utc)),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='revenue',
            name='driver',
            field=models.ForeignKey(limit_choices_to={'role': 'driver'}, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddIndex(
            model_name='revenue',
            index=models.Index(fields=['date'], name='akuko_api_r_date_14463b_idx'),
        ),
    ]
