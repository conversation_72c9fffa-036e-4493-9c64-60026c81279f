# Generated by Django 5.0.7 on 2025-07-30 07:37

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('akuko_api', '0057_alter_driverrating_calculation_start_date'),
    ]

    operations = [
        migrations.AddField(
            model_name='driver',
            name='is_locked',
            field=models.BooleanField(default=False, help_text='Whether the driver account is locked'),
        ),
        migrations.AddField(
            model_name='driver',
            name='lock_expires_at',
            field=models.DateTimeField(blank=True, help_text='When temporary lock expires', null=True),
        ),
        migrations.AddField(
            model_name='driver',
            name='lock_reason',
            field=models.TextField(blank=True, help_text='Reason for account lock', null=True),
        ),
        migrations.AddField(
            model_name='driver',
            name='lock_type',
            field=models.CharField(blank=True, choices=[('temporary', 'Temporary (6 months)'), ('permanent', 'Permanent')], help_text='Type of account lock', max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='driver',
            name='locked_at',
            field=models.DateTimeField(blank=True, help_text='When the account was locked', null=True),
        ),
        migrations.CreateModel(
            name='DriverFlag',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reason', models.TextField(help_text='Reason for flagging the driver (mandatory)')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='When the flag was created')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this flag is still active')),
                ('resolved_at', models.DateTimeField(blank=True, help_text='When the flag was resolved (if applicable)', null=True)),
                ('resolution_notes', models.TextField(blank=True, help_text='Notes about flag resolution', null=True)),
                ('driver', models.ForeignKey(help_text='Driver being flagged', on_delete=django.db.models.deletion.CASCADE, related_name='flags', to='akuko_api.driver')),
                ('flagged_by_user', models.ForeignKey(blank=True, help_text='User who created the flag', null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('partner', models.ForeignKey(help_text='Partner who flagged the driver', on_delete=django.db.models.deletion.CASCADE, related_name='driver_flags', to='akuko_api.partner')),
                ('resolved_by', models.ForeignKey(blank=True, help_text='Admin user who resolved the flag', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='resolved_flags', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['driver', 'is_active'], name='akuko_api_d_driver__c4562b_idx'), models.Index(fields=['partner', 'created_at'], name='akuko_api_d_partner_19bece_idx'), models.Index(fields=['created_at'], name='akuko_api_d_created_666dc3_idx')],
                'unique_together': {('driver', 'partner')},
            },
        ),
    ]
