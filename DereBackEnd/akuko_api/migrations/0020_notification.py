# Generated by Django 5.0.7 on 2025-03-24 09:11

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('akuko_api', '0019_rename_car_picture_vehicle_vehicle_photo'),
    ]

    operations = [
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('recipient', models.EmailField(max_length=254)),
                ('message', models.TextField()),
                ('notification_type', models.CharField(choices=[('email', 'Email'), ('sms', 'SMS')], default='email', max_length=10)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('sent', 'Sent'), ('failed', 'Failed')], default='pending', max_length=10)),
                ('attempts', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
    ]
