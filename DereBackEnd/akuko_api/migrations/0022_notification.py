# Generated by Django 5.0.7 on 2025-03-25 22:51

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('akuko_api', '0021_delete_notification'),
    ]

    operations = [
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('subject', models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ('type', models.Char<PERSON>ield(choices=[('sms', 'SMS'), ('email', 'Email')], max_length=10)),
                ('recipient', models.Char<PERSON>ield(max_length=255)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('sent', 'Sent'), ('failed', 'Failed')], default='pending', max_length=10)),
                ('sent_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
    ]
