# akuko_api/migrations/0035_auto_20250506_0502.py
from django.db import migrations, models
import django.db.models.deletion

class Migration(migrations.Migration):
    dependencies = [
        ('akuko_api', '0030_expenditure_description'),  # Depend on the last applied migration
    ]

    operations = [
        migrations.CreateModel(
            name='PaymentSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('daily_amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('payment_days', models.CharField(max_length=100)),
                ('deadline_time', models.TimeField()),
                ('partner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payment_settings', to='akuko_api.partner')),
                ('vehicle', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='payment_settings', to='akuko_api.vehicle')),
            ],
            options={
                'managed': True,
            },
        ),
    ]