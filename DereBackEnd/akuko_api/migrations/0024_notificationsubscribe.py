# Generated by Django xxx

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        # Replace 'yourapp' and '0001_initial' with your actual app name and last migration
        ('akuko_api', '0023_merge_0022_notification_0022_vehicle_status'),
    ]

    operations = [
        migrations.AddField(
            model_name='user',
            name='notification_subscribe',
            field=models.BooleanField(default=True),
        ),
        migrations.CreateModel(
            name='NotificationSubscriber',
            fields=[
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE,
                 primary_key=True, serialize=False, to='akuko_api.user')),
                ('subscribed_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
    ]
