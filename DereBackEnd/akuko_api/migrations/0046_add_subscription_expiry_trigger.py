# Generated by Django 5.0.7 on 2025-06-19 11:52

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('akuko_api', '0045_merge_20250529_1257'),
    ]

    operations = [
        migrations.RunSQL(
            """
            -- Function to check and update expired subscriptions
            CREATE OR REPLACE FUNCTION check_and_expire_subscriptions()
            RETURNS TRIGGER AS $$
            BEGIN
                -- For INSERT/UPDATE operations, check the specific record
                IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
                    -- Check if the new record is expired
                    IF NEW.expiry_date <= NOW() AND NEW.status IN ('active', 'cancelled') THEN
                        NEW.status = 'expired';
                        NEW.updated_at = NOW();
                    END IF;
                    RETURN NEW;
                END IF;
                
                RETURN NULL;
            END;
            $$ LANGUAGE plpgsql;

            -- Create trigger for INSERT/UPDATE operations
            DROP TRIGGER IF EXISTS subscription_expiry_check_trigger ON akuko_api_subscription;
            CREATE TRIGGER subscription_expiry_check_trigger
                BEFORE INSERT OR UPDATE ON akuko_api_subscription
                FOR EACH ROW
                EXECUTE FUNCTION check_and_expire_subscriptions();

            -- Create a function to manually expire old subscriptions
            CREATE OR REPLACE FUNCTION expire_old_subscriptions()
            RETURNS INTEGER AS $$
            DECLARE
                expired_count INTEGER;
            BEGIN
                UPDATE akuko_api_subscription 
                SET status = 'expired', updated_at = NOW()
                WHERE expiry_date <= NOW() 
                AND status IN ('active', 'cancelled')
                AND status != 'expired';
                
                GET DIAGNOSTICS expired_count = ROW_COUNT;
                RETURN expired_count;
            END;
            $$ LANGUAGE plpgsql;
            """,
            reverse_sql="""
            DROP TRIGGER IF EXISTS subscription_expiry_check_trigger ON akuko_api_subscription;
            DROP FUNCTION IF EXISTS check_and_expire_subscriptions();
            DROP FUNCTION IF EXISTS expire_old_subscriptions();
            """
        ),
    ]
