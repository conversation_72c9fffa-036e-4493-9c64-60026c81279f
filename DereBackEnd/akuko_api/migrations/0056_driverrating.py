# Generated by Django 5.0.7 on 2025-07-26 09:43

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('akuko_api', '0055_make_registration_number_work_days_optional'),
    ]

    operations = [
        migrations.CreateModel(
            name='DriverRating',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('rating_score', models.DecimalField(decimal_places=2, default=0.0, max_digits=4)),
                ('total_points', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('total_payment_days', models.PositiveIntegerField(default=0)),
                ('last_payment_date', models.DateField(blank=True, null=True)),
                ('consecutive_non_payment_days', models.PositiveIntegerField(default=0)),
                ('is_active', models.Bo<PERSON>anField(default=True)),
                ('calculation_start_date', models.DateField(auto_now_add=True)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('driver', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ratings', to='akuko_api.driver')),
                ('partner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='driver_ratings', to='akuko_api.partner')),
                ('vehicle', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='driver_ratings', to='akuko_api.vehicle')),
            ],
            options={
                'ordering': ['-last_updated'],
                'unique_together': {('driver', 'vehicle')},
            },
        ),
    ]
