# Generated by Django 5.0.7 on 2025-05-07 12:22

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('akuko_api', '0032_alter_revenue_driver'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='revenue',
            options={'ordering': ['-date']},
        ),
        migrations.RemoveIndex(
            model_name='revenue',
            name='akuko_api_r_date_14463b_idx',
        ),
        migrations.AlterField(
            model_name='revenue',
            name='confirmation_message',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='revenue',
            name='driver',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='revenues', to='akuko_api.driver'),
        ),
        migrations.AlterField(
            model_name='revenue',
            name='vehicle',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='vehicle_revenues', to='akuko_api.vehicle'),
        ),
    ]
