from rest_framework.decorators import action
from rest_framework import serializers
import logging
from rest_framework.permissions import IsAuthenticated,  AllowAny
from rest_framework.response import Response
from rest_framework import mixins, viewsets
from rest_framework.pagination import PageNumberPagination
from django.core.paginator import Paginator
from .models import Driver, Notification, Partner, Job, VehicleMake, VehicleModel, WorkArea, VehicleType, User, JobApplication, PasswordResetToken, Vehicle, Expenditure, Revenue, PaymentSettings, NotificationSubscriber, Enquiry, PaymentTransaction, Subscription, DriverRating
from .serializers import JobApplicationSerializer, FinancialReportSerializer,  DriverSerializer, PartnerSerializer, JobSerializer, VehicleMakeSerializer, VehicleModelSerializer, WorkAreaSerializer, VehicleTypeSerializer, UserSerializer, PasswordResetSerializer, PasswordResetConfirmSerializer, VehicleSerializer, ExpenditureSerializer, RevenueSerializer, PaymentSettingsSerializer, ProfitReportSerializer, DailyPaymentReportSerializer, NotificationSubscriberSerializer, send_driver_hired_email, send_driver_rejected_email, EnquirySerializer, PaymentTransactionSerializer, SubscriptionSerializer, send_subscription_success_email, DriverRatingSerializer, DriverRatingReportSerializer
from django.shortcuts import get_object_or_404
from rest_framework.views import APIView
from rest_framework import status
from django.contrib.auth.models import AnonymousUser
from rest_framework_simplejwt.views import TokenObtainPairView
from .serializers import CustomTokenObtainPairSerializer
from rest_framework.exceptions import ValidationError
from django.contrib.auth.password_validation import validate_password
from rest_framework.permissions import BasePermission
from rest_framework.parsers import MultiPartParser, FormParser
from django.core.mail import send_mail
from django.conf import settings
from django.utils import timezone
from django.db.models import Q, Sum, Count, Avg
from django.db import models
from datetime import datetime, timedelta
from django.db.models.manager import BaseManager
from rest_framework import permissions
from django.utils.dateparse import parse_date
from calendar import monthrange
from rest_framework.decorators import api_view, permission_classes
from decimal import Decimal
from typing import Any
import base64
import requests
from decouple import config
from django.core.exceptions import ObjectDoesNotExist
import json
from django.urls import reverse
from django.db import transaction
from django.views.decorators.csrf import csrf_exempt
import time
from django.http import HttpRequest
from rest_framework.request import Request
from rest_framework.parsers import JSONParser
import pytz
from rest_framework.viewsets import ViewSet


# Create a logger object
logger = logging.getLogger(__name__)


# Custom pagination class for reports
class ReportPagination(PageNumberPagination):
    page_size = 10
    page_size_query_param = 'page_size'
    max_page_size = 100

    def get_paginated_response(self, data):
        return Response({
            'count': self.page.paginator.count,
            'next': self.get_next_link(),
            'previous': self.get_previous_link(),
            'total_pages': self.page.paginator.num_pages,
            'current_page': self.page.number,
            'page_size': self.page_size,
            'results': data
        })

# email


class TestEmailView(APIView):
    def post(self, request):
        email = request.data.get('email')
        if not email:
            return Response({'error': 'Email is required.'}, status=status.HTTP_400_BAD_REQUEST)

        subject = 'Test Email from Django'
        message = 'This is a test email to check if the email sending service is working.'
        recipient_list = [email]

        try:
            send_mail(subject, message,
                      settings.DEFAULT_FROM_EMAIL, recipient_list)
            return Response({'message': 'Test email sent successfully!'}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# # logged in user view
class MyUserDetailView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        user = request.user

        if isinstance(user, AnonymousUser):
            return Response({"detail": "User is not authenticated."}, status=status.HTTP_401_UNAUTHORIZED)

        # Safe check for role attribute during schema generation (Swagger inspection)
        if getattr(self, 'swagger_fake_view', False):
            data = {
                "email": user.email,
                "first_name": user.first_name,
                "last_name": user.last_name,
                # Temporarily use a placeholder for role during schema generation
                "role": "role_placeholder",
                "email_verified": user.email_verified,
            }
            return Response(data)

        data = {
            "email": user.email,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "role": user.role,
            "email_verified": user.email_verified,
        }

        if user.role == 'driver':
            try:
                driver = Driver.objects.get(user=user)
                data['driver_details'] = DriverSerializer(driver).data

                # Fetch applications removed by the partner
                removed_applications = JobApplication.objects.filter(
                    driver=driver, removed_by_partner=True, withdrawn_by_driver=False
                )
                data['notifications'] = {
                    "removed_applications": JobApplicationSerializer(removed_applications, many=True).data
                }
            except Driver.DoesNotExist:
                data['driver_details'] = "Driver details not found."

        elif user.role == 'partner':
            try:
                partner = Partner.objects.get(user=user)
                data['partner_details'] = PartnerSerializer(partner).data

                # Fetch applications withdrawn by drivers
                withdrawn_applications = JobApplication.objects.filter(
                    job__partner=partner, withdrawn_by_driver=True, removed_by_partner=False
                )
                data['notifications'] = {
                    "withdrawn_applications": JobApplicationSerializer(withdrawn_applications, many=True).data
                }
            except Partner.DoesNotExist:
                data['partner_details'] = "Partner details not found."

        return Response(data)


class UpdatePasswordView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """
        Update the password for the authenticated user.
        """
        user = request.user
        data = request.data

        # Validate required fields
        current_password = data.get("current_password")
        new_password = data.get("new_password")
        confirm_password = data.get("confirm_password")

        if not current_password or not new_password or not confirm_password:
            return Response(
                {"detail": "All fields (current_password, new_password, confirm_password) are required."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if the current password is correct
        if not user.check_password(current_password):
            return Response(
                {"detail": "Old password is incorrect."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if the new password matches the confirmation
        if new_password != confirm_password:
            return Response(
                {"detail": "New password and confirm password do not match."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Validate the new password
        try:
            validate_password(new_password, user=user)
        except ValidationError as e:
            return Response(
                {"detail": e.messages},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Update the password
        user.set_password(new_password)
        user.save()

        return Response(
            {"detail": "Password updated successfully."},
            status=status.HTTP_200_OK
        )

# login user view


class CustomTokenObtainPairView(TokenObtainPairView):
    serializer_class = CustomTokenObtainPairSerializer

# verify email view


class VerifyEmail(APIView):
    def get(self, request, verification_code):
        user = get_object_or_404(User, verification_code=verification_code)
        if user.email_verified:
            return Response({'detail': 'Email verified successfully!'}, status=status.HTTP_200_OK)

        user.email_verified = True
        user.save()
        return Response({'detail': 'Email verified successfully!'}, status=status.HTTP_200_OK)


# vehicle type view
class VehicleTypeViewSet(mixins.CreateModelMixin,
                         mixins.RetrieveModelMixin,
                         mixins.UpdateModelMixin,
                         mixins.DestroyModelMixin,
                         mixins.ListModelMixin,
                         viewsets.GenericViewSet):
    queryset = VehicleType.objects.all().order_by('name')
    serializer_class = VehicleTypeSerializer

# vehicle make view


class VehicleMakeViewSet(viewsets.GenericViewSet, mixins.ListModelMixin, mixins.RetrieveModelMixin):
    queryset = VehicleMake.objects.all().order_by('name')
    serializer_class = VehicleMakeSerializer


# vehicle model view
class VehicleModelViewSet(viewsets.GenericViewSet, mixins.ListModelMixin, mixins.RetrieveModelMixin):
    serializer_class = VehicleModelSerializer

    def get_queryset(self):
        queryset = VehicleModel.objects.all()
        make_id = self.request.query_params.get('make', None)
        if make_id:
            queryset = queryset.filter(make_id=make_id)
        return queryset.order_by('name').distinct()

# work area view


class WorkAreaViewSet(mixins.CreateModelMixin,
                      mixins.RetrieveModelMixin,
                      mixins.UpdateModelMixin,
                      mixins.DestroyModelMixin,
                      mixins.ListModelMixin,
                      viewsets.GenericViewSet):
    queryset = WorkArea.objects.all()
    serializer_class = WorkAreaSerializer

# custom permission


class CustomPermissionMixin:
    """
    A mixin to dynamically set permissions based on the action being performed.
    """

    def get_permissions(self):
        if self.action == 'create':
            return [AllowAny()]
        return [IsAuthenticated()]


# # driver view
class DriverViewSet(CustomPermissionMixin,
                    mixins.CreateModelMixin,
                    mixins.RetrieveModelMixin,
                    mixins.UpdateModelMixin,
                    mixins.DestroyModelMixin,
                    mixins.ListModelMixin,
                    viewsets.GenericViewSet):
    queryset = Driver.objects.all()
    serializer_class = DriverSerializer
    parser_classes = [MultiPartParser, FormParser]

    @action(detail=True, methods=['get'], permission_classes=[IsAuthenticated])
    def rating(self, request, pk=None):
        """Get comprehensive rating dashboard for a specific driver"""
        driver = self.get_object()

        # Check permissions - drivers can only see their own rating, partners can see their drivers' ratings
        if request.user.role == 'driver':
            if not hasattr(request.user, 'driver') or request.user.driver.id != driver.id:
                return Response({"detail": "You can only view your own rating."},
                              status=status.HTTP_403_FORBIDDEN)
        elif request.user.role == 'partner':
            try:
                partner = request.user.partner
                if driver.partner != partner:
                    return Response({"detail": "You can only view ratings for your own drivers."},
                                  status=status.HTTP_403_FORBIDDEN)
            except AttributeError:
                return Response({"detail": "Partner profile not found."},
                              status=status.HTTP_404_NOT_FOUND)
        else:
            return Response({"detail": "Access denied."}, status=status.HTTP_403_FORBIDDEN)

        # Get the driver's current rating(s)
        ratings = DriverRating.objects.filter(driver=driver).select_related('vehicle', 'partner')

        if not ratings.exists():
            return Response({
                "driver_id": driver.id,
                "driver_name": f"{driver.first_name} {driver.last_name}".strip(),
                "driver_code": f"DRV-{driver.id:04d}",
                "age_group": self._get_driver_age_group(driver),
                "current_rating": 0.0,
                "rating_level": "No Rating",
                "message": "No ratings available yet. Ratings are calculated based on payment behavior."
            })

        # Calculate comprehensive rating data
        rating_data = self._calculate_comprehensive_rating(driver, ratings)

        return Response(rating_data)

    def _get_driver_age_group(self, driver):
        """Get driver age group"""
        from .rating_utils import get_driver_age_group
        age_group_map = {
            'below_25': 'Below 25',
            '26_39': '26-39 years',
            '40_above': '40+ years',
            'unknown': 'Unknown'
        }
        return age_group_map.get(get_driver_age_group(driver), 'Unknown')

    def _get_rating_level(self, score):
        """Get rating level based on score"""
        if score >= 4.5:
            return "Excellent"
        elif score >= 3.5:
            return "Good"
        elif score >= 2.5:
            return "Average"
        elif score >= 1.0:
            return "Poor"
        else:
            return "Very Poor"

    def _calculate_comprehensive_rating(self, driver, ratings):
        """Calculate comprehensive rating data for dashboard"""
        from datetime import timedelta
        from django.utils import timezone

        # Calculate overall rating
        total_points = sum(rating.total_points for rating in ratings)
        total_payment_days = sum(rating.total_payment_days for rating in ratings)
        overall_rating = float(total_points / total_payment_days) if total_payment_days > 0 else 0.0

        # Get payment performance data
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=30)  # Last 30 days

        # Get revenue records for analysis
        revenues = Revenue.objects.filter(
            driver=driver,
            date__gte=start_date,
            date__lte=end_date,
            deleted=False
        ).select_related('vehicle')

        # Calculate payment statistics
        payment_stats = self._calculate_payment_statistics(driver, revenues, start_date, end_date)

        # Get recent payment history
        recent_payments = self._get_recent_payment_history(driver, limit=10)

        # Get rating trend (last 5 weeks)
        rating_trend = self._get_rating_trend(driver)

        # Get primary vehicle info
        primary_rating = ratings.first()
        vehicle_info = f"{primary_rating.vehicle.registration_number}"
        if hasattr(primary_rating.vehicle, 'vehicle_make') and primary_rating.vehicle.vehicle_make:
            vehicle_info = f"{primary_rating.vehicle.vehicle_make.name} ({primary_rating.vehicle.registration_number})"

        # Get all vehicles the driver has used
        all_vehicles = self._get_driver_vehicle_history(driver)

        # Get total points across all time and vehicles
        total_points_all_time = self._get_driver_total_points(driver)

        return {
            # Basic driver info
            "driver_id": driver.id,
            "driver_name": f"{driver.first_name} {driver.last_name}".strip(),
            "driver_code": f"DRV-{driver.id:04d}",
            "vehicle": vehicle_info,
            "age_group": self._get_driver_age_group(driver),
            "active_since": primary_rating.calculation_start_date.strftime("%B %Y") if primary_rating else "Unknown",

            # Rating info
            "current_rating": round(overall_rating, 1),
            "rating_level": self._get_rating_level(overall_rating),

            # Payment statistics
            "payment_days": payment_stats['total_expected_days'],
            "on_time_payments": payment_stats['on_time_count'],
            "late_payments": payment_stats['late_count'],
            "missed_payments": payment_stats['missed_count'],
            "total_points_period": int(total_points),  # Points for current period
            "total_points_all_time": int(total_points_all_time),  # All-time points
            "success_rate": payment_stats['success_rate'],  # Traditional payment frequency based
            "payment_completion_rate": payment_stats['payment_completion_rate'],  # New revenue-based rate
            "expected_revenue": payment_stats['expected_revenue'],
            "actual_revenue": payment_stats['actual_revenue'],

            # Vehicle information
            "current_vehicle": vehicle_info,
            "vehicle_history": all_vehicles,
            "total_vehicles": len(all_vehicles),
            "active_vehicles": ratings.count(),

            # Trends and history
            "rating_trend": rating_trend,
            "payment_performance": {
                "on_time": payment_stats['on_time_percentage'],
                "late": payment_stats['late_percentage'],
                "missed": payment_stats['missed_percentage']
            },
            "recent_payment_history": recent_payments,

            # Status
            "is_active": all(rating.is_active for rating in ratings),
            "rating_calculation_info": {
                "calculation_method": "payment_based",
                "points_on_time": 5.0,
                "points_late": 2.5,
                "points_missed": 0.0,
                "rating_formula": "total_points / total_payment_days"
            }
        }

    def _get_driver_vehicle_history(self, driver):
        """Get comprehensive vehicle history for a driver"""
        # Get all vehicles from ratings (current and past)
        rating_vehicles = DriverRating.objects.filter(driver=driver).select_related('vehicle')

        # Get all vehicles from revenue records (to catch any missed in ratings)
        revenue_vehicles = Revenue.objects.filter(
            driver=driver,
            deleted=False
        ).values('vehicle__id', 'vehicle__registration_number').distinct()

        vehicles = []
        seen_vehicle_ids = set()

        # Add vehicles from ratings (these have rating data)
        for rating in rating_vehicles:
            if rating.vehicle and rating.vehicle.id not in seen_vehicle_ids:
                vehicles.append({
                    "id": rating.vehicle.id,
                    "registration": rating.vehicle.registration_number,
                    "make": rating.vehicle.vehicle_make.name if hasattr(rating.vehicle, 'vehicle_make') and rating.vehicle.vehicle_make else "Unknown",
                    "model": rating.vehicle.vehicle_model.name if hasattr(rating.vehicle, 'vehicle_model') and rating.vehicle.vehicle_model else "Unknown",
                    "type": rating.vehicle.vehicle_type.name if hasattr(rating.vehicle, 'vehicle_type') and rating.vehicle.vehicle_type else "Unknown",
                    "has_rating": True,
                    "is_active": rating.is_active,
                    "rating_start_date": rating.calculation_start_date.strftime('%Y-%m-%d') if rating.calculation_start_date else None,
                    "total_points": float(rating.total_points),
                    "payment_days": rating.total_payment_days
                })
                seen_vehicle_ids.add(rating.vehicle.id)

        # Add any additional vehicles from revenue records
        for rev_vehicle in revenue_vehicles:
            if rev_vehicle['vehicle__id'] and rev_vehicle['vehicle__id'] not in seen_vehicle_ids:
                vehicles.append({
                    "id": rev_vehicle['vehicle__id'],
                    "registration": rev_vehicle['vehicle__registration_number'],
                    "make": "Unknown",
                    "model": "Unknown",
                    "type": "Unknown",
                    "has_rating": False,
                    "is_active": False,
                    "rating_start_date": None,
                    "total_points": 0.0,
                    "payment_days": 0
                })
                seen_vehicle_ids.add(rev_vehicle['vehicle__id'])

        return vehicles

    def _calculate_payment_statistics(self, driver, revenues, start_date, end_date):
        """Calculate payment statistics for the given period with revenue-based completion rate"""
        from .rating_utils import get_expected_payment_days, calculate_payment_status

        # Get expected payment days for all driver's vehicles
        expected_dates = set()
        for rating in DriverRating.objects.filter(driver=driver):
            vehicle_expected = get_expected_payment_days(rating.vehicle, start_date, end_date)
            expected_dates.update(vehicle_expected)

        total_expected_days = len(expected_dates)

        if total_expected_days == 0:
            return {
                'total_expected_days': 0,
                'on_time_count': 0,
                'late_count': 0,
                'missed_count': 0,
                'success_rate': 0,
                'payment_completion_rate': 0,
                'on_time_percentage': 0,
                'late_percentage': 0,
                'missed_percentage': 0,
                'expected_revenue': 0.0,
                'actual_revenue': 0.0
            }

        # Create payment map
        payment_map = {revenue.date: revenue for revenue in revenues}

        on_time_count = 0
        late_count = 0
        missed_count = 0

        for expected_date in expected_dates:
            if expected_date in payment_map:
                revenue = payment_map[expected_date]
                status, points = calculate_payment_status(revenue)
                if status == 'on_time':
                    on_time_count += 1
                else:  # late
                    late_count += 1
            else:
                missed_count += 1

        # Calculate traditional success rate (payment frequency based)
        success_rate = round(((on_time_count + late_count) / total_expected_days) * 100) if total_expected_days > 0 else 0
        on_time_percentage = round((on_time_count / total_expected_days) * 100) if total_expected_days > 0 else 0
        late_percentage = round((late_count / total_expected_days) * 100) if total_expected_days > 0 else 0
        missed_percentage = round((missed_count / total_expected_days) * 100) if total_expected_days > 0 else 0

        # Calculate revenue-based completion rate
        expected_revenue, actual_revenue, payment_completion_rate = self._calculate_revenue_completion_rate(
            driver, start_date, end_date
        )

        return {
            'total_expected_days': total_expected_days,
            'on_time_count': on_time_count,
            'late_count': late_count,
            'missed_count': missed_count,
            'success_rate': success_rate,  # Traditional payment frequency based
            'payment_completion_rate': payment_completion_rate,  # New revenue-based rate
            'on_time_percentage': on_time_percentage,
            'late_percentage': late_percentage,
            'missed_percentage': missed_percentage,
            'expected_revenue': expected_revenue,
            'actual_revenue': actual_revenue
        }

    def _get_recent_payment_history(self, driver, limit=10):
        """Get recent payment history with status"""
        from .rating_utils import calculate_payment_status

        recent_revenues = Revenue.objects.filter(
            driver=driver,
            deleted=False
        ).order_by('-date')[:limit]

        payment_history = []
        for revenue in recent_revenues:
            status, points = calculate_payment_status(revenue)
            payment_history.append({
                'date': revenue.date.strftime('%a, %b %d, %Y'),
                'status': 'On Time' if status == 'on_time' else 'Late',
                'points': int(points),
                'amount': float(revenue.amount)
            })

        return payment_history

    def _get_rating_trend(self, driver):
        """Get rating trend for last 5 weeks"""
        from datetime import timedelta
        from django.utils import timezone

        end_date = timezone.now().date()
        weeks = []

        for i in range(5):
            week_end = end_date - timedelta(days=i*7)
            week_start = week_end - timedelta(days=6)

            # Calculate rating for this week
            week_revenues = Revenue.objects.filter(
                driver=driver,
                date__gte=week_start,
                date__lte=week_end,
                deleted=False
            )

            if week_revenues.exists():
                # Simple calculation for weekly rating
                from .rating_utils import get_expected_payment_days, calculate_payment_status

                expected_dates = set()
                for rating in DriverRating.objects.filter(driver=driver):
                    vehicle_expected = get_expected_payment_days(rating.vehicle, week_start, week_end)
                    expected_dates.update(vehicle_expected)

                if expected_dates:
                    payment_map = {revenue.date: revenue for revenue in week_revenues}
                    total_points = 0.0

                    for expected_date in expected_dates:
                        if expected_date in payment_map:
                            status, points = calculate_payment_status(payment_map[expected_date])
                            total_points += points

                    week_rating = total_points / len(expected_dates) if expected_dates else 0
                else:
                    week_rating = 0
            else:
                week_rating = 0

            weeks.append({
                'week': f'Week {5-i}',
                'rating': round(week_rating, 1)
            })

        return list(reversed(weeks))

    def _calculate_revenue_completion_rate(self, driver, start_date, end_date):
        """Calculate payment completion rate based on revenue: actual revenue / expected revenue * 100"""
        from decimal import Decimal

        # Get all vehicles for this driver
        driver_vehicles = []
        for rating in DriverRating.objects.filter(driver=driver):
            driver_vehicles.append(rating.vehicle)

        if not driver_vehicles:
            return 0.0, 0.0, 0.0

        # Calculate expected revenue
        expected_revenue = Decimal('0.00')
        actual_revenue = Decimal('0.00')

        for vehicle in driver_vehicles:
            # Get payment settings for this vehicle's partner
            payment_settings = PaymentSettings.objects.filter(partner=vehicle.partner).first()

            if payment_settings:
                # Count working days for this vehicle in the period
                working_days = self._count_working_days(start_date, end_date, payment_settings.payment_days)
                vehicle_expected_revenue = payment_settings.daily_amount * working_days
                expected_revenue += vehicle_expected_revenue

            # Calculate actual revenue for this vehicle
            vehicle_actual_revenue = Revenue.objects.filter(
                driver=driver,
                vehicle=vehicle,
                date__gte=start_date,
                date__lte=end_date,
                deleted=False
            ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')

            actual_revenue += vehicle_actual_revenue

        # Calculate completion rate
        payment_completion_rate = 0.0
        if expected_revenue > 0:
            payment_completion_rate = round(float((actual_revenue / expected_revenue) * 100), 1)

        return float(expected_revenue), float(actual_revenue), payment_completion_rate

    @action(detail=False, methods=['get'], permission_classes=[IsAuthenticated])
    def performance_report(self, request):
        """Get paginated driver performance report for partners with advanced filtering"""
        if request.user.role != 'partner':
            return Response({"detail": "Only partners can access performance reports."},
                          status=status.HTTP_403_FORBIDDEN)

        try:
            partner = request.user.partner
        except AttributeError:
            return Response({"detail": "Partner profile not found."},
                          status=status.HTTP_404_NOT_FOUND)

        # Get query parameters
        from django.core.paginator import Paginator

        # Pagination
        page = int(request.query_params.get('page', 1))
        page_size = min(int(request.query_params.get('page_size', 20)), 100)

        # Filtering
        age_group = request.query_params.get('age_group')
        vehicle_type = request.query_params.get('vehicle_type')
        vehicle_registration = request.query_params.get('vehicle_registration')
        status_filter = request.query_params.get('status', 'all')  # all, active, inactive
        min_rating = request.query_params.get('min_rating')
        max_rating = request.query_params.get('max_rating')
        sort_by = request.query_params.get('sort_by', 'rating')  # rating, name, success_rate, total_points
        sort_order = request.query_params.get('sort_order', 'desc')  # asc, desc

        # Get partner's drivers
        drivers_qs = Driver.objects.filter(partner=partner)

        # Apply age group filter
        if age_group:
            from .rating_utils import get_driver_age_group
            filtered_drivers = []
            for driver in drivers_qs:
                if get_driver_age_group(driver) == age_group:
                    filtered_drivers.append(driver.id)
            drivers_qs = drivers_qs.filter(id__in=filtered_drivers)

        # Apply vehicle filters
        if vehicle_type:
            drivers_qs = drivers_qs.filter(
                driverrating__vehicle__vehicle_type__name__icontains=vehicle_type
            ).distinct()

        if vehicle_registration:
            drivers_qs = drivers_qs.filter(
                driverrating__vehicle__registration_number__icontains=vehicle_registration
            ).distinct()

        # Get performance data for each driver
        performance_data = []

        for driver in drivers_qs:
            ratings = DriverRating.objects.filter(driver=driver).select_related('vehicle', 'partner')

            if not ratings.exists():
                continue

            # Calculate driver performance
            driver_data = self._calculate_driver_performance_summary(driver, ratings)

            # Apply rating filters
            if min_rating:
                try:
                    if driver_data['current_rating'] < float(min_rating):
                        continue
                except ValueError:
                    pass

            if max_rating:
                try:
                    if driver_data['current_rating'] > float(max_rating):
                        continue
                except ValueError:
                    pass

            # Apply status filter
            if status_filter == 'active' and not driver_data['is_active']:
                continue
            elif status_filter == 'inactive' and driver_data['is_active']:
                continue

            performance_data.append(driver_data)

        # Apply sorting
        reverse_sort = sort_order == 'desc'
        if sort_by == 'rating':
            performance_data.sort(key=lambda x: x['current_rating'], reverse=reverse_sort)
        elif sort_by == 'name':
            performance_data.sort(key=lambda x: x['driver_name'], reverse=reverse_sort)
        elif sort_by == 'success_rate':
            performance_data.sort(key=lambda x: x['success_rate'], reverse=reverse_sort)
        elif sort_by == 'total_points':
            performance_data.sort(key=lambda x: x['total_points_all_time'], reverse=reverse_sort)
        else:
            performance_data.sort(key=lambda x: x['current_rating'], reverse=True)  # Default

        # Apply pagination
        paginator = Paginator(performance_data, page_size)

        try:
            drivers_page = paginator.page(page)
        except:
            return Response({"detail": "Invalid page number."},
                          status=status.HTTP_400_BAD_REQUEST)

        # Calculate summary statistics
        total_drivers = len(performance_data)
        active_drivers = sum(1 for d in performance_data if d['is_active'])
        avg_rating = sum(d['current_rating'] for d in performance_data) / total_drivers if total_drivers > 0 else 0
        total_points_all = sum(d['total_points_all_time'] for d in performance_data)

        return Response({
            "partner_id": partner.id,
            "partner_name": partner.company_name,
            "summary": {
                "total_drivers": total_drivers,
                "active_drivers": active_drivers,
                "inactive_drivers": total_drivers - active_drivers,
                "average_rating": round(avg_rating, 2),
                "total_points_all_drivers": int(total_points_all)
            },
            "pagination": {
                "current_page": page,
                "page_size": page_size,
                "total_pages": paginator.num_pages,
                "total_records": paginator.count,
                "has_next": drivers_page.has_next(),
                "has_previous": drivers_page.has_previous(),
                "next_page": page + 1 if drivers_page.has_next() else None,
                "previous_page": page - 1 if drivers_page.has_previous() else None
            },
            "filters_applied": {
                "age_group": age_group,
                "vehicle_type": vehicle_type,
                "vehicle_registration": vehicle_registration,
                "status": status_filter,
                "min_rating": min_rating,
                "max_rating": max_rating,
                "sort_by": sort_by,
                "sort_order": sort_order
            },
            "drivers": list(drivers_page)
        })

    def _calculate_driver_performance_summary(self, driver, ratings):
        """Calculate driver performance summary for partner report"""
        from datetime import timedelta
        from django.utils import timezone

        # Calculate overall rating
        total_points = sum(rating.total_points for rating in ratings)
        total_payment_days = sum(rating.total_payment_days for rating in ratings)
        overall_rating = float(total_points / total_payment_days) if total_payment_days > 0 else 0.0

        # Get payment performance data (last 30 days)
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=30)

        revenues = Revenue.objects.filter(
            driver=driver,
            date__gte=start_date,
            date__lte=end_date,
            deleted=False
        )

        payment_stats = self._calculate_payment_statistics(driver, revenues, start_date, end_date)

        # Get primary vehicle info
        primary_rating = ratings.first()
        vehicle_info = primary_rating.vehicle.registration_number if primary_rating else "N/A"

        # Get total points across all time
        total_points_all_time = self._get_driver_total_points(driver)

        return {
            "driver_name": f"{driver.first_name} {driver.last_name}".strip(),
            "driver_id": driver.id,
            "driver_code": f"DRV-{driver.id:04d}",
            "vehicle": vehicle_info,
            "age_group": self._get_driver_age_group(driver),
            "current_rating": round(overall_rating, 1),
            "rating_level": self._get_rating_level(overall_rating),
            "payment_days": payment_stats['total_expected_days'],
            "on_time_payments": payment_stats['on_time_count'],
            "late_payments": payment_stats['late_count'],
            "missed_payments": payment_stats['missed_count'],
            "total_points_period": int(total_points),  # Points for current period
            "total_points_all_time": int(total_points_all_time),  # All-time points
            "success_rate": payment_stats['success_rate'],  # Traditional payment frequency based
            "payment_completion_rate": payment_stats['payment_completion_rate'],  # New revenue-based rate
            "expected_revenue": payment_stats['expected_revenue'],
            "actual_revenue": payment_stats['actual_revenue'],
            "is_active": all(rating.is_active for rating in ratings),
            "status": "Active" if all(rating.is_active for rating in ratings) else "Inactive",
            "vehicle_count": ratings.count()
        }

    @action(detail=True, methods=['get'], permission_classes=[IsAuthenticated])
    def payment_history(self, request, pk=None):
        """Get paginated payment history for a specific driver with advanced filtering"""
        driver = self.get_object()

        # Check permissions - drivers can only see their own history, partners can see their drivers' history
        if request.user.role == 'driver':
            if not hasattr(request.user, 'driver') or request.user.driver.id != driver.id:
                return Response({"detail": "You can only view your own payment history."},
                              status=status.HTTP_403_FORBIDDEN)
        elif request.user.role == 'partner':
            try:
                partner = request.user.partner
                if driver.partner != partner:
                    return Response({"detail": "You can only view payment history for your own drivers."},
                                  status=status.HTTP_403_FORBIDDEN)
            except AttributeError:
                return Response({"detail": "Partner profile not found."},
                              status=status.HTTP_404_NOT_FOUND)
        else:
            return Response({"detail": "Access denied."}, status=status.HTTP_403_FORBIDDEN)

        # Get query parameters with enhanced filtering
        from datetime import datetime, timedelta
        from django.utils import timezone
        from django.core.paginator import Paginator

        # Pagination parameters
        page = int(request.query_params.get('page', 1))
        page_size = min(int(request.query_params.get('page_size', 20)), 100)  # Max 100 per page

        # Date filtering
        start_date_str = request.query_params.get('start_date')
        end_date_str = request.query_params.get('end_date')
        days = request.query_params.get('days')

        # Vehicle filtering
        vehicle_id = request.query_params.get('vehicle_id')
        vehicle_registration = request.query_params.get('vehicle_registration')

        # Status filtering
        payment_status = request.query_params.get('status')  # 'on_time', 'late', 'all'

        # Amount filtering
        min_amount = request.query_params.get('min_amount')
        max_amount = request.query_params.get('max_amount')

        # Build base queryset
        revenues_qs = Revenue.objects.filter(
            driver=driver,
            deleted=False
        ).select_related('vehicle')

        # Apply date filters
        if start_date_str and end_date_str:
            try:
                start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
                end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
                revenues_qs = revenues_qs.filter(date__gte=start_date, date__lte=end_date)
            except ValueError:
                return Response({"detail": "Invalid date format. Use YYYY-MM-DD."},
                              status=status.HTTP_400_BAD_REQUEST)
        elif days:
            try:
                days_int = int(days)
                end_date = timezone.now().date()
                start_date = end_date - timedelta(days=days_int)
                revenues_qs = revenues_qs.filter(date__gte=start_date, date__lte=end_date)
            except ValueError:
                return Response({"detail": "Invalid days parameter."},
                              status=status.HTTP_400_BAD_REQUEST)
        else:
            # Default to last 30 days
            end_date = timezone.now().date()
            start_date = end_date - timedelta(days=30)
            revenues_qs = revenues_qs.filter(date__gte=start_date, date__lte=end_date)

        # Apply vehicle filters
        if vehicle_id:
            try:
                revenues_qs = revenues_qs.filter(vehicle_id=int(vehicle_id))
            except ValueError:
                return Response({"detail": "Invalid vehicle_id parameter."},
                              status=status.HTTP_400_BAD_REQUEST)

        if vehicle_registration:
            revenues_qs = revenues_qs.filter(vehicle__registration_number__icontains=vehicle_registration)

        # Apply amount filters
        if min_amount:
            try:
                revenues_qs = revenues_qs.filter(amount__gte=float(min_amount))
            except ValueError:
                return Response({"detail": "Invalid min_amount parameter."},
                              status=status.HTTP_400_BAD_REQUEST)

        if max_amount:
            try:
                revenues_qs = revenues_qs.filter(amount__lte=float(max_amount))
            except ValueError:
                return Response({"detail": "Invalid max_amount parameter."},
                              status=status.HTTP_400_BAD_REQUEST)

        # Order by date (newest first)
        revenues_qs = revenues_qs.order_by('-date', '-created_at')

        # Apply pagination
        paginator = Paginator(revenues_qs, page_size)

        try:
            revenues_page = paginator.page(page)
        except:
            return Response({"detail": "Invalid page number."},
                          status=status.HTTP_400_BAD_REQUEST)

        # Calculate payment status for each record and apply status filter
        payment_history = []
        from .rating_utils import calculate_payment_status

        for revenue in revenues_page:
            status, points = calculate_payment_status(revenue)

            # Apply status filter if specified
            if payment_status and payment_status != 'all':
                if payment_status == 'on_time' and status != 'on_time':
                    continue
                elif payment_status == 'late' and status != 'late':
                    continue

            # Get vehicle information with history tracking
            vehicle_info = self._get_vehicle_info_with_history(revenue.vehicle, revenue.date)

            payment_history.append({
                'id': revenue.id,
                'date': revenue.date.strftime('%Y-%m-%d'),
                'formatted_date': revenue.date.strftime('%a, %b %d, %Y'),
                'amount': float(revenue.amount),
                'vehicle': vehicle_info,
                'status': 'On Time' if status == 'on_time' else 'Late',
                'status_code': status,
                'points': float(points),
                'created_at': revenue.created_at.isoformat() if revenue.created_at else None
            })

        # Calculate summary statistics for all filtered records (not just current page)
        all_filtered_revenues = list(revenues_qs)
        summary_stats = self._calculate_payment_summary(driver, all_filtered_revenues)

        # Get driver's total accumulated points across all time
        total_points_all_time = self._get_driver_total_points(driver)

        return Response({
            "driver_id": driver.id,
            "driver_name": f"{driver.first_name} {driver.last_name}".strip(),
            "driver_code": f"DRV-{driver.id:04d}",
            "total_points_all_time": total_points_all_time,
            "pagination": {
                "current_page": page,
                "page_size": page_size,
                "total_pages": paginator.num_pages,
                "total_records": paginator.count,
                "has_next": revenues_page.has_next(),
                "has_previous": revenues_page.has_previous(),
                "next_page": page + 1 if revenues_page.has_next() else None,
                "previous_page": page - 1 if revenues_page.has_previous() else None
            },
            "filters_applied": {
                "start_date": start_date_str,
                "end_date": end_date_str,
                "days": days,
                "vehicle_id": vehicle_id,
                "vehicle_registration": vehicle_registration,
                "status": payment_status,
                "min_amount": min_amount,
                "max_amount": max_amount
            },
            "payment_history": payment_history,
            "summary": summary_stats
        })

    def _get_vehicle_info_with_history(self, vehicle, payment_date):
        """Get vehicle information with historical context"""
        if not vehicle:
            return {"registration": "N/A", "make": "Unknown", "model": "Unknown", "is_current": False}

        # Check if this was the driver's current vehicle at the time of payment
        # This helps track vehicle switches over time
        return {
            "id": vehicle.id,
            "registration": vehicle.registration_number,
            "make": vehicle.vehicle_make.name if hasattr(vehicle, 'vehicle_make') and vehicle.vehicle_make else "Unknown",
            "model": vehicle.vehicle_model.name if hasattr(vehicle, 'vehicle_model') and vehicle.vehicle_model else "Unknown",
            "type": vehicle.vehicle_type.name if hasattr(vehicle, 'vehicle_type') and vehicle.vehicle_type else "Unknown",
            "payment_date": payment_date.strftime('%Y-%m-%d')
        }

    def _calculate_payment_summary(self, driver, revenues):
        """Calculate comprehensive payment summary statistics"""
        from .rating_utils import calculate_payment_status

        if not revenues:
            return {
                "total_payments": 0,
                "on_time_payments": 0,
                "late_payments": 0,
                "total_amount": 0.0,
                "total_points": 0.0,
                "success_rate": 0,
                "average_amount": 0.0,
                "vehicles_used": []
            }

        on_time_count = 0
        late_count = 0
        total_amount = 0.0
        total_points = 0.0
        vehicles_used = set()

        for revenue in revenues:
            status, points = calculate_payment_status(revenue)
            if status == 'on_time':
                on_time_count += 1
            else:
                late_count += 1

            total_amount += float(revenue.amount)
            total_points += float(points)

            if revenue.vehicle:
                vehicles_used.add(revenue.vehicle.registration_number)

        total_payments = len(revenues)
        success_rate = round((on_time_count / total_payments) * 100) if total_payments > 0 else 0
        average_amount = round(total_amount / total_payments, 2) if total_payments > 0 else 0.0

        return {
            "total_payments": total_payments,
            "on_time_payments": on_time_count,
            "late_payments": late_count,
            "total_amount": round(total_amount, 2),
            "total_points": round(total_points, 2),
            "success_rate": success_rate,
            "average_amount": average_amount,
            "vehicles_used": list(vehicles_used)
        }

    def _get_driver_total_points(self, driver):
        """Get driver's total accumulated points across all time and vehicles"""
        ratings = DriverRating.objects.filter(driver=driver)
        return sum(rating.total_points for rating in ratings)

    @action(detail=True, methods=['post'], permission_classes=[IsAuthenticated])
    def calculate_rating(self, request, pk=None):
        """Manually trigger rating calculation for a specific driver"""
        driver = self.get_object()

        # Check permissions - only partners can trigger calculations for their drivers, admins for all
        if request.user.role == 'partner':
            try:
                partner = request.user.partner
                if driver.partner != partner:
                    return Response({"detail": "You can only calculate ratings for your own drivers."},
                                  status=status.HTTP_403_FORBIDDEN)
            except AttributeError:
                return Response({"detail": "Partner profile not found."},
                              status=status.HTTP_404_NOT_FOUND)
        elif request.user.role != 'admin':
            return Response({"detail": "Only partners and admins can trigger rating calculations."},
                          status=status.HTTP_403_FORBIDDEN)

        # Get parameters
        recalculate_from_date = request.data.get('recalculate_from_date')
        vehicle_id = request.data.get('vehicle_id')  # Optional: calculate for specific vehicle only

        try:
            from .rating_utils import calculate_driver_rating
            from datetime import datetime

            results = []

            if vehicle_id:
                # Calculate for specific vehicle only
                try:
                    vehicle = Vehicle.objects.get(id=vehicle_id)
                    rating = calculate_driver_rating(driver, vehicle, driver.partner)
                    results.append({
                        'vehicle_id': vehicle.id,
                        'vehicle_registration': vehicle.registration_number,
                        'rating_updated': True,
                        'current_rating': float(rating.current_rating) if rating.total_payment_days > 0 else 0.0,
                        'total_points': float(rating.total_points),
                        'payment_days': rating.total_payment_days
                    })
                except Vehicle.DoesNotExist:
                    return Response({"detail": "Vehicle not found."},
                                  status=status.HTTP_404_NOT_FOUND)
            else:
                # Calculate for all driver's vehicles
                vehicles_used = Revenue.objects.filter(
                    driver=driver,
                    deleted=False
                ).values_list('vehicle', flat=True).distinct()

                for vehicle_id in vehicles_used:
                    if vehicle_id:
                        try:
                            vehicle = Vehicle.objects.get(id=vehicle_id)
                            rating = calculate_driver_rating(driver, vehicle, driver.partner)
                            results.append({
                                'vehicle_id': vehicle.id,
                                'vehicle_registration': vehicle.registration_number,
                                'rating_updated': True,
                                'current_rating': float(rating.current_rating) if rating.total_payment_days > 0 else 0.0,
                                'total_points': float(rating.total_points),
                                'payment_days': rating.total_payment_days
                            })
                        except Vehicle.DoesNotExist:
                            results.append({
                                'vehicle_id': vehicle_id,
                                'vehicle_registration': 'Unknown',
                                'rating_updated': False,
                                'error': 'Vehicle not found'
                            })

            # Calculate overall statistics
            total_points_all_time = self._get_driver_total_points(driver)
            active_ratings = DriverRating.objects.filter(driver=driver, is_active=True)
            overall_rating = 0.0

            if active_ratings.exists():
                total_points = sum(r.total_points for r in active_ratings)
                total_days = sum(r.total_payment_days for r in active_ratings)
                overall_rating = float(total_points / total_days) if total_days > 0 else 0.0

            return Response({
                "driver_id": driver.id,
                "driver_name": f"{driver.first_name} {driver.last_name}".strip(),
                "calculation_triggered": True,
                "calculation_timestamp": datetime.now().isoformat(),
                "recalculated_from_date": recalculate_from_date,
                "overall_rating": round(overall_rating, 2),
                "total_points_all_time": int(total_points_all_time),
                "vehicles_processed": len(results),
                "vehicle_results": results
            })

        except Exception as e:
            return Response({
                "driver_id": driver.id,
                "calculation_triggered": False,
                "error": str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=['post'], permission_classes=[IsAuthenticated])
    def transfer_partner(self, request, pk=None):
        """Transfer driver to a new partner while maintaining rating continuity"""
        driver = self.get_object()

        # Only admins can transfer drivers between partners
        if request.user.role != 'admin':
            return Response({"detail": "Only administrators can transfer drivers between partners."},
                          status=status.HTTP_403_FORBIDDEN)

        # Get new partner ID from request
        new_partner_id = request.data.get('new_partner_id')
        if not new_partner_id:
            return Response({"detail": "new_partner_id is required."},
                          status=status.HTTP_400_BAD_REQUEST)

        try:
            new_partner = Partner.objects.get(id=new_partner_id)
        except Partner.DoesNotExist:
            return Response({"detail": "Partner not found."},
                          status=status.HTTP_404_NOT_FOUND)

        # Get current partner
        old_partner = driver.partner
        if old_partner == new_partner:
            return Response({"detail": "Driver is already assigned to this partner."},
                          status=status.HTTP_400_BAD_REQUEST)

        try:
            from .rating_utils import handle_driver_partner_transfer

            # Perform the transfer
            transfer_result = handle_driver_partner_transfer(driver, old_partner, new_partner)

            # Update driver's partner
            driver.partner = new_partner
            driver.save()

            return Response({
                "message": "Driver successfully transferred to new partner with rating continuity maintained.",
                "transfer_details": transfer_result
            })

        except Exception as e:
            return Response({
                "detail": f"Error during partner transfer: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=['get'], permission_classes=[IsAuthenticated])
    def rating_trend(self, request, pk=None):
        """Get rating trend for a specific driver"""
        driver = self.get_object()

        # Check permissions - drivers can only see their own trend, partners can see their drivers' trends
        if request.user.role == 'driver':
            if not hasattr(request.user, 'driver') or request.user.driver.id != driver.id:
                return Response({"detail": "You can only view your own rating trend."},
                              status=status.HTTP_403_FORBIDDEN)
        elif request.user.role == 'partner':
            try:
                partner = request.user.partner
                if driver.partner != partner:
                    return Response({"detail": "You can only view rating trends for your own drivers."},
                                  status=status.HTTP_403_FORBIDDEN)
            except AttributeError:
                return Response({"detail": "Partner profile not found."},
                              status=status.HTTP_404_NOT_FOUND)
        else:
            return Response({"detail": "Access denied."}, status=status.HTTP_403_FORBIDDEN)

        # Get query parameters
        period = request.query_params.get('period', 'weekly')  # weekly, monthly
        weeks = int(request.query_params.get('weeks', 8))  # Default 8 weeks

        if period == 'weekly':
            trend_data = self._get_weekly_rating_trend(driver, weeks)
        else:
            # For future implementation
            trend_data = self._get_weekly_rating_trend(driver, weeks)

        return Response({
            "driver_id": driver.id,
            "driver_name": f"{driver.first_name} {driver.last_name}".strip(),
            "period": period,
            "weeks_analyzed": weeks,
            "trend_data": trend_data,
            "current_rating": trend_data[-1]['rating'] if trend_data else 0.0,
            "trend_direction": self._calculate_trend_direction(trend_data)
        })

    def _get_weekly_rating_trend(self, driver, weeks=8):
        """Get weekly rating trend for specified number of weeks"""
        from datetime import timedelta
        from django.utils import timezone

        end_date = timezone.now().date()
        trend_data = []

        for i in range(weeks):
            week_end = end_date - timedelta(days=i*7)
            week_start = week_end - timedelta(days=6)

            # Calculate rating for this week
            week_revenues = Revenue.objects.filter(
                driver=driver,
                date__gte=week_start,
                date__lte=week_end,
                deleted=False
            )

            week_rating = 0.0
            payment_count = 0

            if week_revenues.exists():
                from .rating_utils import get_expected_payment_days, calculate_payment_status

                # Get expected payment days for this week
                expected_dates = set()
                for rating in DriverRating.objects.filter(driver=driver):
                    vehicle_expected = get_expected_payment_days(rating.vehicle, week_start, week_end)
                    expected_dates.update(vehicle_expected)

                if expected_dates:
                    payment_map = {revenue.date: revenue for revenue in week_revenues}
                    total_points = 0.0

                    for expected_date in expected_dates:
                        if expected_date in payment_map:
                            status, points = calculate_payment_status(payment_map[expected_date])
                            total_points += points
                            payment_count += 1

                    week_rating = total_points / len(expected_dates) if expected_dates else 0

            trend_data.append({
                'week_number': weeks - i,
                'week_label': f'Week {weeks - i}',
                'start_date': week_start.strftime('%Y-%m-%d'),
                'end_date': week_end.strftime('%Y-%m-%d'),
                'rating': round(week_rating, 2),
                'payments_made': payment_count
            })

        return list(reversed(trend_data))

    def _calculate_trend_direction(self, trend_data):
        """Calculate overall trend direction"""
        if len(trend_data) < 2:
            return "insufficient_data"

        first_half = trend_data[:len(trend_data)//2]
        second_half = trend_data[len(trend_data)//2:]

        first_avg = sum(d['rating'] for d in first_half) / len(first_half) if first_half else 0
        second_avg = sum(d['rating'] for d in second_half) / len(second_half) if second_half else 0

        if second_avg > first_avg + 0.2:
            return "improving"
        elif second_avg < first_avg - 0.2:
            return "declining"
        else:
            return "stable"

    @action(detail=False, methods=['get'], permission_classes=[IsAuthenticated])
    def payment_history_report(self, request):
        """Get payment history report for all drivers under a partner"""
        if request.user.role != 'partner':
            return Response({"detail": "Only partners can access payment history reports."},
                          status=status.HTTP_403_FORBIDDEN)

        try:
            partner = request.user.partner
        except AttributeError:
            return Response({"detail": "Partner profile not found."},
                          status=status.HTTP_404_NOT_FOUND)

        # Get query parameters
        days = int(request.query_params.get('days', 30))  # Default last 30 days
        driver_id = request.query_params.get('driver_id')  # Optional filter by specific driver
        limit_per_driver = min(int(request.query_params.get('limit_per_driver', 10)), 50)

        # Get partner's drivers
        drivers = Driver.objects.filter(partner=partner)
        if driver_id:
            drivers = drivers.filter(id=driver_id)

        # Get payment history for each driver
        from datetime import timedelta
        from django.utils import timezone
        from .rating_utils import calculate_payment_status

        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=days)

        drivers_payment_history = []
        total_payments = 0
        total_amount = Decimal("0.00")
        total_on_time = 0
        total_late = 0

        for driver in drivers:
            revenues = Revenue.objects.filter(
                driver=driver,
                date__gte=start_date,
                date__lte=end_date,
                deleted=False
            ).select_related('vehicle').order_by('-date')[:limit_per_driver]

            driver_payments = []
            driver_on_time = 0
            driver_late = 0
            driver_amount = Decimal('0.00')

            for revenue in revenues:
                status, points = calculate_payment_status(revenue)
                payment_record = {
                    'id': revenue.id,
                    'date': revenue.date.strftime('%Y-%m-%d'),
                    'formatted_date': revenue.date.strftime('%a, %b %d, %Y'),
                    'amount': float(revenue.amount),
                    'vehicle': revenue.vehicle.registration_number if revenue.vehicle else 'N/A',
                    'status': 'On Time' if status == 'on_time' else 'Late',
                    'status_code': status,
                    'points': float(points)
                }
                driver_payments.append(payment_record)

                if status == 'on_time':
                    driver_on_time += 1
                else:
                    driver_late += 1
                driver_amount += revenue.amount

            if driver_payments:  # Only include drivers with payments
                drivers_payment_history.append({
                    'driver_id': driver.id,
                    'driver_name': f"{driver.first_name} {driver.last_name}".strip(),
                    'total_payments': len(driver_payments),
                    'on_time_payments': driver_on_time,
                    'late_payments': driver_late,
                    'total_amount': driver_amount,
                    'success_rate': round((driver_on_time / len(driver_payments)) * 100) if driver_payments else 0,
                    'recent_payments': driver_payments
                })

                total_payments += len(driver_payments)
                total_amount += driver_amount
                total_on_time += driver_on_time
                total_late += driver_late

        return Response({
            "partner_id": partner.id,
            "partner_name": partner.company_name,
            "period": {
                "start_date": start_date.strftime('%Y-%m-%d'),
                "end_date": end_date.strftime('%Y-%m-%d'),
                "days": days
            },
            "summary": {
                "total_drivers_with_payments": len(drivers_payment_history),
                "total_payments": total_payments,
                "total_amount": float(total_amount),
                "on_time_payments": total_on_time,
                "late_payments": total_late,
                "overall_success_rate": round((total_on_time / total_payments) * 100) if total_payments > 0 else 0
            },
            "drivers": drivers_payment_history,
            "filters_applied": {
                "days": days,
                "driver_id": driver_id,
                "limit_per_driver": limit_per_driver
            }
        })

    @action(detail=True, methods=['post'], permission_classes=[IsAuthenticated])
    def flag_driver(self, request, pk=None):
        """Flag a driver for suspicious behavior or false information"""
        driver = self.get_object()

        # Only partners can flag drivers
        if request.user.role != 'partner':
            return Response({"detail": "Only partners can flag drivers."},
                          status=status.HTTP_403_FORBIDDEN)

        try:
            partner = request.user.partner
        except AttributeError:
            return Response({"detail": "Partner profile not found."},
                          status=status.HTTP_404_NOT_FOUND)

        # Get reason from request (mandatory)
        reason = request.data.get('reason', '').strip()
        if not reason:
            return Response({"detail": "Reason for flagging is required."},
                          status=status.HTTP_400_BAD_REQUEST)

        # Check if this partner has already flagged this driver
        from .models import DriverFlag
        existing_flag = DriverFlag.objects.filter(
            driver=driver,
            partner=partner,
            is_active=True
        ).first()

        if existing_flag:
            return Response({"detail": "You have already flagged this driver."},
                          status=status.HTTP_400_BAD_REQUEST)

        try:
            # Create the flag
            flag = DriverFlag.objects.create(
                driver=driver,
                partner=partner,
                reason=reason,
                flagged_by_user=request.user
            )

            # Check if driver should be locked based on flag count
            self._check_and_apply_driver_lock(driver)

            return Response({
                "message": "Driver flagged successfully.",
                "flag_id": flag.id,
                "driver_id": driver.id,
                "driver_name": f"{driver.first_name} {driver.last_name}".strip(),
                "reason": reason,
                "flagged_at": flag.created_at.isoformat(),
                "total_flags": DriverFlag.get_active_flags_count(driver),
                "unique_partners_flagged": DriverFlag.get_unique_partners_flagged_count(driver)
            })

        except Exception as e:
            return Response({
                "detail": f"Error flagging driver: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=['get'], permission_classes=[IsAuthenticated])
    def flag_status(self, request, pk=None):
        """Get flagging status and history for a driver"""
        driver = self.get_object()

        # Partners can only see flag status for drivers applying to their jobs
        # Admins can see all flag statuses
        if request.user.role not in ['partner', 'admin']:
            return Response({"detail": "Access denied."},
                          status=status.HTTP_403_FORBIDDEN)

        from .models import DriverFlag
        from datetime import timedelta

        # Get active flags within last 6 months
        active_flags = DriverFlag.objects.filter(
            driver=driver,
            is_active=True,
            created_at__gte=timezone.now() - timedelta(days=180)  # 6 months
        ).select_related('partner')

        # Prepare flag details
        flag_details = []
        for flag in active_flags:
            flag_info = {
                'flag_id': flag.id,
                'partner_name': flag.partner.company_name,
                'reason': flag.reason,
                'flagged_at': flag.created_at.isoformat(),
                'days_ago': (timezone.now() - flag.created_at).days
            }

            # Only show partner name to admins, hide from other partners
            if request.user.role != 'admin':
                flag_info['partner_name'] = 'Anonymous Partner'

            flag_details.append(flag_info)

        total_flags = len(flag_details)
        unique_partners = DriverFlag.get_unique_partners_flagged_count(driver)

        return Response({
            "driver_id": driver.id,
            "driver_name": f"{driver.first_name} {driver.last_name}".strip(),
            "is_flagged": total_flags > 0,
            "total_flags": total_flags,
            "unique_partners_flagged": unique_partners,
            "is_locked": driver.is_account_locked(),
            "lock_type": driver.lock_type,
            "locked_at": driver.locked_at.isoformat() if driver.locked_at else None,
            "lock_expires_at": driver.lock_expires_at.isoformat() if driver.lock_expires_at else None,
            "flag_details": flag_details
        })

    def _check_and_apply_driver_lock(self, driver):
        """Check if driver should be locked based on flagging rules and apply lock if necessary"""
        from .models import DriverFlag
        from datetime import timedelta

        # Get unique partners who flagged this driver in last 6 months
        unique_partners_count = DriverFlag.get_unique_partners_flagged_count(driver, months_back=6)

        # Apply locking rules
        if unique_partners_count >= 3:
            # Permanent lock for 3+ different partners
            driver.is_locked = True
            driver.lock_type = 'permanent'
            driver.locked_at = timezone.now()
            driver.lock_expires_at = None
            driver.lock_reason = f"Permanently locked due to flags from {unique_partners_count} different partners"
            driver.save()

            # Send permanent ban email
            self._send_driver_lock_email(driver, 'permanent')

            # Create in-system notification
            self._create_driver_lock_notification(driver, 'permanent')

        elif unique_partners_count >= 2:
            # Temporary lock for 2 different partners (6 months)
            if not driver.is_locked or driver.lock_type != 'temporary':
                driver.is_locked = True
                driver.lock_type = 'temporary'
                driver.locked_at = timezone.now()
                driver.lock_expires_at = timezone.now() + timedelta(days=180)  # 6 months
                driver.lock_reason = f"Temporarily locked for 6 months due to flags from {unique_partners_count} different partners"
                driver.save()

                # Send temporary lock email
                self._send_driver_lock_email(driver, 'temporary')

                # Create in-system notification
                self._create_driver_lock_notification(driver, 'temporary')

    def _send_driver_lock_email(self, driver, lock_type):
        """Send email notification to driver about account lock"""
        try:
            from django.core.mail import send_mail
            from django.conf import settings

            driver_name = f"{driver.first_name} {driver.last_name}".strip() or "Driver"

            if lock_type == 'permanent':
                subject = "Account Permanently Banned - KadeReconnect"
                message = f"Dear {driver_name},\n\nYour account has been permanently banned due to partner flagging.\n\nIf you believe this is an error, please contact support.\n\nBest regards,\nKadeReconnect Team"
            else:
                subject = "Account Temporarily Locked - KadeReconnect"
                message = f"Dear {driver_name},\n\nYour account has been locked for 6 months due to partner flagging.\n\nYour account will be automatically unlocked on {driver.lock_expires_at.strftime('%B %d, %Y') if driver.lock_expires_at else 'N/A'}.\n\nIf you believe this is an error, please contact support.\n\nBest regards,\nKadeReconnect Team"

            send_mail(
                subject,
                message,
                settings.DEFAULT_FROM_EMAIL,
                [driver.email],
                fail_silently=True
            )
        except Exception as e:
            print(f"Failed to send lock email to driver {driver.id}: {str(e)}")

    def _create_driver_lock_notification(self, driver, lock_type):
        """Create in-system notification for driver account lock"""
        try:
            from .models import Notification

            driver_name = f"{driver.first_name} {driver.last_name}".strip() or "Driver"

            if lock_type == 'permanent':
                title = "Account Permanently Banned"
                message = f"Dear {driver_name}, your account has been permanently banned due to partner flagging. If you believe this is an error, please contact support."
            else:
                title = "Account Temporarily Locked"
                expires_date = driver.lock_expires_at.strftime('%B %d, %Y') if driver.lock_expires_at else 'N/A'
                message = f"Dear {driver_name}, your account has been locked for 6 months due to partner flagging. Your account will be automatically unlocked on {expires_date}. If you believe this is an error, please contact support."

            # Create notification
            Notification.objects.create(
                user=driver.user,
                title=title,
                message=message,
                notification_type='account_lock',
                is_read=False
            )

            print(f"Lock notification created for driver {driver.id}")

        except Exception as e:
            print(f"Failed to create lock notification for driver {driver.id}: {str(e)}")

    @action(detail=False, methods=['get'], permission_classes=[IsAuthenticated])
    def flagged_drivers(self, request):
        """Get list of flagged drivers for partners to review"""
        # Only partners and admins can access this
        if request.user.role not in ['partner', 'admin']:
            return Response({"detail": "Access denied."},
                          status=status.HTTP_403_FORBIDDEN)

        from .models import DriverFlag
        from datetime import timedelta
        from django.core.paginator import Paginator

        # Pagination parameters
        page = int(request.query_params.get('page', 1))
        page_size = min(int(request.query_params.get('page_size', 20)), 100)

        # Filter parameters
        lock_status = request.query_params.get('lock_status', 'all')  # all, locked, unlocked
        min_flags = request.query_params.get('min_flags', 1)

        # Get flagged drivers (within last 6 months)
        cutoff_date = timezone.now() - timedelta(days=180)

        # Get drivers with active flags
        flagged_driver_ids = DriverFlag.objects.filter(
            is_active=True,
            created_at__gte=cutoff_date
        ).values_list('driver_id', flat=True).distinct()

        drivers_qs = Driver.objects.filter(id__in=flagged_driver_ids)

        # Apply lock status filter
        if lock_status == 'locked':
            drivers_qs = drivers_qs.filter(is_locked=True)
        elif lock_status == 'unlocked':
            drivers_qs = drivers_qs.filter(is_locked=False)

        # Prepare driver data with flag information
        flagged_drivers_data = []
        for driver in drivers_qs:
            # Get flag count and unique partners
            total_flags = DriverFlag.get_active_flags_count(driver)
            unique_partners = DriverFlag.get_unique_partners_flagged_count(driver)

            # Apply min_flags filter
            if total_flags < int(min_flags):
                continue

            # Get recent flags for this driver
            recent_flags = DriverFlag.objects.filter(
                driver=driver,
                is_active=True,
                created_at__gte=cutoff_date
            ).select_related('partner').order_by('-created_at')[:3]

            flag_summary = []
            for flag in recent_flags:
                flag_summary.append({
                    'partner_name': flag.partner.company_name if request.user.role == 'admin' else 'Anonymous Partner',
                    'reason': flag.reason[:100] + '...' if len(flag.reason) > 100 else flag.reason,
                    'flagged_at': flag.created_at.isoformat(),
                    'days_ago': (timezone.now() - flag.created_at).days
                })

            flagged_drivers_data.append({
                'driver_id': driver.id,
                'driver_name': f"{driver.first_name} {driver.last_name}".strip(),
                'driver_code': f"DRV-{driver.id:04d}",
                'email': driver.email,
                'total_flags': total_flags,
                'unique_partners_flagged': unique_partners,
                'is_locked': driver.is_account_locked(),
                'lock_type': driver.lock_type,
                'locked_at': driver.locked_at.isoformat() if driver.locked_at else None,
                'lock_expires_at': driver.lock_expires_at.isoformat() if driver.lock_expires_at else None,
                'recent_flags': flag_summary
            })

        # Sort by total flags (highest first)
        flagged_drivers_data.sort(key=lambda x: x['total_flags'], reverse=True)

        # Apply pagination
        paginator = Paginator(flagged_drivers_data, page_size)

        try:
            drivers_page = paginator.page(page)
        except:
            return Response({"detail": "Invalid page number."},
                          status=status.HTTP_400_BAD_REQUEST)

        return Response({
            "summary": {
                "total_flagged_drivers": len(flagged_drivers_data),
                "locked_drivers": sum(1 for d in flagged_drivers_data if d['is_locked']),
                "unlocked_drivers": sum(1 for d in flagged_drivers_data if not d['is_locked'])
            },
            "pagination": {
                "current_page": page,
                "page_size": page_size,
                "total_pages": paginator.num_pages,
                "total_records": paginator.count,
                "has_next": drivers_page.has_next(),
                "has_previous": drivers_page.has_previous()
            },
            "filters_applied": {
                "lock_status": lock_status,
                "min_flags": min_flags,
                "period": "Last 6 months"
            },
            "flagged_drivers": list(drivers_page)
        })


# Driver Flag Management ViewSet (for admins)
class DriverFlagViewSet(viewsets.ModelViewSet):
    """ViewSet for managing driver flags (admin only)"""
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        # Only admins can access this
        if self.request.user.role != 'admin':
            return DriverFlag.objects.none()

        from .models import DriverFlag
        return DriverFlag.objects.all().select_related('driver', 'partner', 'flagged_by_user')

    def get_serializer_class(self):
        from .serializers import DriverFlagSerializer
        return DriverFlagSerializer

    @action(detail=True, methods=['post'])
    def resolve_flag(self, request, pk=None):
        """Resolve a driver flag (admin only)"""
        if request.user.role != 'admin':
            return Response({"detail": "Only administrators can resolve flags."},
                          status=status.HTTP_403_FORBIDDEN)

        flag = self.get_object()
        resolution_notes = request.data.get('resolution_notes', '')

        flag.resolve_flag(request.user, resolution_notes)

        return Response({
            "message": "Flag resolved successfully.",
            "flag_id": flag.id,
            "resolved_at": flag.resolved_at.isoformat(),
            "resolved_by": f"{request.user.first_name} {request.user.last_name}".strip(),
            "resolution_notes": resolution_notes
        })

    @action(detail=False, methods=['post'])
    def unlock_driver(self, request):
        """Unlock a driver account (admin only)"""
        if request.user.role != 'admin':
            return Response({"detail": "Only administrators can unlock driver accounts."},
                          status=status.HTTP_403_FORBIDDEN)

        driver_id = request.data.get('driver_id')
        if not driver_id:
            return Response({"detail": "driver_id is required."},
                          status=status.HTTP_400_BAD_REQUEST)

        try:
            driver = Driver.objects.get(id=driver_id)
        except Driver.DoesNotExist:
            return Response({"detail": "Driver not found."},
                          status=status.HTTP_404_NOT_FOUND)

        if not driver.is_locked:
            return Response({"detail": "Driver account is not locked."},
                          status=status.HTTP_400_BAD_REQUEST)

        # Unlock the account
        driver.unlock_account()

        # Create notification
        try:
            from .models import Notification
            Notification.objects.create(
                user=driver.user,
                title="Account Unlocked",
                message=f"Your account has been unlocked by an administrator. You can now access your account normally.",
                notification_type='account_unlock',
                is_read=False
            )
        except Exception as e:
            print(f"Failed to create unlock notification: {str(e)}")

        return Response({
            "message": "Driver account unlocked successfully.",
            "driver_id": driver.id,
            "driver_name": f"{driver.first_name} {driver.last_name}".strip(),
            "unlocked_at": timezone.now().isoformat(),
            "unlocked_by": f"{request.user.first_name} {request.user.last_name}".strip()
        })


class PartnerDriverViewSet(CustomPermissionMixin,
                           mixins.CreateModelMixin,
                           mixins.RetrieveModelMixin,
                           mixins.UpdateModelMixin,
                           mixins.DestroyModelMixin,
                           mixins.ListModelMixin,
                           viewsets.GenericViewSet):
    serializer_class = DriverSerializer
    parser_classes = [MultiPartParser, FormParser]

    def get_queryset(self):
        user = self.request.user

        if user.is_authenticated and user.role == 'partner':
            partner = get_object_or_404(Partner, user=user)
            # Only show partner's linked drivers
            return Driver.objects.filter(partner=partner)

        return Driver.objects.all() if user.is_staff else Driver.objects.none()

    @action(detail=True, methods=['post'], permission_classes=[IsAuthenticated])
    def unhire(self, request, pk=None):
        """Unhire a driver by removing their partner link, unlinking from any vehicle, and updating job applications"""
        user = request.user

        if user.role != 'partner':
            return Response({"detail": "Only partners can unhire drivers."}, status=status.HTTP_403_FORBIDDEN)

        partner = get_object_or_404(Partner, user=user)
        driver = get_object_or_404(Driver, pk=pk, partner=partner)

        # Unlink the driver from any assigned vehicle
        assigned_vehicle = Vehicle.objects.filter(driver=driver).first()
        if assigned_vehicle:
            assigned_vehicle.driver = None
            assigned_vehicle.save()

        # Unlink the driver from the partner
        driver.partner = None
        driver.save()

        # Update all job applications where this driver was hired
        JobApplication.objects.filter(driver=driver, status='hired').update(
            status='unhired',
            un_hired_at=timezone.now(),
            hired_at=None
        )

        return Response({"detail": "Driver successfully unhired, vehicle unassigned, and job applications updated."},
                        status=status.HTTP_200_OK)


# partner view
class PartnerViewSet(CustomPermissionMixin,
                     mixins.CreateModelMixin,
                     mixins.RetrieveModelMixin,
                     mixins.UpdateModelMixin,
                     mixins.DestroyModelMixin,
                     mixins.ListModelMixin,
                     viewsets.GenericViewSet):
    queryset = Partner.objects.all()
    serializer_class = PartnerSerializer
    parser_classes = [MultiPartParser, FormParser]

# job view
class IsPartnerOrDriver(BasePermission):
    def has_permission(self, request, view):
        if view.action in ['list', 'retrieve']:
            return True

        if request.user.is_authenticated and request.user.role == 'partner':
            return True

        if request.user.is_authenticated and request.user.role == 'driver':
            return view.action in ['list', 'retrieve']

        return False

# # Job View
class JobViewSet(mixins.CreateModelMixin,
                 mixins.RetrieveModelMixin,
                 mixins.UpdateModelMixin,
                 mixins.DestroyModelMixin,
                 mixins.ListModelMixin,
                 viewsets.GenericViewSet):
    serializer_class = JobSerializer
    permission_classes = [IsPartnerOrDriver]
    parser_classes = [MultiPartParser, FormParser]

    def perform_create(self, serializer):
        try:
            partner = Partner.objects.get(user=self.request.user)
        except Partner.DoesNotExist:
            raise ValidationError({
                "detail": "No Partner profile found for the logged-in user. Please ensure you have a Partner profile before posting a job."
            })
        serializer.save(partner=partner)

    def get_queryset(self):
        user = self.request.user

        if not user.is_authenticated:
            return Job.objects.filter(status='open')

        if user.role == 'partner':
            try:
                partner = Partner.objects.get(user=user)
                return Job.objects.filter(partner=partner, status='open')
            except Partner.DoesNotExist:
                return Job.objects.none()

        if user.role == 'driver':
            return Job.objects.filter(status='open')

        return Job.objects.none()

     # Custom action to close a job

    @action(detail=True, methods=['post'], permission_classes=[IsAuthenticated])
    def close_job(self, request, pk=None):
        job = get_object_or_404(Job, pk=pk)

        # Check if the user is a partner and owns the job
        if request.user.role == 'partner' and job.partner.user == request.user:
            job.status = 'closed'
            job.save()

            # Update job applications associated with the closed job
            job.applications.update(status='rejected')

            return Response({"detail": "Job successfully closed."}, status=status.HTTP_200_OK)

        return Response({"detail": "You do not have permission to close this job."}, status=status.HTTP_403_FORBIDDEN)


# Job application
class JobApplicationViewSet(viewsets.GenericViewSet,
                            mixins.CreateModelMixin,
                            mixins.RetrieveModelMixin,
                            mixins.UpdateModelMixin,
                            mixins.ListModelMixin):
    queryset = JobApplication.objects.all()
    serializer_class = JobApplicationSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        user = self.request.user

        # Ensure user is authenticated before accessing role
        if not user.is_authenticated:
            return JobApplication.objects.none()

        if user.role == 'partner':
            partner = get_object_or_404(Partner, user=user)
            return JobApplication.objects.filter(
                job__partner=partner,
                job__status='open',
                removed_by_partner=False,
                withdrawn_by_driver=False
            ).exclude(status__in=['hired', 'unhired'])

        if user.role == 'driver':
            driver = get_object_or_404(Driver, user=user)
            return JobApplication.objects.filter(
                driver=driver,
                job__status='open',
                withdrawn_by_driver=False
            )

        return JobApplication.objects.none()

    def perform_create(self, serializer):
        user = self.request.user

        # Ensure user is authenticated before proceeding
        if not user.is_authenticated:
            raise ValidationError(
                "User must be authenticated to apply for a job.")

        driver = get_object_or_404(Driver, user=user)
        job = get_object_or_404(Job, id=self.request.data.get('job'))

        if JobApplication.objects.filter(job=job, driver=driver).exists():
            raise ValidationError("You have already applied for this job.")

        serializer.save(driver=driver, job=job)

    @action(detail=True, methods=['post'], permission_classes=[IsAuthenticated])
    def remove_by_partner(self, request, pk=None):
        try:
            application = self.get_object()
            user = request.user

            if not user.is_authenticated:
                return Response({"detail": "User must be authenticated."}, status=status.HTTP_401_UNAUTHORIZED)

            if user.role != 'partner':
                return Response({"detail": "Only partners can remove applications."}, status=status.HTTP_403_FORBIDDEN)

            partner = get_object_or_404(Partner, user=user)
            if application.job.partner != partner:
                return Response({"detail": "You do not have permission to remove this application."}, status=status.HTTP_403_FORBIDDEN)

            # Mark the application as removed and rejected
            application.removed_by_partner = True
            application.status = 'rejected'
            application.save()

            # Send rejection email
            try:
                send_driver_rejected_email(
                    application.driver, partner, application.job)
                logger.info(
                    f"Rejection email sent for application {application.id}")
            except Exception as e:
                logger.error(
                    f"Failed to send rejection email for application {application.id}: {str(e)}")
                return Response({
                    "detail": "Application removed but failed to send email notification.",
                    "error": str(e)
                }, status=status.HTTP_200_OK)

            return Response({"detail": "Application removed and driver notified via email."})

        except Exception as e:
            logger.error(f"Error in remove_by_partner: {str(e)}")
            return Response({"detail": "An error occurred while processing your request."}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=['post'], permission_classes=[IsAuthenticated])
    def withdraw_by_driver(self, request, pk=None):
        application = self.get_object()
        user = request.user

        # Ensure user is authenticated
        if not user.is_authenticated:
            return Response({"detail": "User must be authenticated."}, status=status.HTTP_401_UNAUTHORIZED)

        if user.role != 'driver':
            return Response({"detail": "Only drivers can withdraw applications."}, status=status.HTTP_403_FORBIDDEN)

        driver = get_object_or_404(Driver, user=user)
        if application.driver != driver:
            return Response({"detail": "You do not have permission to withdraw this application."}, status=status.HTTP_403_FORBIDDEN)

        # Mark the application as withdrawn
        application.withdrawn_by_driver = True
        application.save()

        return Response({"detail": "Application withdrawn and removed from both driver and partner lists."})

    @action(detail=True, methods=['post'], permission_classes=[IsAuthenticated])
    def hire_driver(self, request, pk=None):
        try:
            application = self.get_object()
            user = request.user

            if user.role != 'partner':
                return Response({"detail": "Only partners can hire drivers."}, status=status.HTTP_403_FORBIDDEN)

            partner = get_object_or_404(Partner, user=user)

            if application.job.partner != partner:
                return Response({"detail": "You do not have permission to hire for this job."}, status=status.HTTP_403_FORBIDDEN)

            driver = application.driver

            # Check if already hired
            existing_hire = JobApplication.objects.filter(
                driver=driver,
                job__partner=partner,
                status='hired'
            ).exists()

            if existing_hire:
                return Response({"detail": "This driver is already hired for another job by you."}, status=status.HTTP_400_BAD_REQUEST)

            # Link driver to partner and update application
            driver.partner = partner
            driver.save()

            application.status = 'hired'
            application.hired_at = timezone.now()
            application.un_hired_at = None
            application.save()

            # Send hired email notification
            try:
                send_driver_hired_email(driver, partner, application.job)
                logger.info(
                    f"Hire notification email sent for application {application.id}")
            except Exception as e:
                logger.error(
                    f"Failed to send hire notification email for application {application.id}: {str(e)}")
                return Response({
                    "detail": "Driver hired but failed to send email notification.",
                    "error": str(e)
                }, status=status.HTTP_200_OK)

            return Response({"detail": "Driver successfully hired and notification sent via email."}, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error in hire_driver: {str(e)}")
            return Response({"detail": "An error occurred while processing your request."}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# password reset
class PasswordResetRequestView(APIView):
    def post(self, request):
        serializer = PasswordResetSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response({"detail": "Password reset link has been sent to your email address."}, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

# password confirm view
class PasswordResetConfirmView(APIView):
    def post(self, request):
        # Retrieve UID and token from request data
        uid = request.data.get('uid')
        token = request.data.get('token')

        print(f"Received UID: {uid}, Token: {token}")  # Debugging line

        # Pass UID and token to serializer
        serializer = PasswordResetConfirmSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()

            # Delete the token after successful password reset
            PasswordResetToken.objects.filter(
                user=serializer.validated_data['user']).delete()

            return Response({"detail": "Your password has been successfully reset! You can now sign in."}, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


# Permissions (Partners can create/update/delete, Drivers can only view)
class IsPartnerOrReadOnly(permissions.BasePermission):
    def has_permission(self, request, view):
        if request.method in permissions.SAFE_METHODS:
            # Allow GET requests for all users (Drivers & Partners)
            return True
        # Only partners can modify
        return request.user.is_authenticated and hasattr(request.user, 'partner')

# ViewSet using Mixins
class VehicleViewSet(mixins.CreateModelMixin, mixins.RetrieveModelMixin, mixins.UpdateModelMixin,
                     mixins.DestroyModelMixin, mixins.ListModelMixin, viewsets.GenericViewSet):
    queryset = Vehicle.objects.all()
    serializer_class = VehicleSerializer
    permission_classes = [IsPartnerOrReadOnly]

    def get_queryset(self):
        user = self.request.user
        if hasattr(user, 'partner'):
            # Partners see only active vehicles
            return Vehicle.objects.filter(partner=user.partner, status='active')
        elif hasattr(user, 'driver'):
            # Drivers see only active vehicles
            return Vehicle.objects.filter(driver=user.driver, status='active')
        return Vehicle.objects.none()

    def perform_create(self, serializer):
        user = self.request.user
        if not hasattr(user, 'partner'):
            raise serializers.ValidationError(
                {"error": "Only partners can register vehicles."})
        serializer.save(partner=user.partner)

    # Assign or Unassign a Driver

    @action(detail=True, methods=['PATCH'], permission_classes=[IsPartnerOrReadOnly])
    def assign_driver(self, request, pk=None):
        try:
            vehicle = self.get_object()

            if vehicle.partner != request.user.partner:
                return Response({"error": "You do not have permission to assign a driver to this vehicle."},
                                status=status.HTTP_403_FORBIDDEN)

            driver_id = request.data.get('driver_id')

            if driver_id:  # Assign a driver
                try:
                    driver = Driver.objects.get(id=driver_id)

                    # Ensure driver is not already assigned to another vehicle
                    existing_vehicle = Vehicle.objects.filter(
                        driver=driver).first()
                    if existing_vehicle:
                        return Response({"error": f"Driver {driver_id} is already assigned to another vehicle."},
                                        status=status.HTTP_400_BAD_REQUEST)

                    vehicle.driver = driver
                    vehicle.save()
                    return Response({"success": f"Driver {driver_id} assigned to vehicle {vehicle.id}."})

                except Driver.DoesNotExist:
                    return Response({"error": "Driver not found."}, status=status.HTTP_404_NOT_FOUND)

            else:  # Remove driver
                vehicle.driver = None
                vehicle.save()
                return Response({"success": f"Driver unassigned from vehicle {vehicle.id}."})

        except Vehicle.DoesNotExist:
            return Response({"error": "Vehicle not found."}, status=status.HTTP_404_NOT_FOUND)

        # Remove Vehicle (Set Status to Inactive)
    @action(detail=True, methods=['PATCH'], permission_classes=[IsPartnerOrReadOnly])
    def remove_vehicle(self, request, pk=None):
        try:
            vehicle = self.get_object()

            # Only the partner who owns the vehicle can deactivate it
            if vehicle.partner != request.user.partner:
                return Response({"error": "You do not have permission to remove this vehicle."},
                                status=status.HTTP_403_FORBIDDEN)

            # Remove driver association before setting status to inactive
            vehicle.driver = None
            vehicle.status = 'inactive'
            vehicle.save()

            return Response({
                "success": f"Vehicle {vehicle.registration_number} has been removed and driver association cleared."
            })

        except Vehicle.DoesNotExist:
            return Response({"error": "Vehicle not found."}, status=status.HTTP_404_NOT_FOUND)

# Notification viewset
class NotificationPreferenceView(APIView):
    """
    API endpoint to view and update notification preferences for the current user
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get current notification preferences"""
        user = request.user

        # The notification_subscribe field in the User model is the source of truth
        is_subscribed = user.notification_subscribe

        # For consistency, check the NotificationSubscriber table too
        has_subscriber_record = NotificationSubscriber.objects.filter(
            user=user).exists()

        # If there's a mismatch, fix it (should be rare due to signal handler)
        if is_subscribed != has_subscriber_record:
            if is_subscribed:
                # User is subscribed but missing from NotificationSubscriber table
                NotificationSubscriber.objects.get_or_create(user=user)
            else:
                # User is not subscribed but exists in NotificationSubscriber table
                NotificationSubscriber.objects.filter(user=user).delete()

        # If user is subscribed, return the subscriber data
        if is_subscribed:
            subscriber = NotificationSubscriber.objects.get(user=user)
            return Response({
                'user': user.id,
                'email': user.email,
                'subscribed': True,
                'subscribed_at': subscriber.subscribed_at
            })

        # Otherwise, return data indicating user is not subscribed
        return Response({
            'user': user.id,
            'email': user.email,
            'subscribed': False
        })

    def put(self, request):
        """Update notification preferences"""
        user = request.user

        subscribed = request.data.get('subscribed')
        if subscribed is None:
            return Response({'error': 'Missing subscribed parameter'}, status=status.HTTP_400_BAD_REQUEST)

        # Update User model - the signal handler will handle the NotificationSubscriber table
        user.notification_subscribe = subscribed
        user.save(update_fields=['notification_subscribe'])

        # Return the updated preferences
        if subscribed:
            # The signal should have created this, but we'll get it for the response
            subscriber = NotificationSubscriber.objects.get(user=user)
            return Response({
                'subscribed': True,
                'user': user.id,
                'email': user.email,
                'subscribed_at': subscriber.subscribed_at
            })
        else:
            return Response({
                'subscribed': False,
                'user': user.id,
                'email': user.email
            })

# Expenditure ViewSet
class ExpenditureViewSet(mixins.CreateModelMixin,
                         mixins.RetrieveModelMixin,
                         mixins.ListModelMixin,
                         mixins.UpdateModelMixin,
                         mixins.DestroyModelMixin,
                         viewsets.GenericViewSet):
    """
    API endpoints for partners to manage vehicle expenditures.
    Partners can only create/view expenditures for vehicles they own..
    Partners can only create/view expenditures for vehicles they own.
    """
    serializer_class = ExpenditureSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """
        Return only expenses for vehicles owned by the current partner
        """
        user = self.request.user
        if not user.is_authenticated or user.role != 'partner':
            return Expenditure.objects.none()

        try:
            partner = Partner.objects.get(user=user)
            return Expenditure.objects.filter(partner=partner, deleted=False).order_by('-date')
        except Partner.DoesNotExist:
            return Expenditure.objects.none()

    def perform_create(self, serializer):
        """
        Set the partner and validate vehicle ownership before creating expenditure
        """
        partner = get_object_or_404(Partner, user=self.request.user)
        vehicle_id = self.request.data.get('vehicle')

        # Verify the vehicle exists and belongs to the partner
        vehicle = get_object_or_404(Vehicle, id=vehicle_id)
        if vehicle.partner != partner:
            raise ValidationError({
                "vehicle": "You can only create expenditures for vehicles you own."
            })

        serializer.save(partner=partner)
        
    def partial_update(self, request, *args, **kwargs):
        """
        Handle PATCH requests, including soft deletion by setting deleted=True
        """
        instance = self.get_object()
        if request.data.get('deleted', False):
            # Soft delete: set deleted=True and save
            instance.deleted = True
            instance.save()
            return Response(status=status.HTTP_204_NO_CONTENT)
        # Otherwise, proceed with normal partial update
        return super().partial_update(request, *args, **kwargs)


# Revenue viewset class
class RevenueViewSet(viewsets.ModelViewSet):
    serializer_class = RevenueSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        user = self.request.user
        if not user.is_authenticated:
            return Revenue.objects.none()

        # Check if user has associated driver profile
        driver = getattr(user, 'driver', None)
        if driver:
            return Revenue.objects.filter(driver=driver, deleted=False)

        # Check if user has associated partner profile
        partner = getattr(user, 'partner', None)
        if partner:
            return Revenue.objects.filter(driver__partner=partner, deleted=False)

        return Revenue.objects.none()

    def perform_create(self, serializer):
        driver = getattr(self.request.user, 'driver', None)
        if not driver:
            raise ValidationError("Only drivers can submit revenue.")
        serializer.save(driver=driver)
        
    def partial_update(self, request, *args, **kwargs):
        """
        Handle PATCH requests, including soft deletion by setting deleted=True
        """
        instance = self.get_object()
        if request.data.get('deleted', False):
            # Soft delete: set deleted=True and save
            instance.deleted = True
            instance.save()
            return Response(status=status.HTTP_204_NO_CONTENT)
        # Otherwise, proceed with normal partial update
        return super().partial_update(request, *args, **kwargs)


# Add PaymentSettingsViewSet
class PaymentSettingsViewSet(mixins.CreateModelMixin,
                             mixins.UpdateModelMixin,
                             mixins.ListModelMixin,
                             mixins.RetrieveModelMixin,
                             viewsets.GenericViewSet):
    permission_classes = [IsAuthenticated]
    serializer_class = PaymentSettingsSerializer

    def get_queryset(self):
        user = self.request.user
        if not user.is_authenticated:
            return PaymentSettings.objects.none()

        if user.role != 'partner':
            raise ValidationError("Only partners can access payment settings.")

        try:
            partner = user.partner
        except AttributeError:
            raise ValidationError("Partner profile not found for this user.")

        return PaymentSettings.objects.filter(partner=partner)

    def perform_create(self, serializer):
        user = self.request.user
        if user.role != 'partner':
            raise ValidationError("Only partners can create payment settings.")

        try:
            partner = user.partner
        except AttributeError:
            raise ValidationError("Partner profile not found for this user.")

        serializer.save(partner=partner)

# # Financial report viewset
class FinancialReportView(APIView):
    permission_classes = [IsAuthenticated]
    pagination_class = ReportPagination

    def get(self, request):
        if not hasattr(request.user, 'partner') and request.user.role != 'partner':
            return Response({"error": "Only partners can access this report."}, status=status.HTTP_403_FORBIDDEN)

        # Get query parameters
        start_date_str = request.query_params.get('start_date')
        end_date_str = request.query_params.get('end_date')
        days_str = request.query_params.get('days')
        vehicle_reg = request.query_params.get('vehicle')
        record_type_filter = request.query_params.get('type')  # 'revenue' or 'expenditure'
        item_name = request.query_params.get('item_name')

        # Determine date range
        actual_start_date = None
        actual_end_date = None

        try:
            if days_str:
                days = int(days_str)
                if days <= 0:
                    return Response({"error": "Days must be a positive integer."}, status=status.HTTP_400_BAD_REQUEST)

                actual_end_date = datetime.strptime(
                    end_date_str, '%Y-%m-%d').date() if end_date_str else timezone.now().date()
                actual_start_date = actual_end_date - timedelta(days=days - 1)
            elif start_date_str and end_date_str:
                actual_start_date = datetime.strptime(
                    start_date_str, '%Y-%m-%d').date()
                actual_end_date = datetime.strptime(
                    end_date_str, '%Y-%m-%d').date()
            else:  # Default to last 30 days
                actual_end_date = timezone.now().date()
                actual_start_date = actual_end_date - timedelta(days=29)  # 30 days inclusive

            if actual_start_date and actual_end_date and actual_start_date > actual_end_date:
                return Response({"error": "Start date cannot be after end date."}, status=status.HTTP_400_BAD_REQUEST)

        except ValueError:
            return Response({"error": "Invalid date format. Please use YYYY-MM-DD or a valid integer for days."}, status=status.HTTP_400_BAD_REQUEST)

        # Get the partner
        partner = request.user.partner

        # Get revenues and expenditures for a partner with consistent filtering
        revenues_q = Revenue.objects.filter(
            vehicle__partner=partner,
            deleted=False
        )
        expenditures_q = Expenditure.objects.filter(
            vehicle__partner=partner,
            deleted=False
        )

        # Apply date filters
        if actual_start_date:
            revenues_q = revenues_q.filter(date__gte=actual_start_date)
            expenditures_q = expenditures_q.filter(date__gte=actual_start_date)
        if actual_end_date:
            revenues_q = revenues_q.filter(date__lte=actual_end_date)
            expenditures_q = expenditures_q.filter(date__lte=actual_end_date)

        # Apply vehicle filter
        if vehicle_reg:
            vehicle_reg = vehicle_reg.strip()
            revenues_q = revenues_q.filter(vehicle__registration_number=vehicle_reg)
            expenditures_q = expenditures_q.filter(vehicle__registration_number=vehicle_reg)

        # Apply item_name filter (only to expenditures)
        if item_name:
            expenditures_q = expenditures_q.filter(item_name__icontains=item_name)

        # Prepare lists for detailed report and querysets for totals based on type filter
        detailed_report_items = []
        revenues_for_totals = revenues_q
        expenditures_for_totals = expenditures_q

        if record_type_filter:
            record_type_filter = record_type_filter.lower()
            if record_type_filter == 'revenue':
                expenditures_for_totals = expenditures_q.none()
                for rev in revenues_q.order_by('date'):
                    detailed_report_items.append({
                        'id': rev.id,  # Added ID for frontend use
                        'type': 'Revenue',
                        'date': rev.date,
                        'vehicle': rev.vehicle.registration_number if rev.vehicle else None,
                        'amount': rev.amount,
                        'confirmation_message': rev.confirmation_message or '',
                        'description': getattr(rev, 'confirmation_message', 'N/A'),  # Kept for backward compatibility
                        'deleted': rev.deleted,
                    })
            elif record_type_filter == 'expenditure':
                revenues_for_totals = revenues_q.none()
                for exp in expenditures_q.order_by('date'):
                    detailed_report_items.append({
                        'id': exp.id,  # Added ID for frontend use
                        'type': 'Expenditure',
                        'date': exp.date,
                        'vehicle': exp.vehicle.registration_number if exp.vehicle else None,
                        'amount': exp.amount,
                        'description': exp.description or '',
                        'item_name': exp.item_name,
                        'deleted': exp.deleted
                    })
            else:
                return Response({"error": "Invalid value for 'type' filter. Must be 'revenue' or 'expenditure'."}, status=status.HTTP_400_BAD_REQUEST)
        else:
            all_items = []
            for rev in revenues_q:
                all_items.append({
                    'id': rev.id,  # Added ID for frontend use
                    'type': 'Revenue',
                    'date': rev.date,
                    'vehicle': rev.vehicle.registration_number if rev.vehicle else None,
                    'amount': rev.amount,
                    'confirmation_message': rev.confirmation_message or '',
                    'description': getattr(rev, 'confirmation_message', 'N/A'),  # Kept for backward compatibility
                    'deleted': rev.deleted,  # Added
                })
            for exp in expenditures_q:
                all_items.append({
                    'id': exp.id,  # Added ID for frontend use
                    'type': 'Expenditure',
                    'date': exp.date,
                    'vehicle': exp.vehicle.registration_number if exp.vehicle else None,
                    'amount': exp.amount,
                    'description': exp.description or '',
                    'item_name': exp.item_name,
                    'deleted': exp.deleted
                })
            detailed_report_items = sorted(all_items, key=lambda x: x['date'])

        # Calculate daily totals
        daily_totals = {}
        if actual_start_date and actual_end_date:
            current_day = actual_start_date
            while current_day <= actual_end_date:
                day_str = current_day.strftime('%Y-%m-%d')

                daily_revenue_sum = revenues_for_totals.filter(date=current_day).aggregate(
                    total=Sum('amount'))['total'] or Decimal('0.00')
                daily_expenditure_sum = expenditures_for_totals.filter(
                    date=current_day).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')

                daily_totals[day_str] = {
                    'revenue': daily_revenue_sum,
                    'expenditure': daily_expenditure_sum,
                    'profit': daily_revenue_sum - daily_expenditure_sum
                }
                current_day += timedelta(days=1)

        # Calculate overall totals
        total_revenue = revenues_for_totals.aggregate(total=Sum('amount'))['total'] or Decimal('0.00')
        total_expenditure = expenditures_for_totals.aggregate(total=Sum('amount'))['total'] or Decimal('0.00')
        total_profit = total_revenue - total_expenditure

        # Apply pagination to detailed_report
        paginator = self.pagination_class()

        # Get page_size from request, use pagination class default if not provided
        page_size = request.query_params.get('page_size')
        if page_size:
            try:
                page_size = int(page_size)
                if page_size > paginator.max_page_size:
                    page_size = paginator.max_page_size
            except (ValueError, TypeError):
                page_size = paginator.page_size
        else:
            page_size = paginator.page_size

        # Convert detailed_report_items to a list if it's not already
        if not isinstance(detailed_report_items, list):
            detailed_report_items = list(detailed_report_items)

        # Create a mock queryset-like object for pagination
        django_paginator = Paginator(detailed_report_items, page_size)

        # Get the page number from request
        page_number = request.query_params.get('page', 1)
        try:
            page_number = int(page_number)
        except (ValueError, TypeError):
            page_number = 1

        # Get the page
        try:
            page = django_paginator.page(page_number)
        except:
            page = django_paginator.page(1)

        # Serialize the paginated data
        serialized_detailed_report = FinancialReportSerializer(page.object_list, many=True).data

        # Create paginated response
        response_data = {
            'count': django_paginator.count,
            'next': f"?page={page.next_page_number()}" if page.has_next() else None,
            'previous': f"?page={page.previous_page_number()}" if page.has_previous() else None,
            'total_pages': django_paginator.num_pages,
            'current_page': page.number,
            'page_size': page_size,  # Use actual page_size instead of paginator.page_size
            'detailed_report': serialized_detailed_report,
            'daily_totals': daily_totals,
            'total_revenue': total_revenue,
            'total_expenditure': total_expenditure,
            'total_profit': total_profit
        }

        return Response(response_data, status=status.HTTP_200_OK)


# profit report view
class ProfitReportView(APIView):
    permission_classes = [IsAuthenticated]
    pagination_class = ReportPagination

    def get(self, request):
        user = request.user
        if user.role != 'partner':
            raise ValidationError("Only partners can access profit reports.")

        try:
            partner = user.partner
        except AttributeError:
            raise ValidationError("Partner profile not found for this user.")

        # Get query parameters
        vehicle_id = request.query_params.get('vehicle_id', None)
        start_date = request.query_params.get('start_date', None)
        end_date = request.query_params.get('end_date', None)

        # Validate dates
        if start_date:
            try:
                start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            except ValueError:
                raise ValidationError(
                    "Invalid start_date format. Use YYYY-MM-DD.")
        if end_date:
            try:
                end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
            except ValueError:
                raise ValidationError(
                    "Invalid end_date format. Use YYYY-MM-DD.")

        # Get vehicles for the partner
        vehicles = Vehicle.objects.filter(partner=partner)
        if vehicle_id:
            vehicles = vehicles.filter(id=vehicle_id)
            if not vehicles.exists():
                raise ValidationError(
                    "Vehicle not found or not associated with this partner.")

        # Calculate profit per vehicle
        report_data = []
        for vehicle in vehicles:
            # Get revenue for the vehicle
            revenue_query = Revenue.objects.filter(vehicle=vehicle)
            if start_date:
                revenue_query = revenue_query.filter(date__gte=start_date)
            if end_date:
                revenue_query = revenue_query.filter(date__lte=end_date)
            total_revenue = sum(r.amount for r in revenue_query) or 0

            # Get expenditure for the vehicle
            expenditure_query = Expenditure.objects.filter(vehicle=vehicle)
            if start_date:
                expenditure_query = expenditure_query.filter(
                    date__gte=start_date)
            if end_date:
                expenditure_query = expenditure_query.filter(
                    date__lte=end_date)
            total_expenditure = sum(e.amount for e in expenditure_query) or 0

            # Calculate profit
            profit = total_revenue - total_expenditure
            report_data.append({
                'vehicle': vehicle,
                'profit': profit,
            })

        # Apply pagination to report_data
        paginator = self.pagination_class()

        # Create a mock queryset-like object for pagination
        django_paginator = Paginator(report_data, paginator.page_size)

        # Get the page number from request
        page_number = request.query_params.get('page', 1)
        try:
            page_number = int(page_number)
        except (ValueError, TypeError):
            page_number = 1

        # Get the page
        try:
            page = django_paginator.page(page_number)
        except:
            page = django_paginator.page(1)

        # Serialize the paginated data
        serializer = ProfitReportSerializer(page.object_list, many=True)

        # Create paginated response
        response_data = {
            'count': django_paginator.count,
            'next': f"?page={page.next_page_number()}" if page.has_next() else None,
            'previous': f"?page={page.previous_page_number()}" if page.has_previous() else None,
            'total_pages': django_paginator.num_pages,
            'current_page': page.number,
            'page_size': paginator.page_size,
            'results': serializer.data
        }

        return Response(response_data)

# Daily Payment View
class DailyPaymentReportView(APIView):
    permission_classes = [IsAuthenticated]
    pagination_class = ReportPagination

    def get(self, request):
        user = request.user
        if user.role != 'partner':
            raise ValidationError("Only partners can access payment reports.")

        try:
            partner = user.partner
        except AttributeError:
            raise ValidationError("Partner profile not found for this user.")

        # Get the date parameter (default to today)
        date_str = request.query_params.get('date', None)
        if date_str:
            try:
                report_date = datetime.strptime(date_str, '%Y-%m-%d').date()
            except ValueError:
                raise ValidationError("Invalid date format. Use YYYY-MM-DD.")
        else:
            report_date = datetime.now().date()

        # Get all vehicles for the partner
        vehicles = Vehicle.objects.filter(partner=partner)

        # Prepare report data
        report_data = []
        for vehicle in vehicles:
            # Get the driver (if any)
            driver = vehicle.driver

            # Check for payment on the specified date
            payment = Revenue.objects.filter(
                vehicle=vehicle,
                date=report_date
            ).first()

            # Determine status and details
            if payment:
                status = "Paid"
                amount_paid = payment.amount
                confirmation_message = payment.confirmation_message
            else:
                status = "Unpaid"
                amount_paid = None
                confirmation_message = None

            report_data.append({
                'vehicle': vehicle,
                'driver': driver,
                'amount_paid': amount_paid,
                'confirmation_message': confirmation_message,
                'status': status,
            })

        # Apply pagination to report_data
        paginator = self.pagination_class()

        # Create a mock queryset-like object for pagination
        django_paginator = Paginator(report_data, paginator.page_size)

        # Get the page number from request
        page_number = request.query_params.get('page', 1)
        try:
            page_number = int(page_number)
        except (ValueError, TypeError):
            page_number = 1

        # Get the page
        try:
            page = django_paginator.page(page_number)
        except:
            page = django_paginator.page(1)

        # Serialize the paginated data
        serializer = DailyPaymentReportSerializer(page.object_list, many=True)

        # Create paginated response
        response_data = {
            'count': django_paginator.count,
            'next': f"?page={page.next_page_number()}" if page.has_next() else None,
            'previous': f"?page={page.previous_page_number()}" if page.has_previous() else None,
            'total_pages': django_paginator.num_pages,
            'current_page': page.number,
            'page_size': paginator.page_size,
            'results': serializer.data
        }

        return Response(response_data)

 # Enquiry View
class EnquiryView(APIView):
    permission_classes = [AllowAny]

    def post(self, request):
        serializer = EnquirySerializer(data=request.data)
        if serializer.is_valid():
            enquiry = Enquiry.objects.create(**serializer.validated_data)
            subject = f'New Enquiry Received from {enquiry.first_name} {enquiry.last_name}'
            body = (
                f'You have received a new enquiry via your website.\n\n'
                f'Details:\n'
                f'First Name: {enquiry.first_name}\n'
                f'Last Name: {enquiry.last_name}\n'
                f'Phone Number: {enquiry.phone_number}\n'
                f'Email: {enquiry.email}\n\n'
                f'Message:\n{enquiry.message}\n\n'
                f'Please follow up with the user as soon as possible.'
            )
            recipient_list = [settings.DEFAULT_FROM_EMAIL]

            # Create notification record before sending email
            notification = Notification.objects.create(
                subject=subject,
                type="email",
                recipient=enquiry.email,
                status="pending",
                message=body
            )

            try:
                send_mail(subject, body, settings.DEFAULT_FROM_EMAIL,
                          recipient_list)
                notification.mark_as_sent()
                return Response({'success': True, 'message': 'Your inquiry has been sent. We will get back to you shortly.', 'enquiry_id': enquiry.id}, status=status.HTTP_200_OK)
            except Exception as e:
                notification.mark_as_failed()
                logger.error(
                    f"Failed to send enquiry email for enquiry {enquiry.id}: {str(e)}")
                return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


# Class accesstoken
class AccessTokenView(APIView):
    def get(self, request):
        auth_string = f"{settings.MPESA_CONSUMER_KEY}:{settings.MPESA_CONSUMER_SECRET}"
        encoded_auth = base64.b64encode(auth_string.encode()).decode()
        headers = {
            'Authorization': f'Basic {encoded_auth}',
            'Content-Type': 'application/json'
        }
        try:
            response = requests.get(
                f"{settings.MPESA_BASE_URL}/oauth/v1/generate?grant_type=client_credentials",
                headers=headers
            )
            if response.status_code == 200:
                return Response(response.json(), status=status.HTTP_200_OK)
            return Response({"error": "Failed to generate access token"}, status=response.status_code)
        except requests.RequestException as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# password
def generate_password():
    """Generate the base64 encoded password."""
    timestamp = datetime.now(pytz.timezone(
        'Africa/Nairobi')).strftime('%Y%m%d%H%M%S')
    data_to_encode = settings.MPESA_SHORTCODE + settings.MPESA_PASSKEY + timestamp
    password = base64.b64encode(data_to_encode.encode()).decode('utf-8')
    return password, timestamp


# stk push viewset
class STKPushView(APIView):
    def get_access_token(self):
        auth_string = f"{settings.MPESA_CONSUMER_KEY}:{settings.MPESA_CONSUMER_SECRET}"
        encoded_auth = base64.b64encode(auth_string.encode()).decode()
        headers = {'Authorization': f'Basic {encoded_auth}'}
        try:
            response = requests.get(
                f"{settings.MPESA_BASE_URL}/oauth/v1/generate?grant_type=client_credentials",
                headers=headers
            )
            logger.debug(
                f"Access token response: {response.status_code}, {response.text}")
            return response.json().get('access_token') if response.status_code == 200 else None
        except requests.RequestException as e:
            logger.error(f"Access token request failed: {str(e)}")
            return None

    def post(self, request):
        serializer = PaymentTransactionSerializer(data=request.data)
        if serializer.is_valid():
            password, timestamp = generate_password()
            access_token = self.get_access_token()
            if not access_token:
                logger.error("Failed to get access token")
                return Response({"error": "Failed to get access token"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            payload = {
                "BusinessShortCode": settings.MPESA_SHORTCODE,
                "Password": password,
                "Timestamp": timestamp,
                "TransactionType": "CustomerPayBillOnline",
                "Amount": str(serializer.validated_data['amount']),
                "PartyA": serializer.validated_data['phone_number'],
                "PartyB": settings.MPESA_SHORTCODE,
                "PhoneNumber": serializer.validated_data['phone_number'],
                "CallBackURL": settings.CALLBACK_URL,
                "AccountReference": serializer.validated_data['transaction_type'][:12],
                "TransactionDesc": serializer.validated_data['transaction_type'][:13]
            }
            headers = {
                'Authorization': f'Bearer {access_token}',
                'Content-Type': 'application/json'
            }

            logger.debug(f"STK Push payload: {payload}")
            try:
                response = requests.post(
                    f"{settings.MPESA_BASE_URL}/mpesa/stkpush/v1/processrequest",
                    json=payload,
                    headers=headers
                )
                logger.debug(
                    f"STK Push response: {response.status_code}, {response.text}")
                if response.status_code == 200:
                    transaction = serializer.save(
                        transaction_id=response.json().get('CheckoutRequestID'),
                        status='pending'
                    )
                    return Response(response.json(), status=status.HTTP_200_OK)
                return Response(
                    {"error": "STK Push request failed",
                        "details": response.json()},
                    status=status.HTTP_400_BAD_REQUEST
                )
            except requests.RequestException as e:
                logger.error(f"STK Push request failed: {str(e)}")
                return Response({"error": f"STK Push request failed: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        logger.error(f"Invalid STK Push data: {serializer.errors}")
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


#  #payment call view
class PaymentCallbackView(APIView):
    def post(self, request):
        logger.debug("MPESA CALLBACK RECEIVED: %s",
                     json.dumps(request.data, indent=4))
        print("MPESA CALLBACK RECEIVED:", json.dumps(request.data, indent=4))

        data = request.data.get('Body', {}).get('stkCallback', {})
        checkout_request_id = data.get('CheckoutRequestID')
        result_code = data.get('ResultCode')
        result_desc = data.get('ResultDesc')

        if not checkout_request_id:
            logger.error("No CheckoutRequestID in callback data: %s",
                         json.dumps(request.data))
            print("ERROR: No CheckoutRequestID in callback data")
            return Response({"error": "Invalid callback data"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            transaction = PaymentTransaction.objects.get(
                transaction_id=checkout_request_id)
            logger.debug(
                f"Found transaction: {transaction.id}, current status: {transaction.status}")
            print(
                f"Found transaction: {transaction.id}, status: {transaction.status}")

        except PaymentTransaction.DoesNotExist:
            logger.warning(
                f"Transaction {checkout_request_id} not found. Creating placeholder.")
            transaction = PaymentTransaction.objects.create(
                transaction_id=checkout_request_id,
                amount=0,
                phone_number='',
                status='failed',
                transaction_type='subscription',
                reference_id=0
            )
            logger.info(
                f"Created placeholder transaction {checkout_request_id}")

        if result_code == 0:
            metadata = data.get("CallbackMetadata", {}).get("Item", [])
            metadata_dict = {
                "mpesa_receipt_number": None,
                "transaction_date": None
            }
            for item in metadata:
                if item["Name"] == "MpesaReceiptNumber":
                    metadata_dict["mpesa_receipt_number"] = item["Value"]
                elif item["Name"] == "TransactionDate":
                    try:
                        metadata_dict["transaction_date"] = datetime.strptime(
                            str(item["Value"]), "%Y%m%d%H%M%S")
                    except ValueError as e:
                        logger.warning(f"Failed to parse TransactionDate: {e}")

            updated = False
            if not transaction.mpesa_receipt_number and metadata_dict["mpesa_receipt_number"]:
                transaction.mpesa_receipt_number = metadata_dict["mpesa_receipt_number"]
                updated = True
            if not transaction.transaction_date and metadata_dict["transaction_date"]:
                transaction.transaction_date = metadata_dict["transaction_date"]
                updated = True

            if transaction.status != 'success':
                transaction.status = 'success'
                updated = True

            if transaction.transaction_type == 'subscription' and transaction.reference_id:
                try:
                    subscription = Subscription.objects.get(
                        id=transaction.reference_id)
                    if subscription.status != 'active':
                        subscription.status = 'active'
                        subscription.save()
                        logger.info(
                            f"Subscription {subscription.id} activated")
                        print(f"Subscription {subscription.id} activated")

                        # Send subscription success email
                    try:
                        send_subscription_success_email(
                            subscription.partner, subscription, transaction)
                        logger.info(
                            f"Subscription success email sent for subscription {subscription.id}")
                    except Exception as e:
                        logger.error(
                            f"Failed to send subscription success email for subscription {subscription.id}: {str(e)}")

                except Subscription.DoesNotExist:
                    logger.warning(
                        f"Subscription {transaction.reference_id} not found")

            if updated:
                transaction.save()
                logger.info(
                    f"Transaction {checkout_request_id} updated: receipt={metadata_dict['mpesa_receipt_number']}, date={metadata_dict['transaction_date']}")
                print(
                    f"Updated transaction {checkout_request_id}: {metadata_dict}")
            else:
                logger.info(
                    f"Transaction {checkout_request_id} already up-to-date")
                print(f"Transaction {checkout_request_id} already up-to-date")

            return Response({"status": "ok"}, status=status.HTTP_200_OK)
        else:
            if transaction.status != 'failed':
                transaction.status = 'failed'
                transaction.save()
                logger.warning(
                    f"Transaction {checkout_request_id} failed: {result_desc}")
                print(
                    f"Transaction {checkout_request_id} failed: {result_desc}")
            user_message = (
                "Insufficient funds. Please top up your M-Pesa account and try again."
                if "insufficient" in result_desc.lower()
                else result_desc
            )
            return Response({"error": user_message}, status=status.HTTP_400_BAD_REQUEST)


# #Subscription activation
class SubscriptionActivateView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        if not request.data:
            return Response({"error": "Request body cannot be empty"}, status=status.HTTP_400_BAD_REQUEST)

        if request.user.role != 'partner':
            logger.error(
                f"User {request.user.email} is not a partner (role: {request.user.role})")
            return Response({"error": "Only partners can activate subscriptions"}, status=status.HTTP_403_FORBIDDEN)

        try:
            partner = request.user.partner
        except AttributeError:
            logger.error(
                f"User {request.user.email} has no linked Partner instance")
            return Response({"error": "No Partner profile found for this user"}, status=status.HTTP_400_BAD_REQUEST)

        serializer = SubscriptionSerializer(data=request.data)
        if not serializer.is_valid():
            logger.error(
                f"Subscription serializer errors: {serializer.errors}")
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        try:
            subscription = serializer.save(partner=partner, status='pending')
            logger.debug(
                f"Subscription created: {subscription.id}, plan: {subscription.plan_type}")

            if subscription.plan_type == 'Free':
                subscription.status = 'active'
                subscription.save()
    
                logger.info(f"Free subscription {subscription.id} activated, sending success email")
    
                # Send subscription success email for free plan
                send_subscription_success_email(subscription.partner, subscription) 
                # Return immediately for free plans to prevent STK push and potential duplicate email
                return Response({
                    "message": "Free subscription activated successfully", 
                    "subscription_id": subscription.id
                }, status=status.HTTP_200_OK)


            # Check for existing pending transactions
            if PaymentTransaction.objects.filter(
                reference_id=subscription.id,
                transaction_type='subscription',
                status='pending'
            ).exists():
                logger.warning(
                    f"Pending transaction exists for subscription {subscription.id}")
                return Response(
                    {"error": "A pending payment already exists for this subscription"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Prepare STK Push payload
            stk_payload = {
                'user': request.user.id,
                'transaction_type': 'subscription',
                'amount': int(float(subscription.amount)),
                'phone_number': serializer.validated_data.get('phone_number', request.data.get('phone_number')),
                'reference_id': subscription.id
            }
            logger.debug(f"STK payload: {stk_payload}")

            payment_serializer = PaymentTransactionSerializer(data=stk_payload)
            if not payment_serializer.is_valid():
                logger.error(
                    f"Payment serializer errors: {payment_serializer.errors}")
                return Response(payment_serializer.errors, status=status.HTTP_400_BAD_REQUEST)

            logger.debug(
                f"Payment serializer validated data: {payment_serializer.validated_data}")

            # Call STKPushView internally
            stk_view = STKPushView()
            internal_request = HttpRequest()
            internal_request.method = 'POST'
            internal_request.user = request.user
            internal_request._body = json.dumps(stk_payload).encode('utf-8')
            internal_request.META['CONTENT_TYPE'] = 'application/json'

            drf_request = Request(
                internal_request,
                parsers=[JSONParser()]
            )
            drf_request._full_data = stk_payload

            stk_response = stk_view.post(drf_request)

            logger.debug(
                f"STK Push response: {stk_response.status_code}, {stk_response.data}")

            if stk_response.status_code == 200:
                payment_response = stk_response.data
                logger.debug(f"Payment response: {payment_response}")
                checkout_request_id = payment_response.get('CheckoutRequestID')

                if not checkout_request_id:
                    logger.error(
                        f"No CheckoutRequestID in response: {payment_response}")
                    return Response({"error": "Invalid STK Push response"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

                if PaymentTransaction.objects.filter(transaction_id=checkout_request_id).exists():
                    transaction = PaymentTransaction.objects.get(
                        transaction_id=checkout_request_id)
                    if transaction.status == 'pending':
                        logger.warning(
                            f"Transaction {checkout_request_id} is still pending")
                    else:
                        logger.warning(
                            f"Transaction {checkout_request_id} already processed: {transaction.status}")
                        return Response(
                            {"error": f"Transaction already processed with status {transaction.status}"},
                            status=status.HTTP_400_BAD_REQUEST)
                else:
                    payment_serializer.save(
                        transaction_id=checkout_request_id,
                        status='pending'
                    )

                # Poll STKPushQueryView internally
                max_attempts = 4  # Reduced to avoid timeout
                delay = 15  # Reduced delay
                for attempt in range(max_attempts):
                    try:
                        # Prepare query payload
                        query_payload = {
                            'CheckoutRequestID': checkout_request_id}
                        logger.debug(
                            f"Query attempt {attempt + 1} payload: {query_payload}")

                        # Call STKPushQueryView internally
                        query_view = STKPushQueryView()
                        query_internal_request = HttpRequest()
                        query_internal_request.method = 'POST'
                        query_internal_request.user = request.user
                        query_internal_request._body = json.dumps(
                            query_payload).encode('utf-8')
                        query_internal_request.META['CONTENT_TYPE'] = 'application/json'

                        drf_query_request = Request(
                            query_internal_request,
                            parsers=[JSONParser()]
                        )
                        drf_query_request._full_data = query_payload

                        query_response = query_view.post(drf_query_request)

                        logger.debug(
                            f"Query attempt {attempt + 1}: {query_response.status_code}, {query_response.data}")
                        print(
                            f"Query attempt {attempt + 1}: {query_response.data}")

                        if query_response.status_code == 200:
                            query_data = query_response.data
                            if query_data.get('status') == 'ok':
                                transaction = PaymentTransaction.objects.get(
                                    transaction_id=checkout_request_id)
                                if transaction.status == 'success':
                                    logger.info(
                                        f"Transaction successful, sending subscription success email for subscription {subscription.id}")
                                    
                                     # Send subscription success email
                                    # email_sent = send_subscription_success_email(
                                    #      subscription.partner, subscription, transaction)
                                    
                                    # if email_sent:
                                    #     logger.info(
                                    #         f"Subscription success email sent successfully for subscription {subscription.id}")
                                    # else:
                                    #     logger.error(
                                    #         f"Failed to send subscription success email for subscription {subscription.id}")
                                    return Response({
                                        "message": "Subscription activated successfully",
                                        "subscription_id": subscription.id,
                                        "transaction_status": transaction.status,
                                        "mpesa_receipt_number": transaction.mpesa_receipt_number,
                                        "transaction_date": transaction.transaction_date,
                                        # "email_sent": email_sent
                                    }, status=status.HTTP_200_OK)
                            elif query_data.get('error') == 'Transaction failed':
                                transaction = PaymentTransaction.objects.get(
                                    transaction_id=checkout_request_id)
                                if transaction.status != 'failed':
                                    transaction.status = 'failed'
                                    transaction.save()
                                    logger.info(
                                        f"Transaction {checkout_request_id} marked as failed")
                                error_message = query_data.get('details', {}).get(
                                    'ResultDesc', 'Transaction failed')
                                user_message = (
                                    "Insufficient funds. Please top up your M-Pesa account and try again."
                                    if "insufficient" in error_message.lower()
                                    else error_message
                                )
                                return Response({
                                    "error": user_message,
                                    "subscription_id": subscription.id,
                                    "transaction_status": transaction.status
                                }, status=status.HTTP_400_BAD_REQUEST)
                        elif query_response.status_code == 500 and 'The transaction is being processed' in query_response.data.get('error', ''):
                            logger.debug(f"Transaction {checkout_request_id} still processing")
                        else:
                            logger.error(f"Query failed: {query_response.status_code}, {query_response.data}")
                            print(f"ERROR: Query failed: {query_response.status_code}, {query_response.data}")
                    except Exception as e:
                        logger.error(f"Query attempt {attempt + 1} failed: {str(e)}")
                        print(f"ERROR: Query attempt {attempt + 1} failed: {str(e)}")
                    time.sleep(delay)

                transaction = PaymentTransaction.objects.get(transaction_id=checkout_request_id)
                if transaction.status == 'failed':
                    return Response({
                        "error": "Transaction failed. Please try again or contact support.",
                        "subscription_id": subscription.id,
                        "transaction_status": transaction.status
                    }, status=status.HTTP_400_BAD_REQUEST)
                logger.warning(f"Max query attempts reached for {checkout_request_id}")
                return Response({
                    "message": "Transaction is still pending, awaiting callback",
                    "checkout_request_id": checkout_request_id,
                    "subscription_id": subscription.id
                }, status=status.HTTP_202_ACCEPTED)
            else:
                logger.error(f"STK Push failed: {stk_response.data}")
                return Response({"error": "STK Push failed", "details": stk_response.data}, status=stk_response.status_code)

        except Exception as e:
            logger.error(f"Unexpected error activating subscription for user {request.user.email}: {str(e)}")
            return Response({"error": f"Unexpected error: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
         
     
# #Stk push Query      
class STKPushQueryView(APIView):
    def post(self, request):
        checkout_request_id = request.data.get('CheckoutRequestID')
        if not checkout_request_id:
            return Response({"error": "CheckoutRequestID required"}, status=status.HTTP_400_BAD_REQUEST)

        auth_string = f"{settings.MPESA_CONSUMER_KEY}:{settings.MPESA_CONSUMER_SECRET}"
        encoded_auth = base64.b64encode(auth_string.encode()).decode()
        headers = {
            'Authorization': f'Basic {encoded_auth}',
            'Content-Type': 'application/json'
        }
        try:
            response = requests.get(
                f"{settings.MPESA_BASE_URL}/oauth/v1/generate?grant_type=client_credentials",
                headers=headers
            )
            if response.status_code != 200:
                return Response({"error": "Failed to get access token"}, status=response.status_code)
            access_token = response.json().get('access_token')

            password, timestamp = generate_password()
            query_payload = {
                "BusinessShortCode": settings.MPESA_SHORTCODE,
                "Password": password,
                "Timestamp": timestamp,
                "CheckoutRequestID": checkout_request_id
            }
            headers = {
                'Authorization': f'Bearer {access_token}',
                'Content-Type': 'application/json'
            }
            response = requests.post(
                f"{settings.MPESA_BASE_URL}/mpesa/stkpushquery/v1/query",
                json=query_payload,
                headers=headers
            )
            logger.debug(f"STK Push Query response: {response.status_code}, {response.text}")
            if response.status_code == 200:
                data = response.json()
                result_code = data.get('ResultCode')
                if result_code == '0':
                    try:
                        transaction = PaymentTransaction.objects.get(transaction_id=checkout_request_id)
                        updated = False
                        if transaction.status != 'success':
                            transaction.status = 'success'
                            updated = True

                        # Extract metadata if available
                        metadata = data.get('CallbackMetadata', {}).get('Item', [])
                        for item in metadata:
                            if item.get('Name') == 'MpesaReceiptNumber' and not transaction.mpesa_receipt_number:
                                transaction.mpesa_receipt_number = item.get('Value')
                                updated = True
                            elif item.get('Name') == 'TransactionDate' and not transaction.transaction_date:
                                try:
                                    transaction.transaction_date = datetime.strptime(str(item.get('Value')), '%Y%m%d%H%M%S')
                                    updated = True
                                except ValueError as e:
                                    logger.warning(f"Failed to parse TransactionDate: {e}")

                        if transaction.transaction_type == 'subscription':
                            subscription = Subscription.objects.get(id=transaction.reference_id)
                            if subscription.status != 'active':
                                subscription.status = 'active'
                                subscription.save()
                                logger.info(f"Subscription {subscription.id} activated")

                        if updated:
                            transaction.save()
                            logger.info(f"Transaction {checkout_request_id} updated to success with metadata")
                        else:
                            logger.info(f"Transaction {checkout_request_id} already up-to-date")

                        return Response({"status": "ok", "details": data}, status=status.HTTP_200_OK)
                    except PaymentTransaction.DoesNotExist:
                        logger.error(f"Transaction {checkout_request_id} not found")
                        return Response({"error": "Transaction not found"}, status=status.HTTP_400_BAD_REQUEST)
                    except Subscription.DoesNotExist:
                        logger.error(f"Subscription {transaction.reference_id} not found")
                        return Response({"error": "Subscription not found"}, status=status.HTTP_400_BAD_REQUEST)
                else:
                    try:
                        transaction = PaymentTransaction.objects.get(transaction_id=checkout_request_id)
                        if transaction.status != 'failed':
                            transaction.status = 'failed'
                            transaction.save()
                            logger.info(f"Transaction {checkout_request_id} marked as failed: {data.get('ResultDesc')}")
                    except PaymentTransaction.DoesNotExist:
                        logger.error(f"Transaction {checkout_request_id} not found")
                        return Response({"error": "Transaction not found"}, status=status.HTTP_400_BAD_REQUEST)
                    logger.warning(f"Transaction {checkout_request_id} failed: {data.get('ResultDesc')}")
                    return Response({"error": "Transaction failed", "details": data}, status=status.HTTP_400_BAD_REQUEST)
            logger.error(f"STK Push Query failed: {response.status_code}, {response.text}")
            return Response({"error": "STK Push Query failed", "details": response.json()}, status=status.HTTP_400_BAD_REQUEST)
        except requests.RequestException as e:
            logger.error(f"STK Push Query failed: {str(e)}")
            return Response({"error": f"STK Push Query failed: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


#listing and cancel suscribtion viewset
class SubscriptionViewSet(ViewSet):
    permission_classes = [IsAuthenticated]

    def list(self, request):
        try:
            # Get the partner linked to the authenticated user
            partner = request.user.partner
            # Get the active subscription (or None if none exists)
            subscription = Subscription.objects.filter(
                partner=partner,
                status='active'
            ).order_by('-created_at').first()
            
            if subscription:
                serializer = SubscriptionSerializer(subscription)
                return Response(serializer.data, status=status.HTTP_200_OK)
            else:
                logger.info(f"No active subscription found for user {request.user.email}")
                return Response({"message": "No active subscription found"}, status=status.HTTP_404_NOT_FOUND)
        except AttributeError:
            logger.error(f"User {request.user.email} has no linked Partner instance")
            return Response({"error": "No Partner profile found for this user"}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Error fetching subscription for user {request.user.email}: {str(e)}")
            return Response({"error": f"Unexpected error: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
    @action(detail=False, methods=['post'], url_path='cancel')
    def cancel(self, request):
        try:
            partner = request.user.partner
            subscription = Subscription.objects.filter(
                partner=partner,
                status='active'
            ).first()
            
            if not subscription:
                logger.info(f"No active subscription to cancel for user {request.user.email}")
                return Response({"message": "No active subscription to cancel"}, status=status.HTTP_404_NOT_FOUND)
            
            subscription.status = 'cancelled'
            subscription.save()
            logger.info(f"Subscription {subscription.id} cancelled for user {request.user.email}")
            
            serializer = SubscriptionSerializer(subscription)
            return Response({
                "message": "Subscription cancelled successfully",
                "subscription": serializer.data
            }, status=status.HTTP_200_OK)
        except AttributeError:
            logger.error(f"User {request.user.email} has no linked Partner instance")
            return Response({"error": "No Partner profile found for this user"}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Error cancelling subscription for user {request.user.email}: {str(e)}")
            return Response({"error": f"Unexpected error: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
    @action(detail=False, methods=['get'], url_path='payment-history')
    def payment_history(self, request):
        try:
            # Get the partner linked to the authenticated user
            partner = request.user.partner
            logger.debug(f"Fetching payment history for partner: {partner.id} ({request.user.email})")

            # Fetch all subscriptions for the partner
            subscriptions = Subscription.objects.filter(partner=partner)
            logger.debug(f"Found {subscriptions.count()} subscriptions for partner {partner.id}")

            # Fetch successful payment transactions using reference_id
            transactions = PaymentTransaction.objects.filter(
                reference_id__in=subscriptions.values('id'),
                transaction_type='subscription',
                status='success'
            ).order_by('-created_at')  # Order by created_at for consistency
            logger.debug(f"Found {transactions.count()} successful subscription transactions")

            # Prepare response data
            payment_history = []
            for transaction in transactions:
                try:
                    subscription = Subscription.objects.get(id=transaction.reference_id)
                    payment_history.append({
                        'plan_type': subscription.plan_type,
                        'amount': str(transaction.amount),
                        'payment_method': 'M-Pesa',  # Assume M-Pesa for all transactions
                        'payment_date': transaction.transaction_date or transaction.created_at
                    })
                except Subscription.DoesNotExist:
                    logger.warning(f"Subscription {transaction.reference_id} not found for transaction {transaction.id}")
                    continue
            
            if not payment_history:
                logger.info(f"No successful payment history found for user {request.user.email}")
                return Response({"message": "No successful payment history found"}, status=status.HTTP_404_NOT_FOUND)
                
            logger.info(f"Retrieved {len(payment_history)} successful payment records for user {request.user.email}")
            return Response(payment_history, status=status.HTTP_200_OK)
            
        except AttributeError:
            logger.error(f"User {request.user.email} has no linked Partner instance")
            return Response({"error": "No Partner profile found for this user"}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Error fetching payment history for user {request.user.email}: {str(e)}")
            return Response({"error": f"Unexpected error: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
    @action(detail=False, methods=['get'], url_path='subscription-history')
    def subscription_history(self, request):
        """
        Retrieve all subscription history for the authenticated partner.
        Returns subscriptions of all statuses (active, pending, cancelled, failed).
        """
        try:
            # Get the partner linked to the authenticated user
            partner = request.user.partner
            logger.debug(f"Fetching subscription history for partner: {partner.id} ({request.user.email})")

            # Fetch all subscriptions for the partner
            subscriptions = Subscription.objects.filter(partner=partner).order_by('-created_at')
            logger.debug(f"Found {subscriptions.count()} subscriptions for partner {partner.id}")

            if not subscriptions.exists():
                logger.info(f"No subscription history found for user {request.user.email}")
                return Response({"message": "No subscription history found"}, status=status.HTTP_404_NOT_FOUND)

            # Serialize the subscriptions
            serializer = SubscriptionSerializer(subscriptions, many=True)
            logger.info(f"Retrieved {len(serializer.data)} subscription records for user {request.user.email}")

            return Response(serializer.data, status=status.HTTP_200_OK)

        except AttributeError:
            logger.error(f"User {request.user.email} has no linked Partner instance")
            return Response({"error": "No Partner profile found for this user"}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Error fetching subscription history for user {request.user.email}: {str(e)}")
            return Response({"error": f"Unexpected error: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# OPTIMIZED DASHBOARD ENDPOINTS
class DashboardSummaryView(APIView):
    """
    Optimized endpoint for dashboard summary data.
    Returns all dashboard metrics in a single API call.
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        user = request.user
        if user.role != 'partner':
            return Response({"error": "Only partners can access dashboard data."},
                          status=status.HTTP_403_FORBIDDEN)

        try:
            partner = user.partner
        except AttributeError:
            return Response({"error": "Partner profile not found."},
                          status=status.HTTP_404_NOT_FOUND)

        # Get current month dates
        today = timezone.now().date()
        current_month_start = today.replace(day=1)
        next_month = current_month_start.replace(month=current_month_start.month + 1) if current_month_start.month < 12 else current_month_start.replace(year=current_month_start.year + 1, month=1)
        current_month_end = next_month - timedelta(days=1)

        # Get active vehicles count
        active_vehicles = Vehicle.objects.filter(partner=partner, status='active')
        total_vehicles = active_vehicles.count()

        # Get payment settings for expected revenue calculation
        payment_settings = PaymentSettings.objects.filter(partner=partner).first()

        # Calculate expected revenue so far (from start of month to today)
        expected_revenue_so_far = Decimal('0.00')
        if payment_settings and total_vehicles > 0:
            # Count working days from start of month to today
            working_days_so_far = self._count_working_days(current_month_start, today, payment_settings.payment_days)
            expected_revenue_so_far = payment_settings.daily_amount * working_days_so_far * total_vehicles

        # Calculate expected revenue for full month
        expected_revenue_full_month = Decimal('0.00')
        if payment_settings and total_vehicles > 0:
            working_days_full_month = self._count_working_days(current_month_start, current_month_end, payment_settings.payment_days)
            expected_revenue_full_month = payment_settings.daily_amount * working_days_full_month * total_vehicles

        # Calculate actual revenue so far (from start of month to today)
        actual_revenue_so_far = Revenue.objects.filter(
            vehicle__in=active_vehicles,
            date__range=[current_month_start, today],
            deleted=False
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')

        # Calculate payment completion rate using new formula: actual revenue (so far) / expected revenue (so far) * 100
        payment_completion_rate = 0
        if expected_revenue_so_far > 0:
            payment_completion_rate = round((actual_revenue_so_far / expected_revenue_so_far) * 100, 1)

        return Response({
            'total_vehicles': total_vehicles,
            'expected_revenue_so_far': expected_revenue_so_far,
            'expected_revenue_full_month': expected_revenue_full_month,
            'actual_revenue_so_far': actual_revenue_so_far,
            'payment_completion_rate': payment_completion_rate,
            'completion_rate_display': f"{payment_completion_rate}%",
            'current_month': today.strftime('%B %Y'),
            'calculation_period': f"{current_month_start.strftime('%Y-%m-%d')} to {today.strftime('%Y-%m-%d')}"
        })

    def _count_working_days(self, start_date, end_date, payment_days_str):
        """Count working days in the given date range based on payment_days setting"""
        if not payment_days_str:
            return 0

        # Parse payment days (e.g., "Monday,Wednesday,Friday")
        payment_days = [day.strip() for day in payment_days_str.split(',')]
        day_mapping = {
            'Monday': 0, 'Tuesday': 1, 'Wednesday': 2, 'Thursday': 3,
            'Friday': 4, 'Saturday': 5, 'Sunday': 6
        }

        working_day_numbers = [day_mapping.get(day) for day in payment_days if day in day_mapping]

        if not working_day_numbers:
            return 0

        # Count days
        working_days = 0
        current_date = start_date
        while current_date <= end_date:
            if current_date.weekday() in working_day_numbers:
                working_days += 1
            current_date += timedelta(days=1)

        return working_days

#dashboard monthly trends
class MonthlyProfitTrendsView(APIView):
    """
    Optimized endpoint for monthly profit trends over the past 6 months.
    Returns aggregated data without pagination for dashboard charts.
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        user = request.user
        if user.role != 'partner':
            return Response({"error": "Only partners can access profit trends."},
                          status=status.HTTP_403_FORBIDDEN)

        try:
            partner = user.partner
        except AttributeError:
            return Response({"error": "Partner profile not found."},
                          status=status.HTTP_404_NOT_FOUND)

        # Calculate date range for last 6 months (more precise calculation)
        today = timezone.now().date()
        current_month_start = today.replace(day=1)

        # Calculate 6 months ago more precisely
        six_months_ago = current_month_start
        for _ in range(6):
            if six_months_ago.month == 1:
                six_months_ago = six_months_ago.replace(year=six_months_ago.year - 1, month=12)
            else:
                six_months_ago = six_months_ago.replace(month=six_months_ago.month - 1)

        # Get active vehicles (include all vehicles, not just active ones for historical data)
        vehicles = Vehicle.objects.filter(partner=partner)

        # Get all revenue and expenditure data for the period
        revenues = Revenue.objects.filter(
            vehicle__in=vehicles,
            date__gte=six_months_ago,
            date__lte=today,
            deleted=False
        ).values('date', 'amount', 'vehicle__registration_number')

        expenditures = Expenditure.objects.filter(
            vehicle__in=vehicles,
            date__gte=six_months_ago,
            date__lte=today,
            deleted=False
        ).values('date', 'amount', 'vehicle__registration_number')

        # Organize data by month and vehicle
        monthly_data = {}

        # Process revenues
        for revenue in revenues:
            month_key = revenue['date'].strftime('%Y-%m')
            vehicle = revenue['vehicle__registration_number']

            if month_key not in monthly_data:
                monthly_data[month_key] = {}
            if vehicle not in monthly_data[month_key]:
                monthly_data[month_key][vehicle] = {'revenue': Decimal('0'), 'expenditure': Decimal('0')}

            monthly_data[month_key][vehicle]['revenue'] += revenue['amount']

        # Process expenditures
        for expenditure in expenditures:
            month_key = expenditure['date'].strftime('%Y-%m')
            vehicle = expenditure['vehicle__registration_number']

            if month_key not in monthly_data:
                monthly_data[month_key] = {}
            if vehicle not in monthly_data[month_key]:
                monthly_data[month_key][vehicle] = {'revenue': Decimal('0'), 'expenditure': Decimal('0')}

            monthly_data[month_key][vehicle]['expenditure'] += expenditure['amount']

        # Generate 6-month series with precise month calculation
        series = []
        current_date = current_month_start

        # Generate list of months to include
        months_to_include = []
        for _ in range(6):
            months_to_include.append(current_date)
            if current_date.month == 1:
                current_date = current_date.replace(year=current_date.year - 1, month=12)
            else:
                current_date = current_date.replace(month=current_date.month - 1)

        # Reverse to get chronological order (oldest to newest)
        months_to_include.reverse()

        for target_date in months_to_include:
            month_key = target_date.strftime('%Y-%m')
            month_label = target_date.strftime('%b %Y')

            month_vehicles = monthly_data.get(month_key, {})
            total_profit = sum(
                (vehicle_data['revenue'] - vehicle_data['expenditure'])
                for vehicle_data in month_vehicles.values()
            )

            # Add vehicle breakdown
            vehicle_profits = {
                vehicle: float(data['revenue'] - data['expenditure'])
                for vehicle, data in month_vehicles.items()
            }

            series.append({
                'month': month_label,
                'totalProfit': float(total_profit),
                **vehicle_profits
            })

        return Response({
            'monthly_profit_data': series,
            'period': f"{series[0]['month'] if series else ''} - {series[-1]['month'] if series else ''}"
        })


# Driver Rating ViewSet
class DriverRatingViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for driver ratings with filtering capabilities.
    Supports filtering by date range, driver, vehicle, and driver age groups.
    """
    serializer_class = DriverRatingSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        user = self.request.user

        if user.role == 'partner':
            # Partners can only see ratings for their own drivers/vehicles
            try:
                partner = user.partner
                queryset = DriverRating.objects.filter(partner=partner)
            except AttributeError:
                return DriverRating.objects.none()
        elif user.role == 'driver':
            # Drivers can only see their own ratings
            try:
                driver = user.driver
                queryset = DriverRating.objects.filter(driver=driver)
            except AttributeError:
                return DriverRating.objects.none()
        else:
            # Other roles have no access
            return DriverRating.objects.none()

        # Apply filters
        queryset = self.apply_filters(queryset)

        return queryset.select_related('driver', 'vehicle', 'partner').order_by('-last_updated')

    def apply_filters(self, queryset):
        """Apply query parameter filters to the queryset"""

        # Date range filter
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')

        if start_date:
            try:
                start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
                queryset = queryset.filter(last_updated__date__gte=start_date)
            except ValueError:
                pass

        if end_date:
            try:
                end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
                queryset = queryset.filter(last_updated__date__lte=end_date)
            except ValueError:
                pass

        # Driver filter
        driver_id = self.request.query_params.get('driver_id')
        if driver_id:
            try:
                queryset = queryset.filter(driver_id=int(driver_id))
            except (ValueError, TypeError):
                pass

        # Vehicle filter
        vehicle_id = self.request.query_params.get('vehicle_id')
        if vehicle_id:
            try:
                queryset = queryset.filter(vehicle_id=int(vehicle_id))
            except (ValueError, TypeError):
                pass

        # Driver age group filter
        age_group = self.request.query_params.get('age_group')
        if age_group in ['below_25', '26_39', '40_above']:
            from .rating_utils import get_driver_age_group
            # Filter drivers by age group
            driver_ids = []
            for rating in queryset:
                if get_driver_age_group(rating.driver) == age_group:
                    driver_ids.append(rating.driver.id)
            queryset = queryset.filter(driver_id__in=driver_ids)

        # Active status filter
        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            if is_active.lower() in ['true', '1']:
                queryset = queryset.filter(is_active=True)
            elif is_active.lower() in ['false', '0']:
                queryset = queryset.filter(is_active=False)

        return queryset

    @action(detail=False, methods=['get'])
    def report(self, request):
        """
        Generate a detailed driver ratings report with filtering.
        Returns data suitable for reports and analytics.
        """
        queryset = self.get_queryset()

        # Prepare report data
        report_data = []
        for rating in queryset:
            from .rating_utils import get_driver_age_group

            report_data.append({
                'driver_id': rating.driver.id,
                'driver_name': f"{rating.driver.first_name} {rating.driver.last_name}".strip(),
                'driver_age_group': get_driver_age_group(rating.driver),
                'vehicle_id': rating.vehicle.id,
                'vehicle_registration': rating.vehicle.registration_number,
                'partner_id': rating.partner.id,
                'partner_name': f"{rating.partner.first_name} {rating.partner.last_name}".strip(),
                'rating_score': rating.rating_score,
                'total_points': rating.total_points,
                'total_payment_days': rating.total_payment_days,
                'last_payment_date': rating.last_payment_date,
                'consecutive_non_payment_days': rating.consecutive_non_payment_days,
                'is_active': rating.is_active,
                'last_updated': rating.last_updated,
            })

        # Paginate the report data
        paginator = PageNumberPagination()
        paginator.page_size = int(request.query_params.get('page_size', 20))
        paginated_data = paginator.paginate_queryset(report_data, request)

        serializer = DriverRatingReportSerializer(paginated_data, many=True)
        return paginator.get_paginated_response(serializer.data)

    @action(detail=False, methods=['get'])
    def summary(self, request):
        """
        Get summary statistics for driver ratings.
        """
        queryset = self.get_queryset()

        total_drivers = queryset.count()
        active_drivers = queryset.filter(is_active=True).count()
        inactive_drivers = total_drivers - active_drivers

        if total_drivers > 0:
            avg_rating = queryset.aggregate(
                avg_score=models.Avg('rating_score')
            )['avg_score'] or 0

            # Rating distribution
            excellent_count = queryset.filter(rating_score__gte=4.5).count()
            good_count = queryset.filter(rating_score__gte=3.5, rating_score__lt=4.5).count()
            average_count = queryset.filter(rating_score__gte=2.5, rating_score__lt=3.5).count()
            poor_count = queryset.filter(rating_score__lt=2.5).count()
        else:
            avg_rating = 0
            excellent_count = good_count = average_count = poor_count = 0

        return Response({
            'total_drivers': total_drivers,
            'active_drivers': active_drivers,
            'inactive_drivers': inactive_drivers,
            'average_rating': round(float(avg_rating), 2),
            'rating_distribution': {
                'excellent': excellent_count,  # 4.5+
                'good': good_count,           # 3.5-4.4
                'average': average_count,     # 2.5-3.4
                'poor': poor_count           # <2.5
            }
        })
