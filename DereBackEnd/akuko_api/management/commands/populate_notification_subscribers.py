# akuko_api/management/commands/populate_notification_subscribers.py
from django.core.management.base import BaseCommand
from akuko_api.models import User, NotificationSubscriber


class Command(BaseCommand):
    help = 'Populates the NotificationSubscriber table with existing users who have notification_subscribe=True'

    def handle(self, *args, **options):
        # Get all users with notification_subscribe=True
        subscribed_users = User.objects.filter(
            notification_subscribe=True,
            notificationsubscriber__isnull=True  # Only those who don't already have a record
        )

        count = 0
        for user in subscribed_users:
            NotificationSubscriber.objects.create(user=user)
            count += 1

        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully added {count} users to NotificationSubscriber table')
        )
