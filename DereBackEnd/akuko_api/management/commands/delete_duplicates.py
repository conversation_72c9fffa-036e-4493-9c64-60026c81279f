from django.core.management.base import BaseCommand
from django.apps import apps
from django.db import transaction, models
from django.db.models import ProtectedError

class Command(BaseCommand):
    help = 'Delete duplicate entries in the specified model based on a field and related make.'

    def add_arguments(self, parser):
        parser.add_argument('model_name', type=str, help='Model name to check for duplicates (e.g., "akuko_api.vehiclemodel")')
        parser.add_argument('field_name', type=str, help='Field to check for duplicates (e.g., "name")')
        parser.add_argument('related_field_name', type=str, help='Related field (e.g., "make") to ensure duplicates are within the same make')

    def handle(self, *args, **options):
        model_name = options['model_name']
        field_name = options['field_name']
        related_field_name = options['related_field_name']

        try:
            # Load the specified model
            model = apps.get_model(model_name)
        except LookupError:
            self.stdout.write(self.style.ERROR(f'Model "{model_name}" not found.'))
            return

        # Find duplicates by grouping on the specified field and related field (make)
        duplicates = (
            model.objects
            .values(related_field_name, field_name)
            .annotate(count_id=models.Count('id'))
            .filter(count_id__gt=1)
        )

        # Delete duplicates while keeping the first occurrence by ID within each make group
        with transaction.atomic():
            for duplicate in duplicates:
                related_field_value = duplicate[related_field_name]
                field_value = duplicate[field_name]
                # Filter instances by the make and model name
                instances = model.objects.filter(**{
                    related_field_name: related_field_value,
                    field_name: field_value
                }).order_by('id')
                instance_to_keep = instances[0]  # Keep the first occurrence
                duplicates_to_delete = instances[1:]  # Duplicate instances to delete

                for instance in duplicates_to_delete:
                    # Delete duplicate instances, keeping the first occurrence
                    try:
                        instance.delete()
                        self.stdout.write(self.style.WARNING(
                            f'Deleted duplicate of {model_name} with {related_field_name} ID={related_field_value}, '
                            f'{field_name}="{field_value}", and ID={instance.id}'
                        ))
                    except ProtectedError as e:
                        self.stdout.write(self.style.ERROR(
                            f'Cannot delete {model_name} with ID={instance.id} due to protected related objects: {e}'
                        ))

        self.stdout.write(self.style.SUCCESS('Duplicate deletion complete.'))
