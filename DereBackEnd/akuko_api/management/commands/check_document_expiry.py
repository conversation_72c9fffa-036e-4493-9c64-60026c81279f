from django.core.management.base import BaseCommand
from django.utils import timezone
import asyncio
from akuko_api.tasks import check_document_expiry


class Command(BaseCommand):
    help = 'Check for expiring driver and vehicle documents and send notifications'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force check regardless of time',
        )
        parser.add_argument(
            '--days',
            type=int,
            nargs='+',
            default=[30, 7, 1],
            help='Days before expiry to check (default: 30 7 1)',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS(
                f'[{timezone.now()}] Starting document expiry check...'
            )
        )

        try:
            # Run the async function
            asyncio.run(check_document_expiry())
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'[{timezone.now()}] Document expiry check completed successfully!'
                )
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(
                    f'[{timezone.now()}] Error during document expiry check: {str(e)}'
                )
            )
            raise e
