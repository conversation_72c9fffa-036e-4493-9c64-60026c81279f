import asyncio
import signal
import time
import traceback
from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta

from ...tasks import check_payment_deadlines, check_weekly_reports, check_job_expiry, check_subscription_expiry, check_for_expired_subscriptions, check_document_expiry_schedule

class Command(BaseCommand):
    help = 'Runs the asynchronous notification service with improved performance'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.running = True
        self.last_pending_check_time = None
        self.email_semaphore = asyncio.Semaphore(5)  # Limit concurrent emails

    def handle_shutdown(self, signum, frame):
        self.stdout.write(self.style.WARNING('Shutdown signal received. Stopping service...'))
        self.running = False

    async def notification_service(self):
        """Main service loop with improved error handling and performance"""
        self.stdout.write(f"Service started. Checking every minute for scheduled tasks.")
        
        # Initialize the last check time
        self.last_pending_check_time = timezone.localtime() - timedelta(minutes=5)

        while self.running:
            try:
                current_time = timezone.localtime()
                
                # Run scheduled checks with timeout protection
                await asyncio.wait_for(
                    self.run_scheduled_checks(current_time),
                    timeout=50.0  # Leave 10 seconds buffer before next minute
                )
                
                # Process pending notifications every 5 minutes
                if self.should_process_pending_notifications(current_time):
                    await asyncio.wait_for(
                        self.process_pending_notifications(),
                        timeout=240.0  # 4 minutes max for pending processing
                    )
                    self.last_pending_check_time = current_time
                else:
                    # Log when the next pending notifications check will occur
                    next_pending_check = self.last_pending_check_time + timedelta(minutes=5)
                    time_until_next = (next_pending_check - current_time).total_seconds()
                    self.stdout.write(f"Next pending notifications check in {time_until_next:.0f}s at {next_pending_check.strftime('%H:%M:%S')}")
                
            except asyncio.TimeoutError:
                self.stdout.write(self.style.ERROR('Operations timed out, continuing to next cycle'))
            except Exception as e:
                self.stdout.write(self.style.ERROR(f'Main loop error: {e}\n{traceback.format_exc()}'))
            
            # Wait until next minute
            await self.wait_for_next_minute()

    async def run_scheduled_checks(self, current_time):
        """Run all scheduled checks with concurrent execution where safe"""
        try:
            # Run payment deadlines, job expiry, subscription expiry, and document expiry concurrently
            payment_task = asyncio.create_task(check_payment_deadlines())
            job_expiry_task = asyncio.create_task(check_job_expiry())
            subscription_expiry_task = asyncio.create_task(check_subscription_expiry())
            expire_subscriptions_task = asyncio.create_task(check_for_expired_subscriptions())
            document_expiry_task = asyncio.create_task(check_document_expiry_schedule())

            # Wait for all to complete
            await asyncio.gather(payment_task, job_expiry_task, subscription_expiry_task, expire_subscriptions_task, document_expiry_task, return_exceptions=True)
            
            # Only run weekly reports on Mondays (weekday() returns 0 for Monday)
            if current_time.weekday() == 0:
                self.stdout.write('Running weekly reports check (Monday)')
                await check_weekly_reports()
            else:
                self.stdout.write('Skipping weekly reports check (not Monday)')
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error in scheduled checks: {e}'))

    def should_process_pending_notifications(self, current_time):
        """Check if it's time to process pending notifications"""
        return (current_time - self.last_pending_check_time).total_seconds() >= 300  # 5 minutes

    async def process_pending_notifications(self):
        """Process pending notifications with rate limiting"""
        try:
            from ...models import Notification
            from asgiref.sync import sync_to_async
            
            # Get pending notifications in batches
            def get_pending_batch():
                return list(Notification.objects.filter(
                    status='pending'
                ).order_by('created_at')[:20])  # Process 20 at a time
            
            pending_notifications = await sync_to_async(get_pending_batch)()
            
            if not pending_notifications:
                self.stdout.write('No pending notifications to process')
                return
                
            self.stdout.write(f'Found {len(pending_notifications)} pending notifications to process')
            
            # Process with semaphore to limit concurrent operations
            tasks = []
            for notification in pending_notifications:
                task = asyncio.create_task(
                    self.process_single_notification(notification)
                )
                tasks.append(task)
            
            # Wait for all to complete
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Log detailed results
            success_count = sum(1 for r in results if r is True)
            failed_count = sum(1 for r in results if r is False)
            error_count = sum(1 for r in results if isinstance(r, Exception))
            
            self.stdout.write(
                f'Pending notifications processing complete: '
                f'{success_count} successful, {failed_count} failed, {error_count} errors'
            )
            
            # Log any exceptions
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    self.stdout.write(
                        self.style.ERROR(f'Notification {i+1} exception: {result}')
                    )
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error processing pending notifications: {e}'))

    async def process_single_notification(self, notification):
        """Process a single notification with rate limiting"""
        async with self.email_semaphore:  # Limit concurrent emails
            try:
                from django.core.mail import send_mail
                from django.conf import settings
                from asgiref.sync import sync_to_async
                
                self.stdout.write(f'Sending notification {notification.id} to {notification.recipient}')
                
                # Send the email
                await sync_to_async(send_mail)(
                    subject=notification.subject,
                    message='',
                    from_email=settings.DEFAULT_FROM_EMAIL,
                    recipient_list=[notification.recipient],
                    html_message=notification.message,
                    fail_silently=False,
                )
                
                # Mark as sent
                await sync_to_async(notification.mark_as_sent)()
                self.stdout.write(f'✓ Successfully sent notification {notification.id}')
                return True
                
            except Exception as e:
                # Mark as failed after 3 attempts
                attempts = getattr(notification, 'retry_count', 0) + 1
                if attempts >= 3:
                    await sync_to_async(notification.mark_as_failed)()
                    self.stdout.write(f'✗ Notification {notification.id} marked as failed after 3 attempts')
                else:
                    # Update retry count (you might need to add this field to your model)
                    notification.retry_count = attempts
                    await sync_to_async(notification.save)()
                    self.stdout.write(f'⚠ Notification {notification.id} failed (attempt {attempts}/3): {e}')
                
                return False

    async def wait_for_next_minute(self):
        """Wait until the next minute starts"""
        now = time.time()
        next_minute = (int(now / 60) + 1) * 60
        wait_time = next_minute - now
        
        next_time = timezone.localtime(timezone.make_aware(
            timezone.datetime.fromtimestamp(next_minute)
        ))
        
        self.stdout.write(f'Next check at {next_time.strftime("%H:%M:%S")} (in {wait_time:.1f}s)')
        await asyncio.sleep(wait_time)

    def handle(self, *args, **options):
        self.stdout.write('Starting enhanced notification service...')
        signal.signal(signal.SIGINT, self.handle_shutdown)
        signal.signal(signal.SIGTERM, self.handle_shutdown)
        
        try:
            asyncio.run(self.notification_service())
        except KeyboardInterrupt:
            self.stdout.write(self.style.SUCCESS('Service stopped manually'))
        finally:
            self.stdout.write(self.style.SUCCESS('Notification service has shut down'))
