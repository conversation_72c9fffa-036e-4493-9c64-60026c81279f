from django.core.management.base import BaseCommand
from django.db.models import Q
from django.utils import timezone
from datetime import datetime, timedelta
from akuko_api.models import Driver, Vehicle, Revenue, DriverRating, PaymentSettings
from akuko_api.rating_utils import calculate_driver_rating


class Command(BaseCommand):
    help = 'Calculate ratings for existing drivers based on their payment history'

    def add_arguments(self, parser):
        parser.add_argument(
            '--start-date',
            type=str,
            help='Start date for rating calculation (YYYY-MM-DD). Defaults to 30 days ago.',
        )
        parser.add_argument(
            '--end-date',
            type=str,
            help='End date for rating calculation (YYYY-MM-DD). Defaults to today.',
        )
        parser.add_argument(
            '--driver-id',
            type=int,
            help='Calculate rating for specific driver ID only',
        )
        parser.add_argument(
            '--vehicle-id',
            type=int,
            help='Calculate rating for specific vehicle ID only',
        )
        parser.add_argument(
            '--partner-id',
            type=int,
            help='Calculate ratings for all drivers of specific partner ID only',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be calculated without actually creating ratings',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Recalculate ratings even if they already exist',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Starting driver rating calculation...'))
        
        # Parse dates
        end_date = timezone.now().date()
        if options['end_date']:
            try:
                end_date = datetime.strptime(options['end_date'], '%Y-%m-%d').date()
            except ValueError:
                self.stdout.write(self.style.ERROR('Invalid end date format. Use YYYY-MM-DD'))
                return

        start_date = end_date - timedelta(days=30)  # Default to 30 days ago
        if options['start_date']:
            try:
                start_date = datetime.strptime(options['start_date'], '%Y-%m-%d').date()
            except ValueError:
                self.stdout.write(self.style.ERROR('Invalid start date format. Use YYYY-MM-DD'))
                return

        self.stdout.write(f'Calculating ratings from {start_date} to {end_date}')

        # Build queryset for driver-vehicle combinations
        revenue_filter = Q(
            date__gte=start_date,
            date__lte=end_date,
            deleted=False,
            driver__isnull=False,
            vehicle__isnull=False
        )

        # Apply filters
        if options['driver_id']:
            revenue_filter &= Q(driver_id=options['driver_id'])
        if options['vehicle_id']:
            revenue_filter &= Q(vehicle_id=options['vehicle_id'])
        if options['partner_id']:
            revenue_filter &= Q(vehicle__partner_id=options['partner_id'])

        # Get unique driver-vehicle combinations from revenue records
        driver_vehicle_combinations = Revenue.objects.filter(
            revenue_filter
        ).values('driver', 'vehicle').distinct()

        total_combinations = driver_vehicle_combinations.count()
        self.stdout.write(f'Found {total_combinations} driver-vehicle combinations to process')

        if total_combinations == 0:
            self.stdout.write(self.style.WARNING('No driver-vehicle combinations found with the given criteria'))
            return

        processed = 0
        created = 0
        updated = 0
        errors = 0

        for combination in driver_vehicle_combinations:
            try:
                driver_id = combination['driver']
                vehicle_id = combination['vehicle']

                # Get driver and vehicle objects
                try:
                    driver = Driver.objects.get(id=driver_id)
                    vehicle = Vehicle.objects.get(id=vehicle_id)
                except (Driver.DoesNotExist, Vehicle.DoesNotExist):
                    self.stdout.write(
                        self.style.WARNING(f'Driver {driver_id} or Vehicle {vehicle_id} not found')
                    )
                    errors += 1
                    continue

                # Check if rating already exists
                existing_rating = DriverRating.objects.filter(
                    driver=driver,
                    vehicle=vehicle
                ).first()

                if existing_rating and not options['force']:
                    self.stdout.write(
                        f'Rating already exists for Driver {driver.first_name} {driver.last_name} '
                        f'and Vehicle {vehicle.registration_number}. Use --force to recalculate.'
                    )
                    continue

                # Check if payment settings exist for this combination
                payment_settings = PaymentSettings.objects.filter(
                    Q(vehicle=vehicle) | Q(partner=vehicle.partner, vehicle__isnull=True)
                ).first()

                if not payment_settings:
                    self.stdout.write(
                        self.style.WARNING(
                            f'No payment settings found for Vehicle {vehicle.registration_number} '
                            f'or Partner {vehicle.partner}. Skipping.'
                        )
                    )
                    continue

                if options['dry_run']:
                    self.stdout.write(
                        f'[DRY RUN] Would calculate rating for Driver {driver.first_name} {driver.last_name} '
                        f'and Vehicle {vehicle.registration_number}'
                    )
                    processed += 1
                    continue

                # Calculate the rating
                self.stdout.write(
                    f'Calculating rating for Driver {driver.first_name} {driver.last_name} '
                    f'and Vehicle {vehicle.registration_number}...'
                )

                # Set calculation start date to the earliest revenue date for this combination
                earliest_revenue = Revenue.objects.filter(
                    driver=driver,
                    vehicle=vehicle,
                    date__gte=start_date,
                    deleted=False
                ).order_by('date').first()

                calculation_start_date = earliest_revenue.date if earliest_revenue else start_date

                # Delete existing rating if force is enabled
                if existing_rating and options['force']:
                    existing_rating.delete()
                    self.stdout.write(f'Deleted existing rating for recalculation')

                # Calculate rating with proper start date
                rating = calculate_driver_rating(driver, vehicle, end_date, calculation_start_date)

                if existing_rating:
                    updated += 1
                    self.stdout.write(
                        self.style.SUCCESS(
                            f'Updated rating: {rating.rating_score:.2f} '
                            f'({rating.total_points} points / {rating.total_payment_days} days)'
                        )
                    )
                else:
                    created += 1
                    self.stdout.write(
                        self.style.SUCCESS(
                            f'Created rating: {rating.rating_score:.2f} '
                            f'({rating.total_points} points / {rating.total_payment_days} days)'
                        )
                    )

                processed += 1

            except Exception as e:
                errors += 1
                self.stdout.write(
                    self.style.ERROR(f'Error processing combination {combination}: {str(e)}')
                )

        # Summary
        self.stdout.write('\n' + '='*50)
        self.stdout.write(self.style.SUCCESS('CALCULATION SUMMARY'))
        self.stdout.write('='*50)
        self.stdout.write(f'Total combinations found: {total_combinations}')
        self.stdout.write(f'Successfully processed: {processed}')
        self.stdout.write(f'Ratings created: {created}')
        self.stdout.write(f'Ratings updated: {updated}')
        self.stdout.write(f'Errors: {errors}')
        
        if options['dry_run']:
            self.stdout.write(self.style.WARNING('\nThis was a DRY RUN - no ratings were actually created/updated'))
        else:
            self.stdout.write(self.style.SUCCESS('\nDriver rating calculation completed!'))

        # Additional recommendations
        if errors > 0:
            self.stdout.write('\n' + self.style.WARNING('RECOMMENDATIONS:'))
            self.stdout.write('- Check that all vehicles have payment settings configured')
            self.stdout.write('- Ensure driver and vehicle records are properly linked')
            self.stdout.write('- Review error messages above for specific issues')

        if created > 0 or updated > 0:
            self.stdout.write('\n' + self.style.SUCCESS('NEXT STEPS:'))
            self.stdout.write('- Ratings will now update automatically when new payments are made')
            self.stdout.write('- Partners can view ratings at: GET /api/driver-ratings/')
            self.stdout.write('- Drivers can view their ratings at: GET /api/drivers/{id}/rating/')
