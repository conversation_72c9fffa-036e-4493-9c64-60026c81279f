from django.contrib import admin
from .models import JobApplication, User, Driver, Partner, Job, VehicleMake, VehicleModel, WorkArea, VehicleType, PasswordResetToken, Vehicle, Notification, NotificationSubscriber, Expenditure, Revenue, PaymentSettings, Enquiry, Subscription, SubscriptionVehicle, PaymentTransaction, DriverRating, DriverFlag


class UserAdmin(admin.ModelAdmin):
    list_display = ('id', 'email', 'first_name', 'last_name', 'role', 'is_active', 'date_joined',
                    'email_verified', 'notification_subscribe', 'verification_code', 'is_staff', 'is_superuser', )
    search_fields = ('email', 'first_name', 'last_name', 'role')
    list_filter = ('is_active', 'role', 'date_joined')
    ordering = ('last_name', 'first_name')
    fields = ('email', 'first_name', 'last_name', 'role',
              'is_active', 'date_joined', 'email_verified')
    readonly_fields = ('date_joined',)


class DriverAdmin(admin.ModelAdmin):
    list_display = (
        'id', 'first_name', 'last_name', 'email', 'id_number', 'date_of_birth',
        'gender', 'mobile_number', 'partner', 'vehicle_type', 'work_area',
        'psv_photo', 'id_photo', 'license_photo', 'good_conduct_photo',
        'uber_trips_screenshot', 'license_expiry_date', 'psv_expiry_date',
        'good_conduct_expiry_date', 'user'
    )
    search_fields = ('first_name', 'last_name', 'email', 'id_number')
    list_filter = ('gender', 'date_of_birth', 'work_area', 'vehicle_type', 'license_expiry_date', 'psv_expiry_date', 'good_conduct_expiry_date')
    ordering = ('last_name', 'first_name')
    fields = (
        'first_name', 'last_name', 'email', 'id_number', 'date_of_birth',
        'gender', 'mobile_number', 'password', 'confirm_password',
        'vehicle_type', 'work_area', 'psv_photo', 'id_photo',
        'license_photo', 'good_conduct_photo', 'uber_trips_screenshot',
        'license_expiry_date', 'psv_expiry_date', 'good_conduct_expiry_date'
    )
    readonly_fields = ('password', 'confirm_password')


class PartnerAdmin(admin.ModelAdmin):
    list_display = (
        'id', 'first_name', 'last_name', 'email', 'id_number', 'company_number',
        'company_name', 'mobile_number',
        'id_photo', 'user'
    )
    search_fields = ('first_name', 'last_name', 'email',
                     'id_number', 'company_name')
    # list_filter = ('preferred_work_area', 'vehicle_type')
    ordering = ('last_name', 'first_name')
    fields = (
        'first_name', 'last_name', 'email', 'id_number', 'company_number',
        'company_name', 'password', 'confirm_password',
        'id_photo', 'car_photo'
    )
    readonly_fields = ('password', 'confirm_password')


class JobAdmin(admin.ModelAdmin):
    list_display = ('id', 'partner', 'vehicle_make', 'vehicle_model', 'status', 'preferred_work_area',
                    'requirements', 'vehicle_photo', 'work_days', 'min_age', 'max_age', 'status', 'created_at', 'closed_at')
    search_fields = ('partner__first_name', 'partner__last_name',
                     'vehicle_make__name', 'status', 'vehicle_model__name')
    list_filter = ('preferred_work_area', 'work_days')
    ordering = ('partner', 'vehicle_make', 'vehicle_model')
    fields = (
        'partner', 'vehicle_make', 'vehicle_model', 'status', 'preferred_work_area',
        'requirements', 'vehicle_photo', 'work_days',
    )


class VehicleMakeAdmin(admin.ModelAdmin):
    list_display = ('id', 'name',)
    search_fields = ('name',)
    ordering = ('name',)
    fields = ('name',)


class VehicleModelAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'make')
    search_fields = ('name', 'make__name')
    list_filter = ('make',)
    ordering = ('make', 'name')
    fields = ('name', 'make')


class WorkAreaAdmin(admin.ModelAdmin):
    list_display = ('id', 'name',)
    search_fields = ('name',)
    ordering = ('name',)
    fields = ('name',)


class VehicleTypeAdmin(admin.ModelAdmin):
    list_display = ('id', 'name',)
    search_fields = ('name',)
    ordering = ('name',)
    fields = ('name',)


class JobApplicationAdmin(admin.ModelAdmin):
    list_display = ('id', 'driver', 'job', 'status', 'applied_at', 'hired_at',
                    'un_hired_at', 'removed_by_partner', 'withdrawn_by_driver')
    list_filter = ('status', 'job', 'driver',
                   'removed_by_partner', 'withdrawn_by_driver')
    search_fields = ('driver__name', 'job__partner__first_name',
                     'job__partner__last_name')
    ordering = ('-applied_at',)

    def save_model(self, request, obj, form, change):
        # Ensure the status is updated based on flags
        if obj.removed_by_partner:
            obj.status = 'rejected'
        elif obj.withdrawn_by_driver:
            obj.status = 'withdrawn'
        else:
            obj.status = 'active'
        super().save_model(request, obj, form, change)


class PasswordResetTokenAdmin(admin.ModelAdmin):
    list_display = ('user', 'token', 'created_at', 'is_expired')
    readonly_fields = ('created_at',)
    search_fields = ('user__email', 'token')
    list_filter = ('created_at',)


class VehicleAdmin(admin.ModelAdmin):
    list_display = ('id', 'partner', 'registration_number', 'vehicle_make',
                    'vehicle_model',  'driver', 'status', 'preferred_work_area',
                    'year_of_manufacture', 'ntsa_expiry_date',
                    'insurance_expiry_date', 'created_at')
    search_fields = ('registration_number', 'partner__first_name',
                     'partner__last_name', 'driver__first_name', 'driver__last_name')
    list_filter = ('vehicle_make', 'vehicle_model', 'preferred_work_area',
                   'work_days', 'ntsa_expiry_date', 'insurance_expiry_date')


class NotificationAdmin(admin.ModelAdmin):
    list_display = ('id', 'subject', 'type', 'recipient',
                    'status', 'sent_at', 'created_at')
    list_filter = ('type', 'status')
    search_fields = ('subject', 'recipient')


class NotificationSubscriberAdmin(admin.ModelAdmin):
    list_display = ('user', 'subscribed_at')
    search_fields = ('user__email',)

# Register Expenditure model
class ExpenditureAdmin(admin.ModelAdmin):
    list_display = ('id', 'vehicle', 'partner', 'date',
                    'item_name', 'description', 'quantity', 'amount', 'deleted',  'created_at')
    search_fields = ('item_name', 'description', 'vehicle__registration_number',
                     'partner__company_name')
    list_filter = ('date', 'vehicle', 'partner')
    ordering = ('-date',)


class RevenueAdmin(admin.ModelAdmin):
    list_display = ('id', 'driver', 'vehicle', 'date',
                    'amount', 'confirmation_message', 'deleted')
    search_fields = ('driver__first_name', 'driver__last_name',
                     'vehicle__registration_number')
    list_filter = ('date', 'driver', 'vehicle')
    ordering = ('-date',)
    readonly_fields = ('driver', 'vehicle')


class PaymentSettingsAdmin(admin.ModelAdmin):
    list_display = ('id', 'partner', 'vehicle', 'daily_amount',
                    'payment_days', 'deadline_time')
    search_fields = ('partner__first_name', 'partner__last_name',
                     'vehicle__registration_number')
    list_filter = ('partner', 'vehicle')
    fields = ('partner', 'vehicle', 'daily_amount',
              'payment_days', 'deadline_time')
    readonly_fields = ('partner',)

class EnquiryAdmin(admin.ModelAdmin):
    list_display = ('id', 'first_name', 'last_name', 'email',
                    'phone_number', 'message', 'created_at')
   
class SubscriptionAdmin(admin.ModelAdmin):
    list_display = ('id', 'partner', 'plan_type', 'billing_cycle', 'vehicle_count', 'amount', 'status', 'start_date', 'expiry_date')


class SubscriptionVehicleAdmin(admin.ModelAdmin):
    list_display = ('id', 'subscription', 'vehicle', 'added_at')


class PaymentTransactionAdmin(admin.ModelAdmin):
    list_display = ('id', 'user', 'transaction_type', 'amount', 'phone_number', 'mpesa_receipt_number', 'transaction_id', 'status', 'transaction_date', 'created_at')


class DriverRatingAdmin(admin.ModelAdmin):
    list_display = ('id', 'driver', 'vehicle', 'partner', 'rating_score',
                    'total_points', 'total_payment_days', 'last_payment_date',
                    'consecutive_non_payment_days', 'is_active', 'last_updated')
    search_fields = ('driver__first_name', 'driver__last_name',
                     'vehicle__registration_number', 'partner__first_name', 'partner__last_name')
    list_filter = ('is_active', 'partner', 'last_payment_date', 'last_updated')
    readonly_fields = ('rating_score', 'total_points', 'total_payment_days',
                       'calculation_start_date', 'last_updated', 'created_at')
    ordering = ('-last_updated',)



admin.site.register(Expenditure, ExpenditureAdmin)
admin.site.register(Revenue, RevenueAdmin)
admin.site.register(PaymentSettings, PaymentSettingsAdmin)
admin.site.register(Notification, NotificationAdmin)
admin.site.register(NotificationSubscriber, NotificationSubscriberAdmin)
admin.site.register(Vehicle, VehicleAdmin)
admin.site.register(User, UserAdmin)
admin.site.register(Driver, DriverAdmin)
admin.site.register(Partner, PartnerAdmin)
admin.site.register(Job, JobAdmin)
admin.site.register(VehicleMake, VehicleMakeAdmin)
admin.site.register(VehicleModel, VehicleModelAdmin)
admin.site.register(WorkArea, WorkAreaAdmin)
admin.site.register(VehicleType, VehicleTypeAdmin)
admin.site.register(JobApplication, JobApplicationAdmin)
admin.site.register(PasswordResetToken, PasswordResetTokenAdmin)
admin.site.register(Enquiry, EnquiryAdmin)
admin.site.register(Subscription, SubscriptionAdmin)
admin.site.register(SubscriptionVehicle, SubscriptionVehicleAdmin)
admin.site.register(PaymentTransaction, PaymentTransactionAdmin)
admin.site.register(DriverRating, DriverRatingAdmin)


class DriverFlagAdmin(admin.ModelAdmin):
    list_display = ('id', 'driver', 'partner', 'is_active', 'created_at',
                    'resolved_at', 'resolved_by')
    search_fields = ('driver__first_name', 'driver__last_name', 'driver__email',
                     'partner__company_name', 'reason')
    list_filter = ('is_active', 'created_at', 'resolved_at', 'partner')
    readonly_fields = ('created_at', 'resolved_at')
    ordering = ('-created_at',)

    fieldsets = (
        ('Flag Information', {
            'fields': ('driver', 'partner', 'reason', 'is_active')
        }),
        ('Metadata', {
            'fields': ('flagged_by_user', 'created_at')
        }),
        ('Resolution', {
            'fields': ('resolved_at', 'resolved_by', 'resolution_notes'),
            'classes': ('collapse',)
        })
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'driver', 'partner', 'flagged_by_user', 'resolved_by'
        )


admin.site.register(DriverFlag, DriverFlagAdmin)
