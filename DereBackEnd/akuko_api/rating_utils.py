from datetime import datetime, timedelta, time
from django.utils import timezone
from django.db.models import Q
from .models import PaymentSettings, Revenue, DriverRating


def get_payment_deadline(vehicle, payment_date):
    """
    Get the payment deadline for a specific vehicle and date.
    Returns the deadline datetime or None if no payment settings found.
    """
    try:
        # Get payment settings for the vehicle or partner
        payment_settings = PaymentSettings.objects.filter(
            Q(vehicle=vehicle) | Q(partner=vehicle.partner, vehicle__isnull=True)
        ).first()
        
        if not payment_settings:
            return None
        
        # Check if the payment_date is a payment day
        payment_days = [day.strip() for day in payment_settings.payment_days.split(',')]
        day_name = payment_date.strftime('%A')
        
        if day_name not in payment_days:
            return None
        
        # Combine payment date with deadline time
        deadline_datetime = timezone.make_aware(
            datetime.combine(payment_date, payment_settings.deadline_time)
        )
        
        return deadline_datetime
        
    except Exception as e:
        print(f"Error getting payment deadline: {str(e)}")
        return None


def calculate_payment_status(revenue_record):
    """
    Calculate payment status for a revenue record.
    Returns tuple: (status, points)
    Status: 'early', 'on_time', 'late', 'no_payment'
    Points: 5.0 for early/on_time, 2.5 for late, 0.0 for no_payment
    """
    if not revenue_record:
        return 'no_payment', 0.0
    
    deadline = get_payment_deadline(revenue_record.vehicle, revenue_record.date)
    
    if not deadline:
        # If no payment settings, consider any payment as on-time
        return 'on_time', 5.0
    
    payment_time = revenue_record.created_at
    
    if payment_time <= deadline:
        return 'on_time', 5.0
    else:
        return 'late', 2.5


def get_expected_payment_days(vehicle, start_date, end_date):
    """
    Get list of dates when payments are expected for a vehicle within a date range.
    """
    try:
        payment_settings = PaymentSettings.objects.filter(
            Q(vehicle=vehicle) | Q(partner=vehicle.partner, vehicle__isnull=True)
        ).first()

        if not payment_settings:
            return []

        payment_days = [day.strip() for day in payment_settings.payment_days.split(',')]
        day_mapping = {
            'Monday': 0, 'Tuesday': 1, 'Wednesday': 2, 'Thursday': 3,
            'Friday': 4, 'Saturday': 5, 'Sunday': 6
        }

        payment_day_numbers = [day_mapping.get(day) for day in payment_days if day in day_mapping]

        expected_dates = []
        current_date = start_date

        while current_date <= end_date:
            if current_date.weekday() in payment_day_numbers:
                expected_dates.append(current_date)
            current_date += timedelta(days=1)

        return expected_dates

    except Exception as e:
        print(f"Error getting expected payment days: {str(e)}")
        return []


def calculate_driver_rating(driver, vehicle, calculation_date=None, start_date=None):
    """
    Calculate or update driver rating for a specific driver-vehicle combination.
    Returns the DriverRating instance.

    Args:
        driver: Driver instance
        vehicle: Vehicle instance
        calculation_date: End date for calculation (defaults to today)
        start_date: Start date for calculation (defaults to earliest revenue or 30 days ago)
    """
    if calculation_date is None:
        calculation_date = timezone.now().date()

    # Determine start date if not provided
    if start_date is None:
        # Try to find the earliest revenue record for this combination
        earliest_revenue = Revenue.objects.filter(
            driver=driver,
            vehicle=vehicle,
            deleted=False
        ).order_by('date').first()

        if earliest_revenue:
            start_date = earliest_revenue.date
        else:
            # Default to 30 days ago if no revenue found
            start_date = calculation_date - timedelta(days=30)

    # Get or create driver rating record
    driver_rating, created = DriverRating.objects.get_or_create(
        driver=driver,
        vehicle=vehicle,
        defaults={
            'partner': vehicle.partner,
            'calculation_start_date': start_date
        }
    )

    # Update calculation start date if this is an existing record and we have a better start date
    if not created and start_date < driver_rating.calculation_start_date:
        driver_rating.calculation_start_date = start_date
        driver_rating.save()
    
    # If rating is inactive due to 3+ days non-payment, check if we should reactivate
    if not driver_rating.is_active:
        # Check if there's a recent payment
        recent_payment = Revenue.objects.filter(
            driver=driver,
            vehicle=vehicle,
            date__gte=calculation_date - timedelta(days=3),
            deleted=False
        ).exists()
        
        if recent_payment:
            driver_rating.is_active = True
            driver_rating.consecutive_non_payment_days = 0
    
    # If still inactive, return without calculation
    if not driver_rating.is_active:
        return driver_rating
    
    # Get all revenue records for this driver-vehicle combination from start date
    revenues = Revenue.objects.filter(
        driver=driver,
        vehicle=vehicle,
        date__gte=driver_rating.calculation_start_date,
        date__lte=calculation_date,
        deleted=False
    ).order_by('date')
    
    # Get expected payment days from start date to calculation date
    expected_dates = get_expected_payment_days(
        vehicle,
        driver_rating.calculation_start_date,
        calculation_date
    )

    total_points = 0.0
    total_payment_days = len(expected_dates)
    last_payment_date = None
    consecutive_non_payment_days = 0
    
    # Create a mapping of payment dates to revenue records
    payment_map = {revenue.date: revenue for revenue in revenues}
    
    # Calculate points for each expected payment day
    for expected_date in expected_dates:
        if expected_date in payment_map:
            # Payment was made
            revenue = payment_map[expected_date]
            status, points = calculate_payment_status(revenue)
            total_points += points
            last_payment_date = expected_date
            consecutive_non_payment_days = 0
        else:
            # No payment made
            total_points += 0.0
            consecutive_non_payment_days += 1
    
    # Check if we should stop calculating due to 3+ consecutive non-payment days
    if consecutive_non_payment_days >= 3:
        driver_rating.is_active = False
    
    # Update driver rating
    driver_rating.total_points = total_points
    driver_rating.total_payment_days = total_payment_days
    driver_rating.last_payment_date = last_payment_date
    driver_rating.consecutive_non_payment_days = consecutive_non_payment_days
    driver_rating.calculate_rating()
    driver_rating.save()
    
    return driver_rating


def get_driver_age_group(driver):
    """
    Get driver age group based on date of birth.
    Returns: 'below_25', '26_39', '40_above', or 'unknown'
    """
    if not driver.date_of_birth:
        return 'unknown'
    
    today = timezone.now().date()
    age = today.year - driver.date_of_birth.year
    
    # Adjust for birthday not yet occurred this year
    if today < driver.date_of_birth.replace(year=today.year):
        age -= 1
    
    if age < 25:
        return 'below_25'
    elif 25 <= age <= 39:
        return '26_39'
    else:
        return '40_above'


def update_all_driver_ratings():
    """
    Utility function to update all driver ratings.
    Useful for batch processing or data migration.
    """
    from .models import Driver, Vehicle

    updated_count = 0

    # Get all active driver-vehicle combinations
    vehicles_with_drivers = Vehicle.objects.filter(
        driver__isnull=False,
        status='active'
    ).select_related('driver', 'partner')

    for vehicle in vehicles_with_drivers:
        try:
            calculate_driver_rating(vehicle.driver, vehicle)
            updated_count += 1
        except Exception as e:
            print(f"Error updating rating for driver {vehicle.driver.id} and vehicle {vehicle.id}: {str(e)}")

    return updated_count


def calculate_ratings_for_existing_drivers(start_date=None, end_date=None):
    """
    Calculate ratings for existing drivers based on their payment history.
    This is useful when introducing the rating system to an existing project.

    Args:
        start_date: Start date for calculation (defaults to 30 days ago)
        end_date: End date for calculation (defaults to today)

    Returns:
        dict: Summary of the calculation process
    """
    from datetime import timedelta
    from django.db.models import Q
    from .models import Revenue, PaymentSettings

    if end_date is None:
        end_date = timezone.now().date()
    if start_date is None:
        start_date = end_date - timedelta(days=30)

    print(f"Calculating ratings for period: {start_date} to {end_date}")

    # Get unique driver-vehicle combinations from revenue records
    driver_vehicle_combinations = Revenue.objects.filter(
        date__gte=start_date,
        date__lte=end_date,
        deleted=False,
        driver__isnull=False,
        vehicle__isnull=False
    ).values('driver', 'vehicle').distinct()

    total_combinations = driver_vehicle_combinations.count()
    processed = 0
    created = 0
    errors = 0

    print(f"Found {total_combinations} driver-vehicle combinations to process")

    for combination in driver_vehicle_combinations:
        try:
            from .models import Driver, Vehicle

            driver = Driver.objects.get(id=combination['driver'])
            vehicle = Vehicle.objects.get(id=combination['vehicle'])

            # Check if payment settings exist
            payment_settings = PaymentSettings.objects.filter(
                Q(vehicle=vehicle) | Q(partner=vehicle.partner, vehicle__isnull=True)
            ).first()

            if not payment_settings:
                print(f"No payment settings for vehicle {vehicle.registration_number}, skipping")
                continue

            # Check if rating already exists
            existing_rating = DriverRating.objects.filter(
                driver=driver,
                vehicle=vehicle
            ).first()

            if existing_rating:
                print(f"Rating already exists for {driver.first_name} {driver.last_name} - {vehicle.registration_number}")
                continue

            # Calculate rating
            rating = calculate_driver_rating(driver, vehicle, end_date)

            # Set calculation start date to earliest revenue date
            earliest_revenue = Revenue.objects.filter(
                driver=driver,
                vehicle=vehicle,
                date__gte=start_date,
                deleted=False
            ).order_by('date').first()

            if earliest_revenue:
                rating.calculation_start_date = earliest_revenue.date
                rating.save()

            created += 1
            processed += 1
            print(f"Created rating for {driver.first_name} {driver.last_name} - {vehicle.registration_number}: {rating.rating_score:.2f}")

        except Exception as e:
            errors += 1
            print(f"Error processing combination {combination}: {str(e)}")

    summary = {
        'total_combinations': total_combinations,
        'processed': processed,
        'created': created,
        'errors': errors,
        'start_date': start_date,
        'end_date': end_date
    }

    print(f"\nSummary: {created} ratings created, {errors} errors out of {total_combinations} combinations")
    return summary


def handle_driver_partner_transfer(driver, old_partner, new_partner):
    """
    Handle driver transfer between partners while maintaining continuous rating calculation.
    Instead of deactivating ratings, this function updates the partner field to ensure
    ratings continue to be calculated without starting over.
    
    Args:
        driver: Driver instance being transferred
        old_partner: Previous partner instance
        new_partner: New partner instance
        
    Returns:
        dict: Summary of the transfer operation
    """
    from .models import DriverRating
    
    # Find all active ratings for this driver with the old partner
    old_ratings = DriverRating.objects.filter(
        driver=driver,
        partner=old_partner,
        is_active=True
    )
    
    transferred_ratings = []
    
    for rating in old_ratings:
        # Update the partner field while keeping the rating active
        rating.partner = new_partner
        rating.save()
        
        transferred_ratings.append({
            'vehicle_id': rating.vehicle.id,
            'vehicle_registration': rating.vehicle.registration_number,
            'rating_transferred': True,
            'current_rating': float(rating.rating_score) if rating.total_payment_days > 0 else 0.0,
            'total_points': float(rating.total_points),
            'payment_days': rating.total_payment_days,
            'is_active': rating.is_active
        })
    
    return {
        'driver_id': driver.id,
        'driver_name': f"{driver.first_name} {driver.last_name}".strip(),
        'old_partner_id': old_partner.id,
        'new_partner_id': new_partner.id,
        'transfer_completed': True,
        'ratings_transferred': len(transferred_ratings),
        'rating_details': transferred_ratings
    }
