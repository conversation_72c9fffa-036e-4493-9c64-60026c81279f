from django.db import transaction
from django.core.mail import send_mail  # send mail import
from django.core.cache import cache  # cache import
from django.conf import settings # settings import
from asgiref.sync import sync_to_async
from django.db.models import Sum
from django.db import models
from django.utils import timezone
import asyncio

from .models import Partner, PaymentSettings, Vehicle, Revenue, Notification, Job, Expenditure, Subscription, Driver
from .email_templates import get_subscription_expiry_template

def format_currency(amount):
    """Format amount to KES with comma separators"""
    return f"{amount:,.2f}"

# Add a utility function for immediate email sending
@sync_to_async
def send_immediate_email(subject, message, recipient_email):
    """Send an email immediately and record it as a notification"""
    try:
        print(f"[Email] Sending immediate email to {recipient_email}: {subject}")
        # Attempt to send the email
        send_mail(
            subject=subject,
            message='',  # Empty plain text message
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[recipient_email],
            html_message=message,
            fail_silently=False,
        )
        
        # Create a notification record marked as sent
        notification = Notification.objects.create(
            subject=subject,
            type="email",
            recipient=recipient_email,
            status="sent",  # Mark as sent immediately
            message=message
        )
        # Use the mark_as_sent method to update sent_at field
        notification.mark_as_sent()
        
        print(f"[Email] Successfully sent email to {recipient_email}")
        return True, notification
    except Exception as e:
        # If email sending fails, create a pending notification as fallback
        print(f"[Email] Failed to send immediate email to {recipient_email}: {str(e)}")
        notification = Notification.objects.create(
            subject=subject,
            type="email",
            recipient=recipient_email,
            status="pending",  # Mark as pending so it will be retried
            message=message
        )
        notification.mark_as_failed()
        return False, notification

@sync_to_async
def create_payment_reminder_notification(partner, vehicle_data_list):
    """Create Daily Revenue Report and send it immediately"""
    recipient_email = None
    partner_name = 'Partner'

    if partner.user:
        if hasattr(partner.user, 'email') and partner.user.email:
            recipient_email = partner.user.email
        if hasattr(partner.user, 'first_name') and partner.user.first_name:
            partner_name = partner.user.first_name
    elif hasattr(partner, 'email') and partner.email:
        recipient_email = partner.email

    if not recipient_email:
        print(f"[Daily Report] Partner {partner.id} has no email, skipping report.")
        return None, None # Indicate no notification was created

    vehicle_rows = ""
    for vehicle_info in vehicle_data_list:
        amount_display = f"KES {format_currency(vehicle_info.get('amount_paid_24hr', 0))}" if vehicle_info.get('amount_paid_24hr', 0) > 0 else "—"
        status_color = "green" if "✅ Paid" in vehicle_info.get('status', '') else "red"
        
        vehicle_rows += f"""
        <tr>
            <td style="padding: 8px; border-bottom: 1px solid #eee;">{vehicle_info.get('registration', 'N/A')}</td>
            <td style="padding: 8px; border-bottom: 1px solid #eee;">{vehicle_info.get('driver', 'N/A')}</td>
            <td style="padding: 8px; border-bottom: 1px solid #eee; text-align: right;">{amount_display}</td>
            <td style="padding: 8px; border-bottom: 1px solid #eee; color: {status_color};"><strong>{vehicle_info.get('status', 'N/A')}</strong></td>
        </tr>
        """

    html_message = f"""
    <html>
    <body style="font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f4f4f4;">
        <div style="max-width: 600px; margin: auto; background-color: #ffffff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <h2 style="color: #333333; border-bottom: 2px solid #007bff; padding-bottom: 10px;">🚗 Daily Revenue Report</h2>
            <p style="font-size: 16px; color: #555555;">Hello {partner_name},</p>
            <p style="font-size: 16px; color: #555555;">Here is your daily revenue summary for the 24-hour period ending {timezone.localtime().strftime('%B %d, %Y %H:%M')}.</p>
            
            <table style="width: 100%; border-collapse: collapse; margin-top: 20px; font-size: 14px;">
                <thead>
                    <tr style="background-color: #007bff; color: #ffffff;">
                        <th style="padding: 10px; text-align: left;">Car Reg.</th>
                        <th style="padding: 10px; text-align: left;">Driver Name</th>
                        <th style="padding: 10px; text-align: right;">Amount Paid (KES)</th>
                        <th style="padding: 10px; text-align: left;">Status</th>
                    </tr>
                </thead>
                <tbody>
                    {vehicle_rows}
                </tbody>
            </table>
            
            <p style="font-size: 16px; color: #555555; margin-top: 20px;">
                🔔 <strong>Note:</strong> Vehicles marked ❌ have not met the expected daily amount in the last 24 hours. Please follow up accordingly.
            </p>
            
            <p style="font-size: 14px; color: #999999; margin-top: 30px; text-align: center;">
                Best regards,<br><strong>KadereConnect Team</strong><br>
                🌐 <a href="https://kadereconnect.co.ke" style="color: #007bff; text-decoration: none;">kadereconnect.co.ke</a>
            </p>
        </div>
    </body>
    </html>
    """
    
    subject = "Daily Revenue Report" # Explicitly set subject
    
    try:
        # Send email using Django's send_mail since we're in sync context
        from django.core.mail import send_mail
        from django.conf import settings
        
        print(f"[Daily Report] Sending email to {recipient_email}: {subject}")
        
        send_mail(
            subject=subject,
            message='',  # Empty plain text message
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[recipient_email],
            html_message=html_message,
            fail_silently=False,
        )
        
        # Create a notification record marked as sent
        notification = Notification.objects.create(
            subject=subject,
            type="email",
            recipient=recipient_email,
            status="sent",
            message=html_message
        )
        notification.mark_as_sent()
        
        print(f"[Daily Report] Email sent successfully to {recipient_email}")
        return True, notification
        
    except Exception as e:
        print(f"[Daily Report] Failed to send email to {recipient_email}: {str(e)}")
        
        # Create a notification record marked as failed
        notification = Notification.objects.create(
            subject=subject,
            type="email",
            recipient=recipient_email,
            status="pending",  # Mark as pending so it will be retried
            message=html_message
        )
        notification.mark_as_failed()
        return False, notification


async def check_payment_deadlines():
    """Async function to check payment deadlines and trigger daily revenue reports."""
    current_time = timezone.localtime() # This is the exact time the check runs (e.g., 19:07:00)
    
    current_weekday = current_time.strftime('%A')
    current_hour = current_time.hour
    current_minute = current_time.minute
    
    payment_settings_qs = PaymentSettings.objects.filter(
        payment_days__contains=current_weekday,
        deadline_time__hour=current_hour, # Filter by hour and minute directly
        deadline_time__minute=current_minute
    ).select_related('partner__user', 'vehicle__driver__user').order_by('partner_id', '-id').distinct('partner_id')
    # Using distinct on partner_id after ordering by -id ensures we get the latest setting per partner if multiple exist for the same deadline.
    
    active_settings_for_now = await sync_to_async(list)(payment_settings_qs)
    
    if not active_settings_for_now:
        # print(f"[Payment Deadline] No payment settings match current time: {current_weekday} {current_hour:02d}:{current_minute:02d}")
        return

    # Check if we've already processed daily reports for this hour to prevent duplicates
    cache_key = f"daily_reports_sent_{current_time.strftime('%Y_%m_%d_%H_%M')}"
    
    if cache.get(cache_key):
        print(f"[Daily Report] Daily reports already sent for this time slot, skipping")
        return
    
    # Set cache to prevent duplicate sends for this specific time slot (65 minutes to be safe)
    cache.set(cache_key, True, 3900)  # 65 minutes cache
    
    print(f"[Daily Report] Processing {len(active_settings_for_now)} payment settings for time {current_hour:02d}:{current_minute:02d}")

    for settings_item in active_settings_for_now:
        partner = settings_item.partner
        print(f"[Daily Report Trigger] Matched deadline for Partner ID: {partner.id} at {settings_item.deadline_time.strftime('%H:%M')}")

        # Define the 24-hour window for revenue collection
        end_time_24hr_window = current_time # Report is up to this exact deadline time
        start_time_24hr_window = current_time - timezone.timedelta(hours=24)

        print(f"[Daily Report] Revenue window for Partner {partner.id}: {start_time_24hr_window.strftime('%Y-%m-%d %H:%M:%S')} to {end_time_24hr_window.strftime('%Y-%m-%d %H:%M:%S')}")

        # Get all active vehicles for this partner
        partner_active_vehicles_qs = Vehicle.objects.filter(
            partner=partner,
            status='active'
        ).select_related('driver__user')
        
        active_vehicles_for_partner = await sync_to_async(list)(partner_active_vehicles_qs)

        if not active_vehicles_for_partner:
            print(f"[Daily Report] No active vehicles found for Partner {partner.id}.")
            continue
            
        vehicle_report_data = []
        for vehicle_obj in active_vehicles_for_partner:
            # Sum payments for this vehicle in the last 24 hours
            # Assumes Revenue.created_at is a DateTimeField
            payment_in_last_24hrs = await sync_to_async(
                lambda: Revenue.objects.filter(
                    vehicle=vehicle_obj,
                    created_at__gte=start_time_24hr_window,
                    created_at__lt=end_time_24hr_window,
                    deleted=False
                ).aggregate(total_paid=Sum('amount'))['total_paid'] or 0.0
            )()

            expected_daily_amount = settings_item.daily_amount if settings_item.daily_amount is not None else 0.0
            
            # Determine payment status
            status_str = "✅ Paid" if payment_in_last_24hrs >= expected_daily_amount else "❌ Unsettled"
            if expected_daily_amount == 0 and payment_in_last_24hrs == 0: # Special case if 0 is expected and 0 paid
                 status_str = "ℹ️ No Target"

            driver_name = "N/A"
            if vehicle_obj.driver and vehicle_obj.driver.user:
                driver_name = f"{vehicle_obj.driver.user.first_name or ''} {vehicle_obj.driver.user.last_name or ''}".strip()
                if not driver_name: driver_name = vehicle_obj.driver.user.email # Fallback to email
            elif vehicle_obj.driver:
                driver_name = "Driver (No User Profile)"

            vehicle_report_data.append({
                'registration': vehicle_obj.registration_number,
                'driver': driver_name,
                'amount_paid_24hr': payment_in_last_24hrs,
                'expected_daily': expected_daily_amount,
                'status': status_str
            })
        
        if vehicle_report_data:
            await create_payment_reminder_notification(partner, vehicle_report_data)
        else:
            print(f"[Daily Report] No vehicle data to report for Partner {partner.id} (perhaps no active vehicles).")
    
    print(f"[Daily Report] Completed daily revenue report generation cycle for {len(active_settings_for_now)} partners")


async def generate_weekly_report(partner):
    """Generate weekly financial report and send immediately with mobile-responsive design"""
    recipient_email = None
    partner_name = 'Partner'

    # Safely access partner.user attributes, assuming user is pre-fetched
    if partner.user: # Check if user object exists
        if hasattr(partner.user, 'email') and partner.user.email:
            recipient_email = partner.user.email
        if hasattr(partner.user, 'first_name') and partner.user.first_name:
            partner_name = partner.user.first_name
    elif hasattr(partner, 'email') and partner.email: # Fallback to partner's direct email if no user
        recipient_email = partner.email

    if not recipient_email:
        print(f"[Weekly Report] Partner {partner.id} (Name: {partner_name if partner_name != 'Partner' else 'N/A'}) has no email, skipping report")
        return

    current_time = timezone.localtime()
    
    # Calculate past 1 week (7 days) from current trigger time
    end_datetime = current_time
    start_datetime = current_time - timezone.timedelta(days=7)
    
    print(f"[Weekly Report] Generating report for period: {start_datetime.strftime('%B %d, %Y %H:%M')} to {end_datetime.strftime('%B %d, %Y %H:%M')}")
    
    # Get only active vehicles for this partner
    def get_active_vehicles():
        return list(Vehicle.objects.filter(
            partner=partner,
            status='active'
        ).select_related('driver__user'))
    
    active_vehicles = await sync_to_async(get_active_vehicles)()
    
    if not active_vehicles:
        print(f"[Weekly Report] Partner {partner.id} ({partner_name}) has no active vehicles, skipping report")
        return
    
    vehicle_financial_data = []
    total_revenue = 0
    total_expenditure = 0
    total_profit = 0
    
    for vehicle in active_vehicles:
        # Get revenue for this vehicle for the week
        def get_vehicle_revenue():
            return Revenue.objects.filter(
                vehicle=vehicle,
                date__gte=start_datetime.date(),
                date__lt=end_datetime.date(),
                deleted=False
            ).aggregate(total_revenue=Sum('amount'))['total_revenue'] or 0
        
        vehicle_revenue = await sync_to_async(get_vehicle_revenue)()
        
        # Get expenditure for this vehicle for the week
        def get_vehicle_expenditure():
            return Expenditure.objects.filter(
                vehicle=vehicle,
                date__gte=start_datetime.date(),
                date__lt=end_datetime.date(),
                deleted=False
            ).aggregate(total_expenditure=Sum('amount'))['total_expenditure'] or 0
            
        vehicle_expenditure = await sync_to_async(get_vehicle_expenditure)()
        
        # Calculate profit
        vehicle_profit = vehicle_revenue - vehicle_expenditure
        
        # Get driver name
        driver_name = "No Driver"
        if vehicle.driver:
            if vehicle.driver.user: 
                driver_name = f"{vehicle.driver.user.first_name or ''} {vehicle.driver.user.last_name or ''}".strip()
                if not driver_name: 
                    driver_name = vehicle.driver.user.email or "Driver (User)"
            else: 
                driver_name = "Driver (No User Profile)" 
        
        vehicle_financial_data.append({
            'registration': vehicle.registration_number,
            'driver': driver_name,
            'revenue': vehicle_revenue,
            'expenditure': vehicle_expenditure,
            'profit': vehicle_profit
        })
        
        # Add to totals
        total_revenue += vehicle_revenue
        total_expenditure += vehicle_expenditure
        total_profit += vehicle_profit
    
    # Build vehicle rows for HTML table
    vehicle_rows = ""
    for vehicle_data in vehicle_financial_data:
        profit_color = "green" if vehicle_data['profit'] >= 0 else "red"
        
        vehicle_rows += f"""
        <tr>
            <td style="padding: 12px 8px; border-bottom: 1px solid #eee; font-size: 14px;">{vehicle_data['registration']}</td>
            <td style="padding: 12px 8px; border-bottom: 1px solid #eee; font-size: 14px;">{vehicle_data['driver']}</td>
            <td style="padding: 12px 8px; border-bottom: 1px solid #eee; text-align: right; font-size: 14px;">KES {format_currency(vehicle_data['revenue'])}</td>
            <td style="padding: 12px 8px; border-bottom: 1px solid #eee; text-align: right; font-size: 14px;">KES {format_currency(vehicle_data['expenditure'])}</td>
            <td style="padding: 12px 8px; border-bottom: 1px solid #eee; text-align: right; color: {profit_color}; font-size: 14px;"><strong>KES {format_currency(vehicle_data['profit'])}</strong></td>
        </tr>
        """
    
    # Build mobile-responsive HTML message
    html_message = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Weekly Financial Report</title>
        <style>
            @media only screen and (max-width: 600px) {{
                .container {{
                    padding: 10px !important;
                    margin: 0 !important;
                }}
                .table-responsive {{
                    overflow-x: auto;
                    -webkit-overflow-scrolling: touch;
                }}
                table {{
                    min-width: 500px;
                    font-size: 12px !important;
                }}
                th, td {{
                    padding: 8px 4px !important;
                    font-size: 12px !important;
                }}
                .summary-box {{
                    padding: 10px !important;
                    margin-top: 15px !important;
                }}
                h2 {{
                    font-size: 20px !important;
                }}
                p {{
                    font-size: 14px !important;
                }}
            }}
        </style>
    </head>
    <body style="font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f4f4f4; line-height: 1.6;">
        <div class="container" style="max-width: 700px; margin: auto; background-color: #ffffff; padding: 25px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <h2 style="color: #333333; border-bottom: 2px solid #007bff; padding-bottom: 10px; margin-bottom: 20px;">📊 Weekly Financial Report</h2>
            
            <p style="font-size: 16px; color: #555555; margin-bottom: 10px;">Hello {partner_name},</p>
            <p style="font-size: 16px; color: #555555; margin-bottom: 20px;">Here is your weekly financial summary for active vehicles from the past 7 days (<strong>{start_datetime.strftime('%B %d, %Y')}</strong> to <strong>{end_datetime.strftime('%B %d, %Y')}</strong>):</p>
            
            <div class="table-responsive" style="overflow-x: auto; margin-top: 20px;">
                <table style="width: 100%; border-collapse: collapse; font-size: 14px; background-color: #fff;">
                    <thead>
                        <tr style="background-color: #007bff; color: #ffffff;">
                            <th style="padding: 12px 10px; text-align: left; font-weight: bold;">Car Reg.</th>
                            <th style="padding: 12px 10px; text-align: left; font-weight: bold;">Driver Name</th>
                            <th style="padding: 12px 10px; text-align: right; font-weight: bold;">Revenue</th>
                            <th style="padding: 12px 10px; text-align: right; font-weight: bold;">Expenditure</th>
                            <th style="padding: 12px 10px; text-align: right; font-weight: bold;">Profit</th>
                        </tr>
                    </thead>
                    <tbody>
                        {vehicle_rows}
                    </tbody>
                </table>
            </div>
            
            <div class="summary-box" style="margin-top: 25px; padding: 20px; background-color: #f8f9fa; border-radius: 8px; border-left: 4px solid #007bff;">
                <h3 style="color: #333333; margin-top: 0; margin-bottom: 15px; font-size: 18px;">📈 Weekly Summary</h3>
                <div style="display: flex; flex-wrap: wrap; gap: 15px;">
                    <p style="margin: 5px 0; flex: 1; min-width: 200px;"><strong>Total Revenue:</strong> <span style="color: #28a745;">KES {format_currency(total_revenue)}</span></p>
                    <p style="margin: 5px 0; flex: 1; min-width: 200px;"><strong>Total Expenditure:</strong> <span style="color: #dc3545;">KES {format_currency(total_expenditure)}</span></p>
                    <p style="margin: 5px 0; flex: 1; min-width: 200px; color: {'#28a745' if total_profit >= 0 else '#dc3545'};"><strong>Total Profit:</strong> <strong>KES {format_currency(total_profit)}</strong></p>
                </div>
            </div>
            
            <div style="margin-top: 25px; padding: 15px; background-color: #e3f2fd; border-radius: 5px; border-left: 4px solid #2196f3;">
                <p style="font-size: 15px; color: #555555; margin: 0;">
                    📈 <strong>Note:</strong> Profit is calculated as Revenue minus Expenditure for each vehicle. This report covers your active vehicles only.
                </p>
            </div>
            
            <div style="margin-top: 30px; text-align: center; border-top: 1px solid #eee; padding-top: 20px;">
                <p style="font-size: 14px; color: #999999; margin: 0;">
                    Best regards,<br>
                    <strong style="color: #007bff;">KadereConnect Team</strong><br>
                    🌐 <a href="https://kadereconnect.co.ke" style="color: #007bff; text-decoration: none;">kadereconnect.co.ke</a>
                </p>
            </div>
        </div>
    </body>
    </html>
    """
    
    subject = "Weekly Financial Report"
    success, notification = await send_immediate_email(subject, html_message, recipient_email)
    
    if success:
        print(f"[Weekly Report] Successfully sent weekly financial report to {recipient_email}")
    else:
        print(f"[Weekly Report] Weekly financial report queued as pending for {recipient_email}")

async def check_weekly_reports():
    """
    Async function to check if weekly reports need to be sent
    This function should run periodically to determine when to generate weekly reports
    """
    current_time = timezone.localtime()
    
    # Send reports every Monday at 8:00 AM
    report_day = 'Monday'
    report_hour = 8
    report_minute = 00
    
    current_weekday = current_time.strftime('%A')
    current_hour = current_time.hour
    current_minute = current_time.minute
    
    print(f"[Weekly Report Check] Current: {current_weekday} {current_hour}:{current_minute:02d}, Target: {report_day} {report_hour}:{report_minute:02d}")
    
    # Only run if it's exactly the target time AND we haven't run in the last 55 minutes
    # This prevents multiple executions within the same minute
    if current_weekday == report_day and current_hour == report_hour and current_minute == report_minute:
        # Check if we've already sent reports recently (within last 55 minutes)
        cache_key = f"weekly_reports_sent_{current_time.strftime('%Y_%m_%d_%H')}"
        
        if cache.get(cache_key):
            print(f"[Weekly Report] Weekly reports already sent this hour, skipping")
            return
        
        print(f"[Weekly Report] Starting scheduled weekly financial report generation")
        
        # Set cache to prevent duplicate sends for 1 hour
        cache.set(cache_key, True, 3600)  # 1 hour cache
        
        # Get partners in batches to avoid memory issues
        batch_size = 10
        offset = 0
        
        while True:
            def get_partner_batch():
                return list(Partner.objects.filter(
                    vehicles__status='active'
                ).select_related('user').distinct()[offset:offset + batch_size])
            
            partner_batch = await sync_to_async(get_partner_batch)()
            
            if not partner_batch:
                break
                
            print(f"[Weekly Report] Processing batch of {len(partner_batch)} partners (offset: {offset})")
            
            # Process batch with limited concurrency
            semaphore = asyncio.Semaphore(3)  # Max 3 reports generating simultaneously
            
            async def process_partner_with_limit(partner):
                async with semaphore:
                    await generate_weekly_report(partner)
            
            tasks = [process_partner_with_limit(p) for p in partner_batch]
            await asyncio.gather(*tasks, return_exceptions=True)
            
            offset += batch_size
            
            # Small delay between batches to prevent overwhelming the system
            await asyncio.sleep(1)
        
        print("[Weekly Report] Completed weekly financial report generation cycle")
    else:
        print(f"[Weekly Report Check] Not the target time, skipping")

async def close_expired_job_listings():
    """
    Async function to automatically close job listings older than two weeks
    """
    print("[Job Expiry Check] Checking for job listings older than two weeks")
    
    # Calculate the date two weeks ago
    current_time = timezone.localtime()
    two_weeks_ago = current_time - timezone.timedelta(days=14)
    
    # Find all jobs that are open and older than two weeks
    def get_expired_jobs():
        return Job.objects.filter(
            created_at__lt=two_weeks_ago,
            status='open'  # Assuming 'open' is the status for active jobs
        )
    
    expired_jobs_qs = await sync_to_async(get_expired_jobs)()
    
    # Get count of expired jobs
    expired_jobs_count = await sync_to_async(lambda: expired_jobs_qs.count())()
    
    if expired_jobs_count == 0:
        print("[Job Expiry Check] No expired job listings found")
        return
        
    print(f"[Job Expiry Check] Found {expired_jobs_count} expired job listings to close")
    
    # Update all matching jobs in a single database query for efficiency
    await sync_to_async(lambda: expired_jobs_qs.update(
        status='closed',  # Set status to 'closed'
        closed_at=current_time  # Set the closed_at timestamp
    ))()
    
    print(f"[Job Expiry Check] Successfully closed {expired_jobs_count} expired job listings")

async def check_job_expiry():
    """Async function to check if job expiry needs to be processed - run this daily"""
    current_time = timezone.localtime()
    
    # Set the time for daily job expiry check (e.g., 2 AM)
    check_hour = 2
    check_minute = 0
    
    # Get current time components
    current_hour = current_time.hour
    current_minute = current_time.minute
    
    # Check if it's exactly the specified time
    if current_hour == check_hour and current_minute == check_minute:
        print(f"[Job Expiry Check] Starting scheduled job expiry check")
        await close_expired_job_listings()
    else:
        # For debugging - you can remove this line once everything is working
        # print(f"[Job Expiry Check] Current time {current_hour}:{current_minute:02d}, waiting for {check_hour}:{check_minute:02d}")
        pass

async def check_subscription_expiry():
    """
    Checks for subscriptions that are expiring in 7, 3, 1 days and when expired.
    Sends email notifications for each period.
    """
    print("[Subscription Expiry] Checking for subscription expiry notifications.")

    # Define notification periods (days before expiry and expired subscriptions)
    warning_periods = [7, 3, 1, 0]  # 7 days, 3 days, 1 day before expiry, and expired (0 days)

    current_date = timezone.now().date()

    for days_before in warning_periods:
        if days_before == 0:
            # Check for subscriptions that expired today
            target_date = current_date
            print(f"[Subscription Expiry] Checking for subscriptions that expired today ({target_date})")

            # Find subscriptions that expired today (status could be active or cancelled)
            expiring_subscriptions_qs = Subscription.objects.filter(
                expiry_date__date=target_date,
                status__in=['active', 'cancelled']  # Include both active and cancelled that expired today
            ).select_related('partner__user')
        else:
            # Check for subscriptions expiring in X days
            target_date = current_date + timezone.timedelta(days=days_before)
            print(f"[Subscription Expiry] Checking for subscriptions expiring on {target_date} ({days_before} days from now)")

            # Find active subscriptions expiring on the target date
            expiring_subscriptions_qs = Subscription.objects.filter(
                expiry_date__date=target_date,
                status='active'
            ).select_related('partner__user')

        expiring_subscriptions = await sync_to_async(list)(expiring_subscriptions_qs)

        if not expiring_subscriptions:
            print(f"[Subscription Expiry] No subscriptions {'expired today' if days_before == 0 else f'expiring in {days_before} days'}.")
            continue

        print(f"[Subscription Expiry] Found {len(expiring_subscriptions)} subscriptions {'expired today' if days_before == 0 else f'expiring in {days_before} days'}.")

        for subscription in expiring_subscriptions:
            await send_subscription_expiry_notification(subscription, days_before)


async def send_subscription_expiry_notification(subscription, days_before):
    """Send subscription expiry notification with appropriate urgency"""
    partner = subscription.partner
    partner_name = partner.user.first_name if partner.user and partner.user.first_name else "Partner"
    recipient_email = partner.user.email if partner.user and partner.user.email else partner.email

    if not recipient_email:
        print(f"[Subscription Expiry] Partner {partner.id} has no email. Skipping email notification.")
        return

    # Define the subject based on urgency
    if days_before == 0:
        notification_subject = "Your Subscription Has Expired!"
    elif days_before == 1:
        notification_subject = "URGENT: Your Subscription Expires Tomorrow!"
    elif days_before == 3:
        notification_subject = "Important: Your Subscription Expires in 3 Days"
    else:  # 7 days
        notification_subject = "Reminder: Your Subscription Expires in 1 Week"

    # Check if a notification has been sent recently to avoid duplicates
    @sync_to_async
    def has_recent_notification(email):
        one_day_ago = timezone.now() - timezone.timedelta(days=1)
        return Notification.objects.filter(
            recipient=email,
            subject=notification_subject,
            created_at__gte=one_day_ago
        ).exists()

    if await has_recent_notification(recipient_email):
        print(f"[Subscription Expiry] {days_before}-day notification already sent to {recipient_email} in the last 24 hours. Skipping.")
        return

    # Generate email content using existing template
    subject, html_content = get_subscription_expiry_template(
        partner_name=partner_name,
        plan_type=subscription.get_plan_type_display(),
        expiry_date=subscription.expiry_date,
        days_before=days_before
    )

    # Send email notification
    await send_immediate_email(subject, html_content, recipient_email)
    print(f"[Subscription Expiry] Sent {days_before}-day notification to {recipient_email}")


async def expire_subscriptions():
    """
    Finds active or cancelled subscriptions that have passed their expiry datetime and marks them as 'expired'.
    """
    print("[Subscription Status] Checking for expired subscriptions.")
    now = timezone.now()

    # Find active or cancelled subscriptions where the expiry datetime is in the past.
    expired_subscriptions_qs = Subscription.objects.filter(
        expiry_date__lt=now,
        status__in=['active', 'cancelled']
    )

    # Efficiently count and update without loading objects into memory
    count = await sync_to_async(expired_subscriptions_qs.count)()

    if count > 0:
        print(f"[Subscription Status] Found {count} subscriptions to mark as expired.")
        await sync_to_async(expired_subscriptions_qs.update)(status='expired')
        print(f"[Subscription Status] Successfully marked {count} subscriptions as expired.")
    else:
        print("[Subscription Status] No expired subscriptions found.")


async def check_for_expired_subscriptions():
    """
    Scheduled task that runs periodically to expire subscriptions.
    """
    print("[Subscription Status] Starting scheduled check for expired subscriptions.")
    await expire_subscriptions()


async def check_document_expiry():
    """
    Check for documents that are expiring soon and send notifications immediately.
    This function checks driver documents and vehicle documents.
    """
    from django.utils import timezone
    from datetime import timedelta

    print("[Document Expiry] Starting immediate document expiry check...")

    # Define notification periods:
    # - Positive numbers = days before expiry
    # - 0 = expiring today
    # - Negative numbers = days after expiry (expired)
    warning_periods = [ 7, 1, 0,-7]  # Before expiry, expiring today, and expired periods

    current_date = timezone.now().date()

    for days_offset in warning_periods:
        if days_offset == 0:
            target_date = current_date  # Check for documents expiring today
            days_before = 0  # Expiring today
            print(f"[Document Expiry] Checking for documents expiring today ({target_date})")
        elif days_offset < 0:
            # Negative values = expired documents
            target_date = current_date + timedelta(days=days_offset)  # Past dates
            days_before = 0  # Treat as expired for template purposes
            print(f"[Document Expiry] Checking for documents that expired on ({target_date})")
        else:
            # Positive values = future expiry dates
            target_date = current_date + timedelta(days=days_offset)
            days_before = days_offset
            print(f"[Document Expiry] Checking for documents expiring on {target_date} ({days_offset} days from now)")

        # Check driver documents
        await check_driver_document_expiry(target_date, days_before)

        # Check vehicle documents
        await check_vehicle_document_expiry(target_date, days_before)


async def check_driver_document_expiry(target_date, days_before):
    """Check driver document expiry and send notifications"""
    from .models import Driver
    from asgiref.sync import sync_to_async

    print(f"[Driver Documents] Checking for documents expiring in {days_before} days...")

    # Get drivers with expiring documents
    @sync_to_async
    def get_expiring_drivers():
        drivers = Driver.objects.filter(
            models.Q(license_expiry_date=target_date) |
            models.Q(psv_expiry_date=target_date) |
            models.Q(good_conduct_expiry_date=target_date)
        ).select_related('user', 'partner')
        return list(drivers)

    drivers = await get_expiring_drivers()

    for driver in drivers:
        expiring_docs = []

        # Check which documents are expiring
        if driver.license_expiry_date == target_date:
            expiring_docs.append(f"Driving License (expires: {driver.license_expiry_date})")
        if driver.psv_expiry_date == target_date:
            expiring_docs.append(f"PSV License (expires: {driver.psv_expiry_date})")
        if driver.good_conduct_expiry_date == target_date:
            expiring_docs.append(f"Good Conduct Certificate (expires: {driver.good_conduct_expiry_date})")

        if expiring_docs:
            await send_driver_expiry_notification(driver, expiring_docs, days_before)


async def check_vehicle_document_expiry(target_date, days_before):
    """Check vehicle document expiry and send notifications"""
    from .models import Vehicle
    from asgiref.sync import sync_to_async

    print(f"[Vehicle Documents] Checking for documents expiring in {days_before} days...")

    # Get vehicles with expiring documents
    @sync_to_async
    def get_expiring_vehicles():
        vehicles = Vehicle.objects.filter(
            # models.Q(vehicle_expiry_date=target_date) |
            models.Q(ntsa_expiry_date=target_date) |
            models.Q(insurance_expiry_date=target_date)
        ).select_related('partner', 'driver')
        return list(vehicles)

    vehicles = await get_expiring_vehicles()

    for vehicle in vehicles:
        expiring_docs = []

        # Check which documents are expiring
        # if vehicle.vehicle_expiry_date == target_date:
        #     expiring_docs.append(f"Vehicle Registration (expires: {vehicle.vehicle_expiry_date})")
        if vehicle.ntsa_expiry_date == target_date:
            expiring_docs.append(f"NTSA Inspection (expires: {vehicle.ntsa_expiry_date})")
        if vehicle.insurance_expiry_date == target_date:
            expiring_docs.append(f"Insurance (expires: {vehicle.insurance_expiry_date})")

        if expiring_docs:
            await send_vehicle_expiry_notification(vehicle, expiring_docs, days_before)


async def send_driver_expiry_notification(driver, expiring_docs, days_before):
    """Send expiry notification to driver and partner using HTML templates with duplicate prevention"""
    from .email_templates import get_driver_document_expiry_template
    from django.core.cache import cache
    from django.utils import timezone
    from asgiref.sync import sync_to_async

    # Create unique cache key for this driver and notification type
    current_date = timezone.now().date()
    cache_key = f"driver_expiry_notification_{driver.id}_{days_before}_{current_date}"

    # Check if we've already sent this notification today
    if cache.get(cache_key):
        print(f"[Driver Documents] Notification already sent to driver {driver.id} for {days_before} days before expiry today, skipping")
        return

    # Generate HTML email content using template
    subject, html_content = get_driver_document_expiry_template(driver, expiring_docs, days_before)

    # Send immediate notification to driver
    if driver.user and driver.user.email:
        await send_immediate_email(subject, html_content, driver.user.email)
        print(f"[Driver Documents] Sent notification to driver {driver.user.email}")

    # Get partner information safely using sync_to_async
    @sync_to_async
    def get_partner_info():
        try:
            if driver.partner and driver.partner.user and driver.partner.user.email:
                # Check if driver has a partner (avoid sending notifications to drivers who have partners)
                driver_has_partner = hasattr(driver.partner, 'partner') and driver.partner.partner
                return {
                    'email': driver.partner.user.email,
                    'first_name': driver.partner.first_name,
                    'last_name': driver.partner.last_name,
                    'has_partner': driver_has_partner
                }
        except Exception as e:
            print(f"[Driver Documents] Error getting partner info: {e}")
        return None

    partner_info = await get_partner_info()

    # Send notification to partner if driver has one and doesn't have a partner themselves
    if partner_info and not partner_info['has_partner']:
        # Create partner-specific subject
        partner_subject = f"Driver Alert - {subject}"

        # Use the same template but customize for partner context
        partner_html = html_content.replace(
            f"Dear {driver.first_name} {driver.last_name},",
            f"Dear {partner_info['first_name']} {partner_info['last_name']},<br><br>"
            f"This is a notification that your driver <strong>{driver.first_name} {driver.last_name}</strong> has documents requiring attention:"
        ).replace(
            "Log in to your KadeReconnect account",
            "Ensure your driver logs in to their KadeReconnect account"
        ).replace(
            "/driverdashboard",
            "/partnerdashboard"
        )

        await send_immediate_email(partner_subject, partner_html, partner_info['email'])
        print(f"[Driver Documents] Sent notification to partner {partner_info['email']}")

    # Set cache to prevent duplicate notifications for 24 hours
    cache.set(cache_key, True, 86400)  # 24 hours cache


async def send_vehicle_expiry_notification(vehicle, expiring_docs, days_before):
    """Send expiry notification to partner and driver using HTML templates with duplicate prevention"""
    from .email_templates import get_vehicle_document_expiry_template
    from django.core.cache import cache
    from django.utils import timezone
    from asgiref.sync import sync_to_async

    # Create unique cache key for this vehicle and notification type
    current_date = timezone.now().date()
    cache_key = f"vehicle_expiry_notification_{vehicle.id}_{days_before}_{current_date}"

    # Check if we've already sent this notification today
    if cache.get(cache_key):
        print(f"[Vehicle Documents] Notification already sent for vehicle {vehicle.id} for {days_before} days before expiry today, skipping")
        return

    # Get partner information safely using sync_to_async
    @sync_to_async
    def get_partner_info():
        try:
            if vehicle.partner and vehicle.partner.user and vehicle.partner.user.email:
                return {
                    'email': vehicle.partner.user.email,
                    'exists': True
                }
        except Exception as e:
            print(f"[Vehicle Documents] Error getting partner info: {e}")
        return {'exists': False}

    # Get driver information safely using sync_to_async
    @sync_to_async
    def get_driver_info():
        try:
            if vehicle.driver and vehicle.driver.user and vehicle.driver.user.email:
                # Check if driver has a partner (avoid sending notifications to drivers who have partners)
                driver_has_partner = hasattr(vehicle.driver, 'partner') and vehicle.driver.partner
                return {
                    'email': vehicle.driver.user.email,
                    'exists': True,
                    'has_partner': driver_has_partner
                }
        except Exception as e:
            print(f"[Vehicle Documents] Error getting driver info: {e}")
        return {'exists': False, 'has_partner': False}

    partner_info = await get_partner_info()
    driver_info = await get_driver_info()

    # Send immediate notification to partner (primary recipient)
    if partner_info['exists']:
        partner_subject, partner_html = get_vehicle_document_expiry_template(
            vehicle, expiring_docs, days_before, recipient_type="partner"
        )
        await send_immediate_email(partner_subject, partner_html, partner_info['email'])
        print(f"[Vehicle Documents] Sent notification to partner {partner_info['email']}")

    # Send notification to driver if vehicle is assigned to one and driver doesn't have a partner
    # (avoid sending notifications to drivers who have partners as per memory)
    if driver_info['exists'] and not driver_info['has_partner']:
        driver_subject, driver_html = get_vehicle_document_expiry_template(
            vehicle, expiring_docs, days_before, recipient_type="driver"
        )
        await send_immediate_email(f"Vehicle Alert - {driver_subject}", driver_html, driver_info['email'])
        print(f"[Vehicle Documents] Sent notification to driver {driver_info['email']}")

    # Set cache to prevent duplicate notifications for 24 hours
    cache.set(cache_key, True, 86400)  # 24 hours cache


async def create_notification(subject, recipient_email, message):
    """Create a notification record"""
    from .models import Notification
    from asgiref.sync import sync_to_async
    from django.utils import timezone

    # Check if notification was already sent recently to avoid duplicates
    @sync_to_async
    def has_recent_notification():
        one_day_ago = timezone.now() - timezone.timedelta(days=1)
        return Notification.objects.filter(
            recipient=recipient_email,
            subject=subject,
            created_at__gte=one_day_ago
        ).exists()

    if await has_recent_notification():
        print(f"[Document Expiry] Notification already sent to {recipient_email} in the last 24 hours. Skipping.")
        return

    # Create notification
    @sync_to_async
    def create_notification_record():
        return Notification.objects.create(
            subject=subject,
            type="email",
            recipient=recipient_email,
            status="pending",
            message=message
        )

    notification = await create_notification_record()
    print(f"[Document Expiry] Created notification {notification.id} for {recipient_email}")


async def check_document_expiry_schedule():
    """
    Scheduled task that runs document expiry checks daily at 4:00 AM.
    Prevents duplicate notifications by checking time and cache.
    """
    from django.utils import timezone
    from django.core.cache import cache

    current_time = timezone.localtime()

    # Set the time for daily document expiry check (8:00 AM)
    check_hour = 8
    check_minute = 00

    # Get current time components
    current_hour = current_time.hour
    current_minute = current_time.minute

    # Check if it's exactly the specified time
    if current_hour == check_hour and current_minute == check_minute:
        # Check if we've already run today to prevent duplicates
        cache_key = f"document_expiry_check_{current_time.strftime('%Y_%m_%d')}"

        if cache.get(cache_key):
            print(f"[Document Expiry] Document expiry check already completed today, skipping")
            return

        print(f"[Document Expiry] Starting scheduled daily document expiry check at {current_time.strftime('%H:%M:%S')}")

        # Set cache to prevent duplicate runs for 23 hours
        cache.set(cache_key, True, 82800)  # 23 hours cache

        await check_document_expiry()

        print(f"[Document Expiry] Daily document expiry check completed at {timezone.localtime().strftime('%H:%M:%S')}")
    else:
        # For debugging - you can remove this line once everything is working
        # print(f"[Document Expiry] Current time {current_hour}:{current_minute:02d}, waiting for {check_hour}:{check_minute:02d}")
        pass

