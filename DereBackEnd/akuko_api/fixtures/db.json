[{"model": "akuko_api.workarea", "pk": 1, "fields": {"name": "Nairobi"}}, {"model": "akuko_api.workarea", "pk": 2, "fields": {"name": "Mombasa"}}, {"model": "akuko_api.workarea", "pk": 3, "fields": {"name": "<PERSON><PERSON><PERSON>"}}, {"model": "akuko_api.workarea", "pk": 4, "fields": {"name": "<PERSON><PERSON><PERSON>"}}, {"model": "akuko_api.workarea", "pk": 5, "fields": {"name": "Eldoret"}}, {"model": "akuko_api.workarea", "pk": 6, "fields": {"name": "<PERSON><PERSON><PERSON>"}}, {"model": "akuko_api.workarea", "pk": 7, "fields": {"name": "<PERSON><PERSON><PERSON>"}}, {"model": "akuko_api.workarea", "pk": 8, "fields": {"name": "Embu"}}, {"model": "akuko_api.workarea", "pk": 9, "fields": {"name": "Kitale"}}, {"model": "akuko_api.workarea", "pk": 10, "fields": {"name": "Malindi"}}, {"model": "akuko_api.workarea", "pk": 11, "fields": {"name": "Kakamega"}}, {"model": "akuko_api.workarea", "pk": 12, "fields": {"name": "Bungoma"}}, {"model": "akuko_api.workarea", "pk": 13, "fields": {"name": "Marsabit"}}, {"model": "akuko_api.workarea", "pk": 14, "fields": {"name": "Isiolo"}}, {"model": "akuko_api.workarea", "pk": 15, "fields": {"name": "<PERSON><PERSON><PERSON>"}}, {"model": "akuko_api.workarea", "pk": 16, "fields": {"name": "<PERSON><PERSON><PERSON>"}}, {"model": "akuko_api.workarea", "pk": 17, "fields": {"name": "<PERSON><PERSON><PERSON>"}}, {"model": "akuko_api.workarea", "pk": 18, "fields": {"name": "<PERSON><PERSON><PERSON>"}}, {"model": "akuko_api.workarea", "pk": 19, "fields": {"name": "<PERSON><PERSON><PERSON>"}}, {"model": "akuko_api.workarea", "pk": 20, "fields": {"name": "Laikipia"}}, {"model": "akuko_api.workarea", "pk": 21, "fields": {"name": "<PERSON><PERSON>"}}, {"model": "akuko_api.workarea", "pk": 22, "fields": {"name": "Bungoma"}}, {"model": "akuko_api.workarea", "pk": 23, "fields": {"name": "Busia"}}, {"model": "akuko_api.workarea", "pk": 24, "fields": {"name": "Elgeyo-Marakwet"}}, {"model": "akuko_api.workarea", "pk": 25, "fields": {"name": "Homa Bay"}}, {"model": "akuko_api.workarea", "pk": 26, "fields": {"name": "<PERSON><PERSON><PERSON>"}}, {"model": "akuko_api.workarea", "pk": 27, "fields": {"name": "<PERSON><PERSON><PERSON>"}}, {"model": "akuko_api.workarea", "pk": 28, "fields": {"name": "Kit<PERSON>"}}, {"model": "akuko_api.workarea", "pk": 29, "fields": {"name": "<PERSON><PERSON><PERSON>"}}, {"model": "akuko_api.workarea", "pk": 30, "fields": {"name": "Laikipia"}}, {"model": "akuko_api.workarea", "pk": 31, "fields": {"name": "<PERSON><PERSON>"}}, {"model": "akuko_api.workarea", "pk": 32, "fields": {"name": "<PERSON><PERSON><PERSON>"}}, {"model": "akuko_api.workarea", "pk": 33, "fields": {"name": "<PERSON><PERSON><PERSON>"}}, {"model": "akuko_api.workarea", "pk": 34, "fields": {"name": "Marsabit"}}, {"model": "akuko_api.workarea", "pk": 35, "fields": {"name": "<PERSON><PERSON>"}}, {"model": "akuko_api.workarea", "pk": 36, "fields": {"name": "<PERSON><PERSON><PERSON>"}}, {"model": "akuko_api.workarea", "pk": 37, "fields": {"name": "Mombasa"}}, {"model": "akuko_api.workarea", "pk": 38, "fields": {"name": "Nairobi"}}, {"model": "akuko_api.workarea", "pk": 39, "fields": {"name": "<PERSON><PERSON><PERSON>"}}, {"model": "akuko_api.workarea", "pk": 40, "fields": {"name": "<PERSON><PERSON>"}}, {"model": "akuko_api.workarea", "pk": 41, "fields": {"name": "<PERSON><PERSON>"}}, {"model": "akuko_api.workarea", "pk": 42, "fields": {"name": "<PERSON><PERSON><PERSON>"}}, {"model": "akuko_api.workarea", "pk": 43, "fields": {"name": "<PERSON><PERSON><PERSON><PERSON>"}}, {"model": "akuko_api.workarea", "pk": 44, "fields": {"name": "<PERSON><PERSON><PERSON>"}}, {"model": "akuko_api.workarea", "pk": 45, "fields": {"name": "Samburu"}}, {"model": "akuko_api.workarea", "pk": 46, "fields": {"name": "<PERSON><PERSON>"}}, {"model": "akuko_api.workarea", "pk": 47, "fields": {"name": "Taita<PERSON><PERSON><PERSON><PERSON>"}}, {"model": "akuko_api.workarea", "pk": 48, "fields": {"name": "Tana River"}}, {"model": "akuko_api.workarea", "pk": 49, "fields": {"name": "<PERSON><PERSON><PERSON>"}}, {"model": "akuko_api.workarea", "pk": 50, "fields": {"name": "Trans Nzoia"}}, {"model": "akuko_api.workarea", "pk": 51, "fields": {"name": "Turkana"}}, {"model": "akuko_api.workarea", "pk": 52, "fields": {"name": "<PERSON><PERSON><PERSON>"}}, {"model": "akuko_api.workarea", "pk": 53, "fields": {"name": "Vihiga"}}, {"model": "akuko_api.workarea", "pk": 54, "fields": {"name": "West Pokot"}}, {"model": "akuko_api.vehiclemake", "pk": 1, "fields": {"name": "Toyota"}}, {"model": "akuko_api.vehiclemake", "pk": 2, "fields": {"name": "Mazda"}}, {"model": "akuko_api.vehiclemake", "pk": 3, "fields": {"name": "Honda"}}, {"model": "akuko_api.vehiclemake", "pk": 4, "fields": {"name": "Nissan"}}, {"model": "akuko_api.vehiclemake", "pk": 5, "fields": {"name": "<PERSON><PERSON>"}}, {"model": "akuko_api.vehiclemake", "pk": 6, "fields": {"name": "Hyundai"}}, {"model": "akuko_api.vehiclemake", "pk": 7, "fields": {"name": "Volkswagen"}}, {"model": "akuko_api.vehiclemake", "pk": 8, "fields": {"name": "Suzuki"}}, {"model": "akuko_api.vehiclemake", "pk": 9, "fields": {"name": "Isuzu"}}, {"model": "akuko_api.vehiclemake", "pk": 10, "fields": {"name": "Volvo"}}, {"model": "akuko_api.vehiclemake", "pk": 11, "fields": {"name": "<PERSON><PERSON><PERSON><PERSON>"}}, {"model": "akuko_api.vehiclemake", "pk": 12, "fields": {"name": "Peugeot"}}, {"model": "akuko_api.vehiclemake", "pk": 13, "fields": {"name": "Audi"}}, {"model": "akuko_api.vehiclemake", "pk": 14, "fields": {"name": "Subaru"}}, {"model": "akuko_api.vehiclemake", "pk": 15, "fields": {"name": "Renault"}}, {"model": "akuko_api.vehiclemake", "pk": 16, "fields": {"name": "YAMAHA"}}, {"model": "akuko_api.vehiclemake", "pk": 17, "fields": {"name": "Mercedes"}}, {"model": "akuko_api.vehiclemake", "pk": 18, "fields": {"name": "BMW"}}, {"model": "akuko_api.vehiclemake", "pk": 19, "fields": {"name": "Chevrolet"}}, {"model": "akuko_api.vehiclemake", "pk": 20, "fields": {"name": "<PERSON><PERSON><PERSON>"}}, {"model": "akuko_api.vehiclemake", "pk": 21, "fields": {"name": "Fiat"}}, {"model": "akuko_api.vehiclemake", "pk": 22, "fields": {"name": "Ford"}}, {"model": "akuko_api.vehiclemake", "pk": 23, "fields": {"name": "Mini"}}, {"model": "akuko_api.vehiclemake", "pk": 24, "fields": {"name": "Landrover"}}, {"model": "akuko_api.vehiclemake", "pk": 25, "fields": {"name": "Jaguar"}}, {"model": "akuko_api.vehiclemake", "pk": 26, "fields": {"name": "Jeep"}}, {"model": "akuko_api.vehiclemake", "pk": 27, "fields": {"name": "<PERSON>us"}}, {"model": "akuko_api.vehiclemake", "pk": 28, "fields": {"name": "Aston Martin"}}, {"model": "akuko_api.vehiclemake", "pk": 29, "fields": {"name": "Ssangyong"}}, {"model": "akuko_api.vehiclemake", "pk": 30, "fields": {"name": "<PERSON><PERSON><PERSON>"}}, {"model": "akuko_api.vehiclemake", "pk": 31, "fields": {"name": "Citreon"}}, {"model": "akuko_api.vehiclemake", "pk": 32, "fields": {"name": "<PERSON><PERSON>"}}, {"model": "akuko_api.vehiclemake", "pk": 33, "fields": {"name": "<PERSON><PERSON>"}}, {"model": "akuko_api.vehiclemake", "pk": 34, "fields": {"name": "Ferrari"}}, {"model": "akuko_api.vehiclemake", "pk": 35, "fields": {"name": "Alfa Romeo"}}, {"model": "akuko_api.vehiclemake", "pk": 36, "fields": {"name": "Dodge"}}, {"model": "akuko_api.vehiclemake", "pk": 37, "fields": {"name": "Chrysler"}}, {"model": "akuko_api.vehiclemake", "pk": 38, "fields": {"name": "Scania"}}, {"model": "akuko_api.vehiclemake", "pk": 39, "fields": {"name": "MAN"}}, {"model": "akuko_api.vehiclemodel", "pk": 1, "fields": {"name": "corolla", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 2, "fields": {"name": "camry", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 3, "fields": {"name": "hilux", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 4, "fields": {"name": "rav4", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 5, "fields": {"name": "vitz", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 6, "fields": {"name": "fortuner", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 7, "fields": {"name": "alphard", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 8, "fields": {"name": "aqua", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 9, "fields": {"name": "auris", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 10, "fields": {"name": "au<PERSON> van", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 11, "fields": {"name": "avensis", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 12, "fields": {"name": "aygo", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 13, "fields": {"name": "aygo x", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 14, "fields": {"name": "c-hr", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 15, "fields": {"name": "celica", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 16, "fields": {"name": "corolla cross", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 17, "fields": {"name": "corolla verso", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 18, "fields": {"name": "corona", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 19, "fields": {"name": "crown", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 20, "fields": {"name": "dyna", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 21, "fields": {"name": "dyna 100 pick-up", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 22, "fields": {"name": "estima", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 23, "fields": {"name": "gr86", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 24, "fields": {"name": "gt86", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 25, "fields": {"name": "harrier", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 26, "fields": {"name": "<PERSON><PERSON>", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 27, "fields": {"name": "highlander", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 28, "fields": {"name": "kluger", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 29, "fields": {"name": "landcruiser", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 30, "fields": {"name": "levin", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 31, "fields": {"name": "mrs", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 32, "fields": {"name": "passo", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 33, "fields": {"name": "prius", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 34, "fields": {"name": "prius alpha", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 35, "fields": {"name": "prius+", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 36, "fields": {"name": "proace", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 37, "fields": {"name": "proace city", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 38, "fields": {"name": "proace combi", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 39, "fields": {"name": "proace verso", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 40, "fields": {"name": "sienta", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 41, "fields": {"name": "starlet", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 42, "fields": {"name": "urban cruiser", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 43, "fields": {"name": "vellfire", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 44, "fields": {"name": "verso", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 45, "fields": {"name": "verso-s", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 46, "fields": {"name": "voxy", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 47, "fields": {"name": "wish", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 48, "fields": {"name": "yaris", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 49, "fields": {"name": "yaris cross", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 50, "fields": {"name": "bz4x", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 51, "fields": {"name": "iq", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 52, "fields": {"name": "Coaster", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 53, "fields": {"name": "HiAce", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 54, "fields": {"name": "<PERSON><PERSON>", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 55, "fields": {"name": "Dyna Bus", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 56, "fields": {"name": "Dyna Truck", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 57, "fields": {"name": "Hino 300", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 58, "fields": {"name": "Hino 500", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 59, "fields": {"name": "Hino 700", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 60, "fields": {"name": "ToyoAce", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 61, "fields": {"name": "ProAce", "make": 1}}, {"model": "akuko_api.vehiclemodel", "pk": 62, "fields": {"name": "cx-5", "make": 2}}, {"model": "akuko_api.vehiclemodel", "pk": 63, "fields": {"name": "<PERSON>la", "make": 2}}, {"model": "akuko_api.vehiclemodel", "pk": 64, "fields": {"name": "atenza", "make": 2}}, {"model": "akuko_api.vehiclemodel", "pk": 65, "fields": {"name": "demio", "make": 2}}, {"model": "akuko_api.vehiclemodel", "pk": 66, "fields": {"name": "cx-3", "make": 2}}, {"model": "akuko_api.vehiclemodel", "pk": 67, "fields": {"name": "cx-30", "make": 2}}, {"model": "akuko_api.vehiclemodel", "pk": 68, "fields": {"name": "cx-60", "make": 2}}, {"model": "akuko_api.vehiclemodel", "pk": 69, "fields": {"name": "cx-80", "make": 2}}, {"model": "akuko_api.vehiclemodel", "pk": 70, "fields": {"name": "mx-30", "make": 2}}, {"model": "akuko_api.vehiclemodel", "pk": 71, "fields": {"name": "mx-5", "make": 2}}, {"model": "akuko_api.vehiclemodel", "pk": 72, "fields": {"name": "mazda2", "make": 2}}, {"model": "akuko_api.vehiclemodel", "pk": 73, "fields": {"name": "mazda3", "make": 2}}, {"model": "akuko_api.vehiclemodel", "pk": 74, "fields": {"name": "mazda5", "make": 2}}, {"model": "akuko_api.vehiclemodel", "pk": 75, "fields": {"name": "mazda6", "make": 2}}, {"model": "akuko_api.vehiclemodel", "pk": 76, "fields": {"name": "rx-7", "make": 2}}, {"model": "akuko_api.vehiclemodel", "pk": 77, "fields": {"name": "Mazda Parkway", "make": 2}}, {"model": "akuko_api.vehiclemodel", "pk": 78, "fields": {"name": "Bongo Bus", "make": 2}}, {"model": "akuko_api.vehiclemodel", "pk": 79, "fields": {"name": "Titan Dump", "make": 2}}, {"model": "akuko_api.vehiclemodel", "pk": 80, "fields": {"name": "Bongo Truck", "make": 2}}, {"model": "akuko_api.vehiclemodel", "pk": 81, "fields": {"name": "Titan Truck", "make": 2}}, {"model": "akuko_api.vehiclemodel", "pk": 82, "fields": {"name": "Scrum Truck", "make": 2}}, {"model": "akuko_api.vehiclemodel", "pk": 83, "fields": {"name": "E-Series Bus", "make": 2}}, {"model": "akuko_api.vehiclemodel", "pk": 84, "fields": {"name": "cr-v", "make": 3}}, {"model": "akuko_api.vehiclemodel", "pk": 85, "fields": {"name": "civic", "make": 3}}, {"model": "akuko_api.vehiclemodel", "pk": 86, "fields": {"name": "hr-v", "make": 3}}, {"model": "akuko_api.vehiclemodel", "pk": 87, "fields": {"name": "fit", "make": 3}}, {"model": "akuko_api.vehiclemodel", "pk": 88, "fields": {"name": "accord", "make": 3}}, {"model": "akuko_api.vehiclemodel", "pk": 89, "fields": {"name": "cbr", "make": 3}}, {"model": "akuko_api.vehiclemodel", "pk": 90, "fields": {"name": "cmx", "make": 3}}, {"model": "akuko_api.vehiclemodel", "pk": 91, "fields": {"name": "cr-z", "make": 3}}, {"model": "akuko_api.vehiclemodel", "pk": 92, "fields": {"name": "e:ny1", "make": 3}}, {"model": "akuko_api.vehiclemodel", "pk": 93, "fields": {"name": "freed", "make": 3}}, {"model": "akuko_api.vehiclemodel", "pk": 94, "fields": {"name": "grace", "make": 3}}, {"model": "akuko_api.vehiclemodel", "pk": 95, "fields": {"name": "insight", "make": 3}}, {"model": "akuko_api.vehiclemodel", "pk": 96, "fields": {"name": "jade", "make": 3}}, {"model": "akuko_api.vehiclemodel", "pk": 97, "fields": {"name": "jazz", "make": 3}}, {"model": "akuko_api.vehiclemodel", "pk": 98, "fields": {"name": "legend", "make": 3}}, {"model": "akuko_api.vehiclemodel", "pk": 99, "fields": {"name": "nt", "make": 3}}, {"model": "akuko_api.vehiclemodel", "pk": 100, "fields": {"name": "odyssey", "make": 3}}, {"model": "akuko_api.vehiclemodel", "pk": 101, "fields": {"name": "s2000", "make": 3}}, {"model": "akuko_api.vehiclemodel", "pk": 102, "fields": {"name": "shuttle", "make": 3}}, {"model": "akuko_api.vehiclemodel", "pk": 103, "fields": {"name": "stepwagon", "make": 3}}, {"model": "akuko_api.vehiclemodel", "pk": 104, "fields": {"name": "stream", "make": 3}}, {"model": "akuko_api.vehiclemodel", "pk": 105, "fields": {"name": "vezel", "make": 3}}, {"model": "akuko_api.vehiclemodel", "pk": 106, "fields": {"name": "zr-v", "make": 3}}, {"model": "akuko_api.vehiclemodel", "pk": 107, "fields": {"name": "e", "make": 3}}, {"model": "akuko_api.vehiclemodel", "pk": 108, "fields": {"name": "Honda Acty Truck", "make": 3}}, {"model": "akuko_api.vehiclemodel", "pk": 109, "fields": {"name": "Vamos", "make": 3}}, {"model": "akuko_api.vehiclemodel", "pk": 110, "fields": {"name": " <PERSON><PERSON>", "make": 3}}, {"model": "akuko_api.vehiclemodel", "pk": 111, "fields": {"name": "Mobilio", "make": 3}}, {"model": "akuko_api.vehiclemodel", "pk": 112, "fields": {"name": "Vamos Hobio", "make": 3}}, {"model": "akuko_api.vehiclemodel", "pk": 113, "fields": {"name": "march", "make": 4}}, {"model": "akuko_api.vehiclemodel", "pk": 114, "fields": {"name": "altima", "make": 4}}, {"model": "akuko_api.vehiclemodel", "pk": 115, "fields": {"name": "rogue", "make": 4}}, {"model": "akuko_api.vehiclemodel", "pk": 116, "fields": {"name": "navara", "make": 4}}, {"model": "akuko_api.vehiclemodel", "pk": 117, "fields": {"name": "note", "make": 4}}, {"model": "akuko_api.vehiclemodel", "pk": 118, "fields": {"name": "300 zx", "make": 4}}, {"model": "akuko_api.vehiclemodel", "pk": 119, "fields": {"name": "350z", "make": 4}}, {"model": "akuko_api.vehiclemodel", "pk": 120, "fields": {"name": "almera", "make": 4}}, {"model": "akuko_api.vehiclemodel", "pk": 121, "fields": {"name": "ariya", "make": 4}}, {"model": "akuko_api.vehiclemodel", "pk": 122, "fields": {"name": "cabstar", "make": 4}}, {"model": "akuko_api.vehiclemodel", "pk": 123, "fields": {"name": "cube", "make": 4}}, {"model": "akuko_api.vehiclemodel", "pk": 124, "fields": {"name": "figaro", "make": 4}}, {"model": "akuko_api.vehiclemodel", "pk": 125, "fields": {"name": "gt-r", "make": 4}}, {"model": "akuko_api.vehiclemodel", "pk": 126, "fields": {"name": "interstar", "make": 4}}, {"model": "akuko_api.vehiclemodel", "pk": 127, "fields": {"name": "juke", "make": 4}}, {"model": "akuko_api.vehiclemodel", "pk": 128, "fields": {"name": "leaf", "make": 4}}, {"model": "akuko_api.vehiclemodel", "pk": 129, "fields": {"name": "micra", "make": 4}}, {"model": "akuko_api.vehiclemodel", "pk": 130, "fields": {"name": "murano", "make": 4}}, {"model": "akuko_api.vehiclemodel", "pk": 131, "fields": {"name": "nt400 cabstar", "make": 4}}, {"model": "akuko_api.vehiclemodel", "pk": 132, "fields": {"name": "nv200", "make": 4}}, {"model": "akuko_api.vehiclemodel", "pk": 133, "fields": {"name": "nv300", "make": 4}}, {"model": "akuko_api.vehiclemodel", "pk": 134, "fields": {"name": "nv300 combi", "make": 4}}, {"model": "akuko_api.vehiclemodel", "pk": 135, "fields": {"name": "nv400", "make": 4}}, {"model": "akuko_api.vehiclemodel", "pk": 136, "fields": {"name": "pathfinder", "make": 4}}, {"model": "akuko_api.vehiclemodel", "pk": 137, "fields": {"name": "president", "make": 4}}, {"model": "akuko_api.vehiclemodel", "pk": 138, "fields": {"name": "primastar", "make": 4}}, {"model": "akuko_api.vehiclemodel", "pk": 139, "fields": {"name": "pulsar", "make": 4}}, {"model": "akuko_api.vehiclemodel", "pk": 140, "fields": {"name": "qx", "make": 4}}, {"model": "akuko_api.vehiclemodel", "pk": 141, "fields": {"name": "qash<PERSON>i", "make": 4}}, {"model": "akuko_api.vehiclemodel", "pk": 142, "fields": {"name": "qashqai +2", "make": 4}}, {"model": "akuko_api.vehiclemodel", "pk": 143, "fields": {"name": "serena", "make": 4}}, {"model": "akuko_api.vehiclemodel", "pk": 144, "fields": {"name": "tiida", "make": 4}}, {"model": "akuko_api.vehiclemodel", "pk": 145, "fields": {"name": "townstar", "make": 4}}, {"model": "akuko_api.vehiclemodel", "pk": 146, "fields": {"name": "vanette", "make": 4}}, {"model": "akuko_api.vehiclemodel", "pk": 147, "fields": {"name": "x-trail", "make": 4}}, {"model": "akuko_api.vehiclemodel", "pk": 148, "fields": {"name": "x-trail van", "make": 4}}, {"model": "akuko_api.vehiclemodel", "pk": 149, "fields": {"name": "Nissan Civilian", "make": 4}}, {"model": "akuko_api.vehiclemodel", "pk": 150, "fields": {"name": "Atlas", "make": 4}}, {"model": "akuko_api.vehiclemodel", "pk": 151, "fields": {"name": "Condor", "make": 4}}, {"model": "akuko_api.vehiclemodel", "pk": 152, "fields": {"name": "UD Truck", "make": 4}}, {"model": "akuko_api.vehiclemodel", "pk": 153, "fields": {"name": "Diesel Big Thumb", "make": 4}}, {"model": "akuko_api.vehiclemodel", "pk": 154, "fields": {"name": "Diesel Quon", "make": 4}}, {"model": "akuko_api.vehiclemodel", "pk": 155, "fields": {"name": "Diesel Resona", "make": 4}}, {"model": "akuko_api.vehiclemodel", "pk": 156, "fields": {"name": "Eco-T", "make": 4}}, {"model": "akuko_api.vehiclemodel", "pk": 157, "fields": {"name": "carens", "make": 5}}, {"model": "akuko_api.vehiclemodel", "pk": 158, "fields": {"name": "ceed", "make": 5}}, {"model": "akuko_api.vehiclemodel", "pk": 159, "fields": {"name": "cerato", "make": 5}}, {"model": "akuko_api.vehiclemodel", "pk": 160, "fields": {"name": "ev3", "make": 5}}, {"model": "akuko_api.vehiclemodel", "pk": 161, "fields": {"name": "ev6", "make": 5}}, {"model": "akuko_api.vehiclemodel", "pk": 162, "fields": {"name": "ev9", "make": 5}}, {"model": "akuko_api.vehiclemodel", "pk": 163, "fields": {"name": "niro", "make": 5}}, {"model": "akuko_api.vehiclemodel", "pk": 164, "fields": {"name": "optima", "make": 5}}, {"model": "akuko_api.vehiclemodel", "pk": 165, "fields": {"name": "rio", "make": 5}}, {"model": "akuko_api.vehiclemodel", "pk": 166, "fields": {"name": "sedona", "make": 5}}, {"model": "akuko_api.vehiclemodel", "pk": 167, "fields": {"name": "sorento", "make": 5}}, {"model": "akuko_api.vehiclemodel", "pk": 168, "fields": {"name": "soul", "make": 5}}, {"model": "akuko_api.vehiclemodel", "pk": 169, "fields": {"name": "stinger", "make": 5}}, {"model": "akuko_api.vehiclemodel", "pk": 170, "fields": {"name": "stonic", "make": 5}}, {"model": "akuko_api.vehiclemodel", "pk": 171, "fields": {"name": "venga", "make": 5}}, {"model": "akuko_api.vehiclemodel", "pk": 172, "fields": {"name": "xceed", "make": 5}}, {"model": "akuko_api.vehiclemodel", "pk": 173, "fields": {"name": "e-niro", "make": 5}}, {"model": "akuko_api.vehiclemodel", "pk": 174, "fields": {"name": "e-soul", "make": 5}}, {"model": "akuko_api.vehiclemodel", "pk": 175, "fields": {"name": "pro_ceed", "make": 5}}, {"model": "akuko_api.vehiclemodel", "pk": 176, "fields": {"name": "KIA Granbird", "make": 5}}, {"model": "akuko_api.vehiclemodel", "pk": 177, "fields": {"name": "Comb<PERSON>", "make": 5}}, {"model": "akuko_api.vehiclemodel", "pk": 178, "fields": {"name": "Rhino", "make": 5}}, {"model": "akuko_api.vehiclemodel", "pk": 179, "fields": {"name": "Frontier", "make": 5}}, {"model": "akuko_api.vehiclemodel", "pk": 180, "fields": {"name": "Trade", "make": 5}}, {"model": "akuko_api.vehiclemodel", "pk": 181, "fields": {"name": "AM928", "make": 5}}, {"model": "akuko_api.vehiclemodel", "pk": 182, "fields": {"name": "AM939", "make": 5}}, {"model": "akuko_api.vehiclemodel", "pk": 183, "fields": {"name": "RENAULT ALPINE", "make": 6}}, {"model": "akuko_api.vehiclemodel", "pk": 184, "fields": {"name": "RENAULT ARKANA", "make": 6}}, {"model": "akuko_api.vehiclemodel", "pk": 186, "fields": {"name": "RENAULT AUSTRAL", "make": 6}}, {"model": "akuko_api.vehiclemodel", "pk": 187, "fields": {"name": "RENAULT C", "make": 6}}, {"model": "akuko_api.vehiclemodel", "pk": 188, "fields": {"name": "RENAULT CAPTUR", "make": 6}}, {"model": "akuko_api.vehiclemodel", "pk": 189, "fields": {"name": "RENAULT CLIO", "make": 6}}, {"model": "akuko_api.vehiclemodel", "pk": 190, "fields": {"name": "RENAULT FLUENCE", "make": 6}}, {"model": "akuko_api.vehiclemodel", "pk": 191, "fields": {"name": "RENAULT GRAND MEGANE", "make": 6}}, {"model": "akuko_api.vehiclemodel", "pk": 192, "fields": {"name": "RENAULT GRAND SCENIC", "make": 6}}, {"model": "akuko_api.vehiclemodel", "pk": 193, "fields": {"name": "RENAULT KADJAR", "make": 6}}, {"model": "akuko_api.vehiclemodel", "pk": 194, "fields": {"name": "RENAULT KANGOO", "make": 6}}, {"model": "akuko_api.vehiclemodel", "pk": 195, "fields": {"name": "RENAULT KOLEOS", "make": 6}}, {"model": "akuko_api.vehiclemodel", "pk": 196, "fields": {"name": "RENAULT LAGUNA", "make": 6}}, {"model": "akuko_api.vehiclemodel", "pk": 197, "fields": {"name": "RENAULT MASTER", "make": 6}}, {"model": "akuko_api.vehiclemodel", "pk": 198, "fields": {"name": "RENAULT MASTER PASSENGER", "make": 6}}, {"model": "akuko_api.vehiclemodel", "pk": 199, "fields": {"name": "RENAULT MEGANE", "make": 6}}, {"model": "akuko_api.vehiclemodel", "pk": 200, "fields": {"name": "RENAULT MEGANE E-TECH", "make": 6}}, {"model": "akuko_api.vehiclemodel", "pk": 201, "fields": {"name": "RENAULT MIDLUM", "make": 6}}, {"model": "akuko_api.vehiclemodel", "pk": 202, "fields": {"name": "RENAULT PREMIUM", "make": 6}}, {"model": "akuko_api.vehiclemodel", "pk": 203, "fields": {"name": "RENAULT KERAX", "make": 6}}, {"model": "akuko_api.vehiclemodel", "pk": 204, "fields": {"name": "RENAULT D SERIES", "make": 6}}, {"model": "akuko_api.vehiclemodel", "pk": 205, "fields": {"name": "RENAULT MASTER", "make": 6}}, {"model": "akuko_api.vehiclemodel", "pk": 206, "fields": {"name": "RENAULT MAGNUM", "make": 6}}, {"model": "akuko_api.vehiclemodel", "pk": 207, "fields": {"name": "RENAULT T SERIES", "make": 6}}, {"model": "akuko_api.vehiclemodel", "pk": 208, "fields": {"name": "RENAULT RANGE C", "make": 6}}, {"model": "akuko_api.vehiclemodel", "pk": 209, "fields": {"name": "RENAULT RANGE D", "make": 6}}, {"model": "akuko_api.vehiclemodel", "pk": 210, "fields": {"name": "amarok", "make": 7}}, {"model": "akuko_api.vehiclemodel", "pk": 211, "fields": {"name": "arteon", "make": 7}}, {"model": "akuko_api.vehiclemodel", "pk": 212, "fields": {"name": "beetle", "make": 7}}, {"model": "akuko_api.vehiclemodel", "pk": 213, "fields": {"name": "cc", "make": 7}}, {"model": "akuko_api.vehiclemodel", "pk": 214, "fields": {"name": "caddy", "make": 7}}, {"model": "akuko_api.vehiclemodel", "pk": 215, "fields": {"name": "caddy maxi", "make": 7}}, {"model": "akuko_api.vehiclemodel", "pk": 216, "fields": {"name": "caddy maxi life", "make": 7}}, {"model": "akuko_api.vehiclemodel", "pk": 217, "fields": {"name": "california", "make": 7}}, {"model": "akuko_api.vehiclemodel", "pk": 218, "fields": {"name": "caravelle", "make": 7}}, {"model": "akuko_api.vehiclemodel", "pk": 219, "fields": {"name": "crafter", "make": 7}}, {"model": "akuko_api.vehiclemodel", "pk": 220, "fields": {"name": "e-golf", "make": 7}}, {"model": "akuko_api.vehiclemodel", "pk": 221, "fields": {"name": "fox", "make": 7}}, {"model": "akuko_api.vehiclemodel", "pk": 222, "fields": {"name": "golf", "make": 7}}, {"model": "akuko_api.vehiclemodel", "pk": 223, "fields": {"name": "golf plus", "make": 7}}, {"model": "akuko_api.vehiclemodel", "pk": 224, "fields": {"name": "golf sv", "make": 7}}, {"model": "akuko_api.vehiclemodel", "pk": 225, "fields": {"name": "id.3", "make": 7}}, {"model": "akuko_api.vehiclemodel", "pk": 226, "fields": {"name": "id.4", "make": 7}}, {"model": "akuko_api.vehiclemodel", "pk": 227, "fields": {"name": "id.5", "make": 7}}, {"model": "akuko_api.vehiclemodel", "pk": 228, "fields": {"name": "id.7", "make": 7}}, {"model": "akuko_api.vehiclemodel", "pk": 229, "fields": {"name": "id.buzz", "make": 7}}, {"model": "akuko_api.vehiclemodel", "pk": 230, "fields": {"name": "jetta", "make": 7}}, {"model": "akuko_api.vehiclemodel", "pk": 231, "fields": {"name": "multivan", "make": 7}}, {"model": "akuko_api.vehiclemodel", "pk": 232, "fields": {"name": "passat", "make": 7}}, {"model": "akuko_api.vehiclemodel", "pk": 233, "fields": {"name": "passat cc", "make": 7}}, {"model": "akuko_api.vehiclemodel", "pk": 234, "fields": {"name": "s<PERSON><PERSON><PERSON>", "make": 7}}, {"model": "akuko_api.vehiclemodel", "pk": 235, "fields": {"name": "sharan", "make": 7}}, {"model": "akuko_api.vehiclemodel", "pk": 236, "fields": {"name": "t-cross", "make": 7}}, {"model": "akuko_api.vehiclemodel", "pk": 237, "fields": {"name": "t-roc", "make": 7}}, {"model": "akuko_api.vehiclemodel", "pk": 238, "fields": {"name": "t5", "make": 7}}, {"model": "akuko_api.vehiclemodel", "pk": 239, "fields": {"name": "taigo", "make": 7}}, {"model": "akuko_api.vehiclemodel", "pk": 240, "fields": {"name": "tiguan allspace", "make": 7}}, {"model": "akuko_api.vehiclemodel", "pk": 241, "fields": {"name": "to<PERSON><PERSON><PERSON>", "make": 7}}, {"model": "akuko_api.vehiclemodel", "pk": 242, "fields": {"name": "touran", "make": 7}}, {"model": "akuko_api.vehiclemodel", "pk": 243, "fields": {"name": "transporter", "make": 7}}, {"model": "akuko_api.vehiclemodel", "pk": 244, "fields": {"name": "transporter kombi", "make": 7}}, {"model": "akuko_api.vehiclemodel", "pk": 245, "fields": {"name": "e-transporter", "make": 7}}, {"model": "akuko_api.vehiclemodel", "pk": 246, "fields": {"name": "e-up!", "make": 7}}, {"model": "akuko_api.vehiclemodel", "pk": 247, "fields": {"name": "up!", "make": 7}}, {"model": "akuko_api.vehiclemodel", "pk": 248, "fields": {"name": "CRAFTER", "make": 7}}, {"model": "akuko_api.vehiclemodel", "pk": 249, "fields": {"name": " LT", "make": 7}}, {"model": "akuko_api.vehiclemodel", "pk": 250, "fields": {"name": "alto", "make": 8}}, {"model": "akuko_api.vehiclemodel", "pk": 251, "fields": {"name": "baleno", "make": 8}}, {"model": "akuko_api.vehiclemodel", "pk": 252, "fields": {"name": "carry", "make": 8}}, {"model": "akuko_api.vehiclemodel", "pk": 253, "fields": {"name": "celerio", "make": 8}}, {"model": "akuko_api.vehiclemodel", "pk": 254, "fields": {"name": "grand vitara", "make": 8}}, {"model": "akuko_api.vehiclemodel", "pk": 255, "fields": {"name": "ignis", "make": 8}}, {"model": "akuko_api.vehiclemodel", "pk": 256, "fields": {"name": "jimny", "make": 8}}, {"model": "akuko_api.vehiclemodel", "pk": 257, "fields": {"name": "s-cross", "make": 8}}, {"model": "akuko_api.vehiclemodel", "pk": 258, "fields": {"name": "sx4", "make": 8}}, {"model": "akuko_api.vehiclemodel", "pk": 259, "fields": {"name": "sx4 s-cross", "make": 8}}, {"model": "akuko_api.vehiclemodel", "pk": 260, "fields": {"name": "splash", "make": 8}}, {"model": "akuko_api.vehiclemodel", "pk": 261, "fields": {"name": "swace", "make": 8}}, {"model": "akuko_api.vehiclemodel", "pk": 262, "fields": {"name": "swift", "make": 8}}, {"model": "akuko_api.vehiclemodel", "pk": 263, "fields": {"name": "vitara", "make": 8}}, {"model": "akuko_api.vehiclemodel", "pk": 264, "fields": {"name": "Every", "make": 8}}, {"model": "akuko_api.vehiclemodel", "pk": 265, "fields": {"name": "Super Carry", "make": 8}}, {"model": "akuko_api.vehiclemodel", "pk": 266, "fields": {"name": "APV minibus", "make": 8}}, {"model": "akuko_api.vehiclemodel", "pk": 267, "fields": {"name": "Bighorn", "make": 9}}, {"model": "akuko_api.vehiclemodel", "pk": 268, "fields": {"name": "D-Max", "make": 9}}, {"model": "akuko_api.vehiclemodel", "pk": 269, "fields": {"name": "MU", "make": 9}}, {"model": "akuko_api.vehiclemodel", "pk": 270, "fields": {"name": "N-Series", "make": 9}}, {"model": "akuko_api.vehiclemodel", "pk": 271, "fields": {"name": "NNR", "make": 9}}, {"model": "akuko_api.vehiclemodel", "pk": 272, "fields": {"name": "NPR", "make": 9}}, {"model": "akuko_api.vehiclemodel", "pk": 273, "fields": {"name": "NQR", "make": 9}}, {"model": "akuko_api.vehiclemodel", "pk": 274, "fields": {"name": "<PERSON><PERSON>", "make": 9}}, {"model": "akuko_api.vehiclemodel", "pk": 275, "fields": {"name": "Isuzu NQR", "make": 9}}, {"model": "akuko_api.vehiclemodel", "pk": 276, "fields": {"name": "Isuzu NPR", "make": 9}}, {"model": "akuko_api.vehiclemodel", "pk": 277, "fields": {"name": "Isuzu FSR", "make": 9}}, {"model": "akuko_api.vehiclemodel", "pk": 278, "fields": {"name": "Isuzu FVR", "make": 9}}, {"model": "akuko_api.vehiclemodel", "pk": 279, "fields": {"name": "Isuzu GIGA", "make": 9}}, {"model": "akuko_api.vehiclemodel", "pk": 280, "fields": {"name": "Isuzu ELF", "make": 9}}, {"model": "akuko_api.vehiclemodel", "pk": 281, "fields": {"name": "Isuzu CYZ", "make": 9}}, {"model": "akuko_api.vehiclemodel", "pk": 282, "fields": {"name": "Isuzu Forward", "make": 9}}, {"model": "akuko_api.vehiclemodel", "pk": 283, "fields": {"name": "Isuzu Bus LT", "make": 9}}, {"model": "akuko_api.vehiclemodel", "pk": 284, "fields": {"name": "Isuzu C-Series", "make": 9}}, {"model": "akuko_api.vehiclemodel", "pk": 285, "fields": {"name": "C30", "make": 10}}, {"model": "akuko_api.vehiclemodel", "pk": 286, "fields": {"name": "C40", "make": 10}}, {"model": "akuko_api.vehiclemodel", "pk": 287, "fields": {"name": "EX30", "make": 10}}, {"model": "akuko_api.vehiclemodel", "pk": 288, "fields": {"name": "EX90", "make": 10}}, {"model": "akuko_api.vehiclemodel", "pk": 289, "fields": {"name": "S80", "make": 10}}, {"model": "akuko_api.vehiclemodel", "pk": 290, "fields": {"name": "V40", "make": 10}}, {"model": "akuko_api.vehiclemodel", "pk": 291, "fields": {"name": "V40 Cross Country", "make": 10}}, {"model": "akuko_api.vehiclemodel", "pk": 292, "fields": {"name": "V50", "make": 10}}, {"model": "akuko_api.vehiclemodel", "pk": 293, "fields": {"name": "V60 Cross Country", "make": 10}}, {"model": "akuko_api.vehiclemodel", "pk": 294, "fields": {"name": "V70", "make": 10}}, {"model": "akuko_api.vehiclemodel", "pk": 295, "fields": {"name": "V90", "make": 10}}, {"model": "akuko_api.vehiclemodel", "pk": 296, "fields": {"name": "XC40", "make": 10}}, {"model": "akuko_api.vehiclemodel", "pk": 297, "fields": {"name": "XC70", "make": 10}}, {"model": "akuko_api.vehiclemodel", "pk": 298, "fields": {"name": "XC90", "make": 10}}, {"model": "akuko_api.vehiclemodel", "pk": 299, "fields": {"name": "Volvo FH", "make": 10}}, {"model": "akuko_api.vehiclemodel", "pk": 300, "fields": {"name": "Volvo FM", "make": 10}}, {"model": "akuko_api.vehiclemodel", "pk": 301, "fields": {"name": "Volvo FL", "make": 10}}, {"model": "akuko_api.vehiclemodel", "pk": 302, "fields": {"name": "Volvo FMX", "make": 10}}, {"model": "akuko_api.vehiclemodel", "pk": 303, "fields": {"name": "Volvo B11R", "make": 10}}, {"model": "akuko_api.vehiclemodel", "pk": 304, "fields": {"name": "Volvo B8R", "make": 10}}, {"model": "akuko_api.vehiclemodel", "pk": 305, "fields": {"name": "Volvo B7R", "make": 10}}, {"model": "akuko_api.vehiclemodel", "pk": 306, "fields": {"name": "Volvo 9700", "make": 10}}, {"model": "akuko_api.vehiclemodel", "pk": 307, "fields": {"name": "Volvo 9900", "make": 10}}, {"model": "akuko_api.vehiclemodel", "pk": 308, "fields": {"name": "<PERSON>nt", "make": 11}}, {"model": "akuko_api.vehiclemodel", "pk": 309, "fields": {"name": "Lancer", "make": 11}}, {"model": "akuko_api.vehiclemodel", "pk": 310, "fields": {"name": "RVR", "make": 11}}, {"model": "akuko_api.vehiclemodel", "pk": 311, "fields": {"name": "SUV", "make": 11}}, {"model": "akuko_api.vehiclemodel", "pk": 312, "fields": {"name": "Triton", "make": 11}}, {"model": "akuko_api.vehiclemodel", "pk": 313, "fields": {"name": "Delica", "make": 11}}, {"model": "akuko_api.vehiclemodel", "pk": 314, "fields": {"name": "Colt", "make": 11}}, {"model": "akuko_api.vehiclemodel", "pk": 315, "fields": {"name": "Pajero", "make": 11}}, {"model": "akuko_api.vehiclemodel", "pk": 316, "fields": {"name": "Mirage", "make": 11}}, {"model": "akuko_api.vehiclemodel", "pk": 317, "fields": {"name": "ASX", "make": 11}}, {"model": "akuko_api.vehiclemodel", "pk": 318, "fields": {"name": "Can<PERSON>", "make": 11}}, {"model": "akuko_api.vehiclemodel", "pk": 319, "fields": {"name": "Eclipse Cross", "make": 11}}, {"model": "akuko_api.vehiclemodel", "pk": 320, "fields": {"name": "L200", "make": 11}}, {"model": "akuko_api.vehiclemodel", "pk": 321, "fields": {"name": "Minica", "make": 11}}, {"model": "akuko_api.vehiclemodel", "pk": 322, "fields": {"name": "Outlander", "make": 11}}, {"model": "akuko_api.vehiclemodel", "pk": 323, "fields": {"name": "Shogun", "make": 11}}, {"model": "akuko_api.vehiclemodel", "pk": 324, "fields": {"name": "Space Star", "make": 11}}, {"model": "akuko_api.vehiclemodel", "pk": 325, "fields": {"name": "<PERSON><PERSON>", "make": 11}}, {"model": "akuko_api.vehiclemodel", "pk": 326, "fields": {"name": "Fuso Fighter", "make": 11}}, {"model": "akuko_api.vehiclemodel", "pk": 327, "fields": {"name": "<PERSON>", "make": 11}}, {"model": "akuko_api.vehiclemodel", "pk": 328, "fields": {"name": "Fuso Super Great", "make": 11}}, {"model": "akuko_api.vehiclemodel", "pk": 329, "fields": {"name": "Fuso Aero Star", "make": 11}}, {"model": "akuko_api.vehiclemodel", "pk": 330, "fields": {"name": "Fuso Aero Queen", "make": 11}}, {"model": "akuko_api.vehiclemodel", "pk": 331, "fields": {"name": "107", "make": 12}}, {"model": "akuko_api.vehiclemodel", "pk": 332, "fields": {"name": "108", "make": 12}}, {"model": "akuko_api.vehiclemodel", "pk": 334, "fields": {"name": "2008", "make": 12}}, {"model": "akuko_api.vehiclemodel", "pk": 335, "fields": {"name": "206", "make": 12}}, {"model": "akuko_api.vehiclemodel", "pk": 336, "fields": {"name": "207", "make": 12}}, {"model": "akuko_api.vehiclemodel", "pk": 337, "fields": {"name": "208", "make": 12}}, {"model": "akuko_api.vehiclemodel", "pk": 338, "fields": {"name": "3008", "make": 12}}, {"model": "akuko_api.vehiclemodel", "pk": 339, "fields": {"name": "307", "make": 12}}, {"model": "akuko_api.vehiclemodel", "pk": 340, "fields": {"name": "308", "make": 12}}, {"model": "akuko_api.vehiclemodel", "pk": 341, "fields": {"name": "407", "make": 12}}, {"model": "akuko_api.vehiclemodel", "pk": 342, "fields": {"name": "408", "make": 12}}, {"model": "akuko_api.vehiclemodel", "pk": 343, "fields": {"name": "5008", "make": 12}}, {"model": "akuko_api.vehiclemodel", "pk": 344, "fields": {"name": "508", "make": 12}}, {"model": "akuko_api.vehiclemodel", "pk": 345, "fields": {"name": "<PERSON><PERSON><PERSON>", "make": 12}}, {"model": "akuko_api.vehiclemodel", "pk": 346, "fields": {"name": "Boxer", "make": 12}}, {"model": "akuko_api.vehiclemodel", "pk": 347, "fields": {"name": "<PERSON><PERSON>", "make": 12}}, {"model": "akuko_api.vehiclemodel", "pk": 348, "fields": {"name": "E-Rifter", "make": 12}}, {"model": "akuko_api.vehiclemodel", "pk": 349, "fields": {"name": "Expert", "make": 12}}, {"model": "akuko_api.vehiclemodel", "pk": 350, "fields": {"name": "Partner", "make": 12}}, {"model": "akuko_api.vehiclemodel", "pk": 351, "fields": {"name": "Partner Tepee", "make": 12}}, {"model": "akuko_api.vehiclemodel", "pk": 352, "fields": {"name": "RCZ", "make": 12}}, {"model": "akuko_api.vehiclemodel", "pk": 353, "fields": {"name": "<PERSON><PERSON><PERSON>", "make": 12}}, {"model": "akuko_api.vehiclemodel", "pk": 354, "fields": {"name": "Traveller", "make": 12}}, {"model": "akuko_api.vehiclemodel", "pk": 355, "fields": {"name": "E-Partner", "make": 12}}, {"model": "akuko_api.vehiclemodel", "pk": 356, "fields": {"name": "Peugeot J9", "make": 12}}, {"model": "akuko_api.vehiclemodel", "pk": 357, "fields": {"name": "Peugeot Expert", "make": 12}}, {"model": "akuko_api.vehiclemodel", "pk": 358, "fields": {"name": "Peugeot P4", "make": 12}}, {"model": "akuko_api.vehiclemodel", "pk": 359, "fields": {"name": "Peugeot D4B", "make": 12}}, {"model": "akuko_api.vehiclemodel", "pk": 360, "fields": {"name": "80", "make": 13}}, {"model": "akuko_api.vehiclemodel", "pk": 361, "fields": {"name": "90", "make": 13}}, {"model": "akuko_api.vehiclemodel", "pk": 362, "fields": {"name": "A1", "make": 13}}, {"model": "akuko_api.vehiclemodel", "pk": 363, "fields": {"name": "A3 Saloon", "make": 13}}, {"model": "akuko_api.vehiclemodel", "pk": 364, "fields": {"name": "A3 Sportback", "make": 13}}, {"model": "akuko_api.vehiclemodel", "pk": 365, "fields": {"name": "A5", "make": 13}}, {"model": "akuko_api.vehiclemodel", "pk": 366, "fields": {"name": "A5 Sportback", "make": 13}}, {"model": "akuko_api.vehiclemodel", "pk": 367, "fields": {"name": "A7", "make": 13}}, {"model": "akuko_api.vehiclemodel", "pk": 368, "fields": {"name": "A8", "make": 13}}, {"model": "akuko_api.vehiclemodel", "pk": 369, "fields": {"name": "Allroad", "make": 13}}, {"model": "akuko_api.vehiclemodel", "pk": 370, "fields": {"name": "Coupe", "make": 13}}, {"model": "akuko_api.vehiclemodel", "pk": 371, "fields": {"name": "Q2", "make": 13}}, {"model": "akuko_api.vehiclemodel", "pk": 372, "fields": {"name": "Q4 E-Tron", "make": 13}}, {"model": "akuko_api.vehiclemodel", "pk": 373, "fields": {"name": "Q6 E-Tron", "make": 13}}, {"model": "akuko_api.vehiclemodel", "pk": 374, "fields": {"name": "Q8 E-Tron", "make": 13}}, {"model": "akuko_api.vehiclemodel", "pk": 375, "fields": {"name": "R8", "make": 13}}, {"model": "akuko_api.vehiclemodel", "pk": 376, "fields": {"name": "RS Q3", "make": 13}}, {"model": "akuko_api.vehiclemodel", "pk": 377, "fields": {"name": "RS4", "make": 13}}, {"model": "akuko_api.vehiclemodel", "pk": 378, "fields": {"name": "RS6", "make": 13}}, {"model": "akuko_api.vehiclemodel", "pk": 379, "fields": {"name": "RS7", "make": 13}}, {"model": "akuko_api.vehiclemodel", "pk": 380, "fields": {"name": "RSQ8", "make": 13}}, {"model": "akuko_api.vehiclemodel", "pk": 381, "fields": {"name": "S1", "make": 13}}, {"model": "akuko_api.vehiclemodel", "pk": 382, "fields": {"name": "S3", "make": 13}}, {"model": "akuko_api.vehiclemodel", "pk": 383, "fields": {"name": "S4", "make": 13}}, {"model": "akuko_api.vehiclemodel", "pk": 384, "fields": {"name": "S5", "make": 13}}, {"model": "akuko_api.vehiclemodel", "pk": 385, "fields": {"name": "S6", "make": 13}}, {"model": "akuko_api.vehiclemodel", "pk": 386, "fields": {"name": "S7", "make": 13}}, {"model": "akuko_api.vehiclemodel", "pk": 387, "fields": {"name": "S8", "make": 13}}, {"model": "akuko_api.vehiclemodel", "pk": 388, "fields": {"name": "SQ5", "make": 13}}, {"model": "akuko_api.vehiclemodel", "pk": 389, "fields": {"name": "SQ7", "make": 13}}, {"model": "akuko_api.vehiclemodel", "pk": 390, "fields": {"name": "E-Tron", "make": 13}}, {"model": "akuko_api.vehiclemodel", "pk": 391, "fields": {"name": "E-Tron GT Quattro", "make": 13}}, {"model": "akuko_api.vehiclemodel", "pk": 392, "fields": {"name": "A1 Sportback", "make": 13}}, {"model": "akuko_api.vehiclemodel", "pk": 393, "fields": {"name": "<PERSON><PERSON>", "make": 14}}, {"model": "akuko_api.vehiclemodel", "pk": 394, "fields": {"name": "Outback", "make": 14}}, {"model": "akuko_api.vehiclemodel", "pk": 395, "fields": {"name": "Legacy", "make": 14}}, {"model": "akuko_api.vehiclemodel", "pk": 396, "fields": {"name": "XV", "make": 14}}, {"model": "akuko_api.vehiclemodel", "pk": 397, "fields": {"name": "BRZ", "make": 14}}, {"model": "akuko_api.vehiclemodel", "pk": 398, "fields": {"name": "WRX", "make": 14}}, {"model": "akuko_api.vehiclemodel", "pk": 399, "fields": {"name": "1600", "make": 14}}, {"model": "akuko_api.vehiclemodel", "pk": 400, "fields": {"name": "Sambar", "make": 14}}, {"model": "akuko_api.vehiclemodel", "pk": 401, "fields": {"name": "Solterra", "make": 14}}, {"model": "akuko_api.vehiclemodel", "pk": 402, "fields": {"name": "Levorg", "make": 14}}, {"model": "akuko_api.vehiclemodel", "pk": 403, "fields": {"name": "<PERSON><PERSON>rez<PERSON>", "make": 14}}, {"model": "akuko_api.vehiclemodel", "pk": 404, "fields": {"name": "Tucson", "make": 15}}, {"model": "akuko_api.vehiclemodel", "pk": 405, "fields": {"name": "Accent", "make": 15}}, {"model": "akuko_api.vehiclemodel", "pk": 406, "fields": {"name": "Santa Fe", "make": 15}}, {"model": "akuko_api.vehiclemodel", "pk": 407, "fields": {"name": "Sonata", "make": 15}}, {"model": "akuko_api.vehiclemodel", "pk": 408, "fields": {"name": "Creta", "make": 15}}, {"model": "akuko_api.vehiclemodel", "pk": 409, "fields": {"name": "<PERSON><PERSON>", "make": 15}}, {"model": "akuko_api.vehiclemodel", "pk": 500, "fields": {"name": "i10", "make": 15}}, {"model": "akuko_api.vehiclemodel", "pk": 501, "fields": {"name": "<PERSON><PERSON>", "make": 15}}, {"model": "akuko_api.vehiclemodel", "pk": 502, "fields": {"name": "Bayon", "make": 15}}, {"model": "akuko_api.vehiclemodel", "pk": 503, "fields": {"name": "Coupe", "make": 15}}, {"model": "akuko_api.vehiclemodel", "pk": 504, "fields": {"name": "Ioniq", "make": 15}}, {"model": "akuko_api.vehiclemodel", "pk": 505, "fields": {"name": "Ioniq 5", "make": 15}}, {"model": "akuko_api.vehiclemodel", "pk": 506, "fields": {"name": "Ioniq 6", "make": 15}}, {"model": "akuko_api.vehiclemodel", "pk": 507, "fields": {"name": "Staria", "make": 15}}, {"model": "akuko_api.vehiclemodel", "pk": 508, "fields": {"name": "<PERSON><PERSON><PERSON>", "make": 15}}, {"model": "akuko_api.vehiclemodel", "pk": 509, "fields": {"name": "i20", "make": 15}}, {"model": "akuko_api.vehiclemodel", "pk": 600, "fields": {"name": "i30", "make": 15}}, {"model": "akuko_api.vehiclemodel", "pk": 601, "fields": {"name": "i40", "make": 15}}, {"model": "akuko_api.vehiclemodel", "pk": 602, "fields": {"name": "ix20", "make": 15}}, {"model": "akuko_api.vehiclemodel", "pk": 603, "fields": {"name": "ix35", "make": 15}}, {"model": "akuko_api.vehiclemodel", "pk": 604, "fields": {"name": "Elantra", "make": 15}}, {"model": "akuko_api.vehiclemodel", "pk": 605, "fields": {"name": "Hyundai County", "make": 15}}, {"model": "akuko_api.vehiclemodel", "pk": 606, "fields": {"name": "Universe", "make": 15}}, {"model": "akuko_api.vehiclemodel", "pk": 607, "fields": {"name": "Aero City", "make": 15}}, {"model": "akuko_api.vehiclemodel", "pk": 608, "fields": {"name": " Mighty", "make": 15}}, {"model": "akuko_api.vehiclemodel", "pk": 609, "fields": {"name": " Xcient", "make": 15}}, {"model": "akuko_api.vehiclemodel", "pk": 610, "fields": {"name": "HD78", "make": 15}}, {"model": "akuko_api.vehiclemodel", "pk": 611, "fields": {"name": "HD65", "make": 15}}, {"model": "akuko_api.vehiclemodel", "pk": 612, "fields": {"name": "Super Aero City", "make": 15}}, {"model": "akuko_api.vehiclemodel", "pk": 613, "fields": {"name": "YZF-R3", "make": 16}}, {"model": "akuko_api.vehiclemodel", "pk": 614, "fields": {"name": "MT-07", "make": 16}}, {"model": "akuko_api.vehiclemodel", "pk": 615, "fields": {"name": "MT-09", "make": 16}}, {"model": "akuko_api.vehiclemodel", "pk": 616, "fields": {"name": "FZ6", "make": 16}}, {"model": "akuko_api.vehiclemodel", "pk": 617, "fields": {"name": "Tenere 700", "make": 16}}, {"model": "akuko_api.vehiclemodel", "pk": 618, "fields": {"name": "XSR700", "make": 16}}, {"model": "akuko_api.vehiclemodel", "pk": 619, "fields": {"name": "XSR900", "make": 16}}, {"model": "akuko_api.vehiclemodel", "pk": 620, "fields": {"name": "FZS 1800 Supercharged", "make": 16}}, {"model": "akuko_api.vehiclemodel", "pk": 621, "fields": {"name": "MT-10", "make": 16}}, {"model": "akuko_api.vehiclemodel", "pk": 622, "fields": {"name": "MT-03", "make": 16}}, {"model": "akuko_api.vehiclemodel", "pk": 623, "fields": {"name": "MWT-9", "make": 16}}, {"model": "akuko_api.vehiclemodel", "pk": 624, "fields": {"name": "TW200", "make": 16}}, {"model": "akuko_api.vehiclemodel", "pk": 625, "fields": {"name": "Tricity 300", "make": 16}}, {"model": "akuko_api.vehiclemodel", "pk": 626, "fields": {"name": "180", "make": 17}}, {"model": "akuko_api.vehiclemodel", "pk": 627, "fields": {"name": "190", "make": 17}}, {"model": "akuko_api.vehiclemodel", "pk": 628, "fields": {"name": "220", "make": 17}}, {"model": "akuko_api.vehiclemodel", "pk": 629, "fields": {"name": "300", "make": 17}}, {"model": "akuko_api.vehiclemodel", "pk": 630, "fields": {"name": "320", "make": 17}}, {"model": "akuko_api.vehiclemodel", "pk": 631, "fields": {"name": "450", "make": 17}}, {"model": "akuko_api.vehiclemodel", "pk": 632, "fields": {"name": "500", "make": 17}}, {"model": "akuko_api.vehiclemodel", "pk": 633, "fields": {"name": "560", "make": 17}}, {"model": "akuko_api.vehiclemodel", "pk": 634, "fields": {"name": "600", "make": 17}}, {"model": "akuko_api.vehiclemodel", "pk": 635, "fields": {"name": "A Class", "make": 17}}, {"model": "akuko_api.vehiclemodel", "pk": 636, "fields": {"name": "AMG", "make": 17}}, {"model": "akuko_api.vehiclemodel", "pk": 637, "fields": {"name": "Atego", "make": 17}}, {"model": "akuko_api.vehiclemodel", "pk": 638, "fields": {"name": "B Class", "make": 17}}, {"model": "akuko_api.vehiclemodel", "pk": 639, "fields": {"name": "C Class", "make": 17}}, {"model": "akuko_api.vehiclemodel", "pk": 640, "fields": {"name": "CL Class", "make": 17}}, {"model": "akuko_api.vehiclemodel", "pk": 641, "fields": {"name": "CLA Class", "make": 17}}, {"model": "akuko_api.vehiclemodel", "pk": 642, "fields": {"name": "CLE", "make": 17}}, {"model": "akuko_api.vehiclemodel", "pk": 643, "fields": {"name": "CLK Class", "make": 17}}, {"model": "akuko_api.vehiclemodel", "pk": 644, "fields": {"name": "CLS Class", "make": 17}}, {"model": "akuko_api.vehiclemodel", "pk": 645, "fields": {"name": "Citan", "make": 17}}, {"model": "akuko_api.vehiclemodel", "pk": 646, "fields": {"name": "E Class", "make": 17}}, {"model": "akuko_api.vehiclemodel", "pk": 647, "fields": {"name": "EQA", "make": 17}}, {"model": "akuko_api.vehiclemodel", "pk": 648, "fields": {"name": "EQB", "make": 17}}, {"model": "akuko_api.vehiclemodel", "pk": 649, "fields": {"name": "EQC", "make": 17}}, {"model": "akuko_api.vehiclemodel", "pk": 650, "fields": {"name": "EQE", "make": 17}}, {"model": "akuko_api.vehiclemodel", "pk": 651, "fields": {"name": "EQS", "make": 17}}, {"model": "akuko_api.vehiclemodel", "pk": 652, "fields": {"name": "EQV", "make": 17}}, {"model": "akuko_api.vehiclemodel", "pk": 653, "fields": {"name": "G Class", "make": 17}}, {"model": "akuko_api.vehiclemodel", "pk": 654, "fields": {"name": "GL Class", "make": 17}}, {"model": "akuko_api.vehiclemodel", "pk": 655, "fields": {"name": "GLA Class", "make": 17}}, {"model": "akuko_api.vehiclemodel", "pk": 656, "fields": {"name": "GLB", "make": 17}}, {"model": "akuko_api.vehiclemodel", "pk": 657, "fields": {"name": "GLC Class", "make": 17}}, {"model": "akuko_api.vehiclemodel", "pk": 658, "fields": {"name": "GLE Class", "make": 17}}, {"model": "akuko_api.vehiclemodel", "pk": 659, "fields": {"name": "GLS", "make": 17}}, {"model": "akuko_api.vehiclemodel", "pk": 670, "fields": {"name": "M Class", "make": 17}}, {"model": "akuko_api.vehiclemodel", "pk": 671, "fields": {"name": "S Class", "make": 17}}, {"model": "akuko_api.vehiclemodel", "pk": 672, "fields": {"name": "SL Class", "make": 17}}, {"model": "akuko_api.vehiclemodel", "pk": 673, "fields": {"name": "SLC", "make": 17}}, {"model": "akuko_api.vehiclemodel", "pk": 674, "fields": {"name": "SLK Class", "make": 17}}, {"model": "akuko_api.vehiclemodel", "pk": 675, "fields": {"name": "Sprinter", "make": 17}}, {"model": "akuko_api.vehiclemodel", "pk": 676, "fields": {"name": "V Class", "make": 17}}, {"model": "akuko_api.vehiclemodel", "pk": 677, "fields": {"name": "<PERSON><PERSON>", "make": 17}}, {"model": "akuko_api.vehiclemodel", "pk": 678, "fields": {"name": "<PERSON><PERSON>", "make": 17}}, {"model": "akuko_api.vehiclemodel", "pk": 679, "fields": {"name": "X Class", "make": 17}}, {"model": "akuko_api.vehiclemodel", "pk": 680, "fields": {"name": " <PERSON><PERSON>", "make": 17}}, {"model": "akuko_api.vehiclemodel", "pk": 681, "fields": {"name": "Atego", "make": 17}}, {"model": "akuko_api.vehiclemodel", "pk": 682, "fields": {"name": "Sprinter", "make": 17}}, {"model": "akuko_api.vehiclemodel", "pk": 683, "fields": {"name": " Tourismo", "make": 17}}, {"model": "akuko_api.vehiclemodel", "pk": 684, "fields": {"name": "Travego", "make": 17}}, {"model": "akuko_api.vehiclemodel", "pk": 685, "fields": {"name": "Citaro", "make": 17}}, {"model": "akuko_api.vehiclemodel", "pk": 686, "fields": {"name": "Econic", "make": 17}}, {"model": "akuko_api.vehiclemodel", "pk": 687, "fields": {"name": "Unimog", "make": 17}}, {"model": "akuko_api.vehiclemodel", "pk": 688, "fields": {"name": "1 Series", "make": 18}}, {"model": "akuko_api.vehiclemodel", "pk": 689, "fields": {"name": "2 Series", "make": 18}}, {"model": "akuko_api.vehiclemodel", "pk": 690, "fields": {"name": "2 Series Active Tourer", "make": 18}}, {"model": "akuko_api.vehiclemodel", "pk": 691, "fields": {"name": "2 Series Gran Tourer", "make": 18}}, {"model": "akuko_api.vehiclemodel", "pk": 692, "fields": {"name": "3 Series", "make": 18}}, {"model": "akuko_api.vehiclemodel", "pk": 693, "fields": {"name": "4 Series", "make": 18}}, {"model": "akuko_api.vehiclemodel", "pk": 694, "fields": {"name": "5 Series", "make": 18}}, {"model": "akuko_api.vehiclemodel", "pk": 695, "fields": {"name": "6 Series", "make": 18}}, {"model": "akuko_api.vehiclemodel", "pk": 696, "fields": {"name": "7 Series", "make": 18}}, {"model": "akuko_api.vehiclemodel", "pk": 697, "fields": {"name": "8 Series", "make": 18}}, {"model": "akuko_api.vehiclemodel", "pk": 698, "fields": {"name": "F650", "make": 18}}, {"model": "akuko_api.vehiclemodel", "pk": 699, "fields": {"name": "M2", "make": 18}}, {"model": "akuko_api.vehiclemodel", "pk": 700, "fields": {"name": "M235i", "make": 18}}, {"model": "akuko_api.vehiclemodel", "pk": 701, "fields": {"name": "M3", "make": 18}}, {"model": "akuko_api.vehiclemodel", "pk": 702, "fields": {"name": "M4", "make": 18}}, {"model": "akuko_api.vehiclemodel", "pk": 703, "fields": {"name": "M5", "make": 18}}, {"model": "akuko_api.vehiclemodel", "pk": 704, "fields": {"name": "R1200GS", "make": 18}}, {"model": "akuko_api.vehiclemodel", "pk": 705, "fields": {"name": "S1000 XR", "make": 18}}, {"model": "akuko_api.vehiclemodel", "pk": 706, "fields": {"name": "X1", "make": 18}}, {"model": "akuko_api.vehiclemodel", "pk": 707, "fields": {"name": "X2", "make": 18}}, {"model": "akuko_api.vehiclemodel", "pk": 708, "fields": {"name": "X3", "make": 18}}, {"model": "akuko_api.vehiclemodel", "pk": 709, "fields": {"name": "X4", "make": 18}}, {"model": "akuko_api.vehiclemodel", "pk": 710, "fields": {"name": "X5", "make": 18}}, {"model": "akuko_api.vehiclemodel", "pk": 711, "fields": {"name": "X6", "make": 18}}, {"model": "akuko_api.vehiclemodel", "pk": 712, "fields": {"name": "X7", "make": 18}}, {"model": "akuko_api.vehiclemodel", "pk": 713, "fields": {"name": "XM", "make": 18}}, {"model": "akuko_api.vehiclemodel", "pk": 714, "fields": {"name": "Z1", "make": 18}}, {"model": "akuko_api.vehiclemodel", "pk": 715, "fields": {"name": "Z3", "make": 18}}, {"model": "akuko_api.vehiclemodel", "pk": 716, "fields": {"name": "Z4", "make": 18}}, {"model": "akuko_api.vehiclemodel", "pk": 717, "fields": {"name": "i3", "make": 18}}, {"model": "akuko_api.vehiclemodel", "pk": 718, "fields": {"name": "i4", "make": 18}}, {"model": "akuko_api.vehiclemodel", "pk": 719, "fields": {"name": "i5", "make": 18}}, {"model": "akuko_api.vehiclemodel", "pk": 720, "fields": {"name": "i7", "make": 18}}, {"model": "akuko_api.vehiclemodel", "pk": 721, "fields": {"name": "i8", "make": 18}}, {"model": "akuko_api.vehiclemodel", "pk": 722, "fields": {"name": "iX", "make": 18}}, {"model": "akuko_api.vehiclemodel", "pk": 723, "fields": {"name": "iX1", "make": 18}}, {"model": "akuko_api.vehiclemodel", "pk": 724, "fields": {"name": "iX2", "make": 18}}, {"model": "akuko_api.vehiclemodel", "pk": 725, "fields": {"name": "iX3", "make": 18}}, {"model": "akuko_api.vehiclemodel", "pk": 726, "fields": {"name": "Aveo", "make": 19}}, {"model": "akuko_api.vehiclemodel", "pk": 727, "fields": {"name": "Malibu", "make": 19}}, {"model": "akuko_api.vehiclemodel", "pk": 728, "fields": {"name": "Cruze", "make": 19}}, {"model": "akuko_api.vehiclemodel", "pk": 729, "fields": {"name": "Impala", "make": 19}}, {"model": "akuko_api.vehiclemodel", "pk": 730, "fields": {"name": "Equinox", "make": 19}}, {"model": "akuko_api.vehiclemodel", "pk": 731, "fields": {"name": "Trailblazer", "make": 19}}, {"model": "akuko_api.vehiclemodel", "pk": 732, "fields": {"name": "Captiva", "make": 19}}, {"model": "akuko_api.vehiclemodel", "pk": 733, "fields": {"name": "Bolt EV", "make": 19}}, {"model": "akuko_api.vehiclemodel", "pk": 734, "fields": {"name": "Traverse", "make": 19}}, {"model": "akuko_api.vehiclemodel", "pk": 735, "fields": {"name": "Camaro", "make": 19}}, {"model": "akuko_api.vehiclemodel", "pk": 736, "fields": {"name": "Corvette", "make": 19}}, {"model": "akuko_api.vehiclemodel", "pk": 737, "fields": {"name": "<PERSON><PERSON>", "make": 19}}, {"model": "akuko_api.vehiclemodel", "pk": 738, "fields": {"name": "Trax", "make": 19}}, {"model": "akuko_api.vehiclemodel", "pk": 739, "fields": {"name": "Express", "make": 19}}, {"model": "akuko_api.vehiclemodel", "pk": 740, "fields": {"name": "Silverado", "make": 19}}, {"model": "akuko_api.vehiclemodel", "pk": 741, "fields": {"name": "Kodiak", "make": 19}}, {"model": "akuko_api.vehiclemodel", "pk": 742, "fields": {"name": "B-Series", "make": 19}}, {"model": "akuko_api.vehiclemodel", "pk": 743, "fields": {"name": "P30", "make": 19}}, {"model": "akuko_api.vehiclemodel", "pk": 744, "fields": {"name": "Grand Move", "make": 20}}, {"model": "akuko_api.vehiclemodel", "pk": 745, "fields": {"name": "Mira", "make": 20}}, {"model": "akuko_api.vehiclemodel", "pk": 746, "fields": {"name": "Move", "make": 20}}, {"model": "akuko_api.vehiclemodel", "pk": 747, "fields": {"name": "Terios", "make": 20}}, {"model": "akuko_api.vehiclemodel", "pk": 748, "fields": {"name": "<PERSON><PERSON>", "make": 20}}, {"model": "akuko_api.vehiclemodel", "pk": 749, "fields": {"name": "<PERSON><PERSON>", "make": 20}}, {"model": "akuko_api.vehiclemodel", "pk": 750, "fields": {"name": "Sirion", "make": 20}}, {"model": "akuko_api.vehiclemodel", "pk": 751, "fields": {"name": "<PERSON>", "make": 20}}, {"model": "akuko_api.vehiclemodel", "pk": 752, "fields": {"name": "Hijet", "make": 20}}, {"model": "akuko_api.vehiclemodel", "pk": 753, "fields": {"name": "<PERSON><PERSON>", "make": 20}}, {"model": "akuko_api.vehiclemodel", "pk": 754, "fields": {"name": "Gran Max", "make": 21}}, {"model": "akuko_api.vehiclemodel", "pk": 755, "fields": {"name": "Hijet", "make": 21}}, {"model": "akuko_api.vehiclemodel", "pk": 756, "fields": {"name": " Delta", "make": 21}}, {"model": "akuko_api.vehiclemodel", "pk": 757, "fields": {"name": " Bongo", "make": 21}}, {"model": "akuko_api.vehiclemodel", "pk": 758, "fields": {"name": "130", "make": 21}}, {"model": "akuko_api.vehiclemodel", "pk": 759, "fields": {"name": "500", "make": 21}}, {"model": "akuko_api.vehiclemodel", "pk": 760, "fields": {"name": "500 C", "make": 21}}, {"model": "akuko_api.vehiclemodel", "pk": 761, "fields": {"name": "500X", "make": 21}}, {"model": "akuko_api.vehiclemodel", "pk": 762, "fields": {"name": "500E", "make": 21}}, {"model": "akuko_api.vehiclemodel", "pk": 763, "fields": {"name": "500L", "make": 21}}, {"model": "akuko_api.vehiclemodel", "pk": 764, "fields": {"name": "600", "make": 21}}, {"model": "akuko_api.vehiclemodel", "pk": 765, "fields": {"name": "<PERSON><PERSON><PERSON>", "make": 21}}, {"model": "akuko_api.vehiclemodel", "pk": 766, "fields": {"name": "Bravo", "make": 21}}, {"model": "akuko_api.vehiclemodel", "pk": 767, "fields": {"name": "Coupe", "make": 21}}, {"model": "akuko_api.vehiclemodel", "pk": 768, "fields": {"name": "<PERSON><PERSON><PERSON><PERSON>", "make": 21}}, {"model": "akuko_api.vehiclemodel", "pk": 769, "fields": {"name": "Doblo", "make": 21}}, {"model": "akuko_api.vehiclemodel", "pk": 770, "fields": {"name": "<PERSON><PERSON><PERSON>", "make": 21}}, {"model": "akuko_api.vehiclemodel", "pk": 771, "fields": {"name": "Ducato", "make": 21}}, {"model": "akuko_api.vehiclemodel", "pk": 772, "fields": {"name": "<PERSON><PERSON><PERSON> Camper", "make": 21}}, {"model": "akuko_api.vehiclemodel", "pk": 773, "fields": {"name": "Ducato Passenger", "make": 21}}, {"model": "akuko_api.vehiclemodel", "pk": 774, "fields": {"name": "<PERSON><PERSON><PERSON>", "make": 21}}, {"model": "akuko_api.vehiclemodel", "pk": 775, "fields": {"name": "Fullback", "make": 21}}, {"model": "akuko_api.vehiclemodel", "pk": 776, "fields": {"name": "Grande Punto", "make": 21}}, {"model": "akuko_api.vehiclemodel", "pk": 777, "fields": {"name": "Panda", "make": 21}}, {"model": "akuko_api.vehiclemodel", "pk": 778, "fields": {"name": "Punt<PERSON>", "make": 21}}, {"model": "akuko_api.vehiclemodel", "pk": 779, "fields": {"name": "Punto Evo", "make": 21}}, {"model": "akuko_api.vehiclemodel", "pk": 780, "fields": {"name": "<PERSON><PERSON><PERSON>", "make": 21}}, {"model": "akuko_api.vehiclemodel", "pk": 781, "fields": {"name": "<PERSON><PERSON>", "make": 21}}, {"model": "akuko_api.vehiclemodel", "pk": 782, "fields": {"name": "<PERSON><PERSON>", "make": 21}}, {"model": "akuko_api.vehiclemodel", "pk": 783, "fields": {"name": "Tipo", "make": 21}}, {"model": "akuko_api.vehiclemodel", "pk": 784, "fields": {"name": "B-MAX", "make": 22}}, {"model": "akuko_api.vehiclemodel", "pk": 785, "fields": {"name": "C-MAX", "make": 22}}, {"model": "akuko_api.vehiclemodel", "pk": 786, "fields": {"name": "Cortina", "make": 22}}, {"model": "akuko_api.vehiclemodel", "pk": 787, "fields": {"name": "Courier", "make": 22}}, {"model": "akuko_api.vehiclemodel", "pk": 788, "fields": {"name": "E-Transit", "make": 22}}, {"model": "akuko_api.vehiclemodel", "pk": 789, "fields": {"name": "Ecosport", "make": 22}}, {"model": "akuko_api.vehiclemodel", "pk": 790, "fields": {"name": "Escort", "make": 22}}, {"model": "akuko_api.vehiclemodel", "pk": 791, "fields": {"name": "Explorer", "make": 22}}, {"model": "akuko_api.vehiclemodel", "pk": 792, "fields": {"name": "F150", "make": 22}}, {"model": "akuko_api.vehiclemodel", "pk": 793, "fields": {"name": "Fiesta", "make": 22}}, {"model": "akuko_api.vehiclemodel", "pk": 794, "fields": {"name": "Focus", "make": 22}}, {"model": "akuko_api.vehiclemodel", "pk": 795, "fields": {"name": "Galaxy", "make": 22}}, {"model": "akuko_api.vehiclemodel", "pk": 796, "fields": {"name": "Grand C-MAX", "make": 22}}, {"model": "akuko_api.vehiclemodel", "pk": 797, "fields": {"name": "Grand Tourneo Connect", "make": 22}}, {"model": "akuko_api.vehiclemodel", "pk": 798, "fields": {"name": "<PERSON>", "make": 22}}, {"model": "akuko_api.vehiclemodel", "pk": 799, "fields": {"name": "Ka +", "make": 22}}, {"model": "akuko_api.vehiclemodel", "pk": 800, "fields": {"name": "<PERSON><PERSON>", "make": 22}}, {"model": "akuko_api.vehiclemodel", "pk": 801, "fields": {"name": "Model A", "make": 22}}, {"model": "akuko_api.vehiclemodel", "pk": 802, "fields": {"name": "Model T", "make": 22}}, {"model": "akuko_api.vehiclemodel", "pk": 803, "fields": {"name": "Mondeo", "make": 22}}, {"model": "akuko_api.vehiclemodel", "pk": 804, "fields": {"name": "Mustang", "make": 22}}, {"model": "akuko_api.vehiclemodel", "pk": 805, "fields": {"name": "Mustang Mach-E", "make": 22}}, {"model": "akuko_api.vehiclemodel", "pk": 806, "fields": {"name": "Prefect", "make": 22}}, {"model": "akuko_api.vehiclemodel", "pk": 807, "fields": {"name": "<PERSON><PERSON>", "make": 22}}, {"model": "akuko_api.vehiclemodel", "pk": 808, "fields": {"name": "<PERSON>", "make": 22}}, {"model": "akuko_api.vehiclemodel", "pk": 809, "fields": {"name": "S-MAX", "make": 22}}, {"model": "akuko_api.vehiclemodel", "pk": 810, "fields": {"name": "Tourneo", "make": 22}}, {"model": "akuko_api.vehiclemodel", "pk": 811, "fields": {"name": "Tourneo Connect", "make": 22}}, {"model": "akuko_api.vehiclemodel", "pk": 812, "fields": {"name": "Tourneo Custom", "make": 22}}, {"model": "akuko_api.vehiclemodel", "pk": 813, "fields": {"name": "Transit", "make": 22}}, {"model": "akuko_api.vehiclemodel", "pk": 814, "fields": {"name": "Transit Connect", "make": 22}}, {"model": "akuko_api.vehiclemodel", "pk": 815, "fields": {"name": "Transit Courier", "make": 22}}, {"model": "akuko_api.vehiclemodel", "pk": 816, "fields": {"name": "Transit Custom", "make": 22}}, {"model": "akuko_api.vehiclemodel", "pk": 817, "fields": {"name": "Transit Custom Campervan", "make": 22}}, {"model": "akuko_api.vehiclemodel", "pk": 818, "fields": {"name": "Transit Custom Kombi", "make": 22}}, {"model": "akuko_api.vehiclemodel", "pk": 819, "fields": {"name": "Transit Kombi", "make": 22}}, {"model": "akuko_api.vehiclemodel", "pk": 820, "fields": {"name": "Transit Tourneo", "make": 22}}, {"model": "akuko_api.vehiclemodel", "pk": 821, "fields": {"name": "F-Series", "make": 23}}, {"model": "akuko_api.vehiclemodel", "pk": 822, "fields": {"name": "Clubman", "make": 23}}, {"model": "akuko_api.vehiclemodel", "pk": 823, "fields": {"name": "Convertible", "make": 23}}, {"model": "akuko_api.vehiclemodel", "pk": 824, "fields": {"name": "<PERSON>", "make": 23}}, {"model": "akuko_api.vehiclemodel", "pk": 825, "fields": {"name": "<PERSON>", "make": 23}}, {"model": "akuko_api.vehiclemodel", "pk": 826, "fields": {"name": "<PERSON>", "make": 23}}, {"model": "akuko_api.vehiclemodel", "pk": 827, "fields": {"name": "Countryman", "make": 23}}, {"model": "akuko_api.vehiclemodel", "pk": 828, "fields": {"name": "First", "make": 23}}, {"model": "akuko_api.vehiclemodel", "pk": 829, "fields": {"name": "<PERSON>", "make": 23}}, {"model": "akuko_api.vehiclemodel", "pk": 830, "fields": {"name": "<PERSON> Works", "make": 23}}, {"model": "akuko_api.vehiclemodel", "pk": 831, "fields": {"name": "One", "make": 23}}, {"model": "akuko_api.vehiclemodel", "pk": 832, "fields": {"name": "Paceman", "make": 23}}, {"model": "akuko_api.vehiclemodel", "pk": 833, "fields": {"name": "Roadster", "make": 23}}, {"model": "akuko_api.vehiclemodel", "pk": 834, "fields": {"name": "Defender", "make": 24}}, {"model": "akuko_api.vehiclemodel", "pk": 835, "fields": {"name": "Discovery", "make": 24}}, {"model": "akuko_api.vehiclemodel", "pk": 836, "fields": {"name": "Discovery Sport", "make": 24}}, {"model": "akuko_api.vehiclemodel", "pk": 837, "fields": {"name": "Freelander", "make": 24}}, {"model": "akuko_api.vehiclemodel", "pk": 838, "fields": {"name": "Range Rover", "make": 24}}, {"model": "akuko_api.vehiclemodel", "pk": 839, "fields": {"name": "Range Rover Evoque", "make": 24}}, {"model": "akuko_api.vehiclemodel", "pk": 840, "fields": {"name": "Range Rover Sport", "make": 24}}, {"model": "akuko_api.vehiclemodel", "pk": 841, "fields": {"name": "Range Rover Velar", "make": 24}}, {"model": "akuko_api.vehiclemodel", "pk": 842, "fields": {"name": "Series III", "make": 24}}, {"model": "akuko_api.vehiclemodel", "pk": 843, "fields": {"name": "E-PACE", "make": 25}}, {"model": "akuko_api.vehiclemodel", "pk": 844, "fields": {"name": "E-TYPE", "make": 25}}, {"model": "akuko_api.vehiclemodel", "pk": 845, "fields": {"name": "F-PACE", "make": 25}}, {"model": "akuko_api.vehiclemodel", "pk": 846, "fields": {"name": "F-TYPE", "make": 25}}, {"model": "akuko_api.vehiclemodel", "pk": 847, "fields": {"name": "I-PACE", "make": 25}}, {"model": "akuko_api.vehiclemodel", "pk": 848, "fields": {"name": "MARK II", "make": 25}}, {"model": "akuko_api.vehiclemodel", "pk": 849, "fields": {"name": "S-TYPE", "make": 25}}, {"model": "akuko_api.vehiclemodel", "pk": 850, "fields": {"name": "X-TYPE", "make": 25}}, {"model": "akuko_api.vehiclemodel", "pk": 851, "fields": {"name": "XE", "make": 25}}, {"model": "akuko_api.vehiclemodel", "pk": 852, "fields": {"name": "XF", "make": 25}}, {"model": "akuko_api.vehiclemodel", "pk": 853, "fields": {"name": "XJ", "make": 25}}, {"model": "akuko_api.vehiclemodel", "pk": 854, "fields": {"name": "XJ6", "make": 25}}, {"model": "akuko_api.vehiclemodel", "pk": 855, "fields": {"name": "XJR", "make": 25}}, {"model": "akuko_api.vehiclemodel", "pk": 856, "fields": {"name": "XJS", "make": 25}}, {"model": "akuko_api.vehiclemodel", "pk": 857, "fields": {"name": "XK", "make": 25}}, {"model": "akuko_api.vehiclemodel", "pk": 858, "fields": {"name": "XK 8", "make": 25}}, {"model": "akuko_api.vehiclemodel", "pk": 859, "fields": {"name": "XKR", "make": 25}}, {"model": "akuko_api.vehiclemodel", "pk": 860, "fields": {"name": "avenger", "make": 26}}, {"model": "akuko_api.vehiclemodel", "pk": 861, "fields": {"name": "cherokee", "make": 26}}, {"model": "akuko_api.vehiclemodel", "pk": 862, "fields": {"name": "compass", "make": 26}}, {"model": "akuko_api.vehiclemodel", "pk": 863, "fields": {"name": "grand cherokee", "make": 26}}, {"model": "akuko_api.vehiclemodel", "pk": 864, "fields": {"name": "patriot", "make": 26}}, {"model": "akuko_api.vehiclemodel", "pk": 865, "fields": {"name": "renegade", "make": 26}}, {"model": "akuko_api.vehiclemodel", "pk": 866, "fields": {"name": "wrangler", "make": 26}}, {"model": "akuko_api.vehiclemodel", "pk": 867, "fields": {"name": "ct 200 h", "make": 27}}, {"model": "akuko_api.vehiclemodel", "pk": 868, "fields": {"name": "es 300 h", "make": 27}}, {"model": "akuko_api.vehiclemodel", "pk": 869, "fields": {"name": "gs 300", "make": 27}}, {"model": "akuko_api.vehiclemodel", "pk": 870, "fields": {"name": "gs 300 h", "make": 27}}, {"model": "akuko_api.vehiclemodel", "pk": 871, "fields": {"name": "gs 450 h", "make": 27}}, {"model": "akuko_api.vehiclemodel", "pk": 872, "fields": {"name": "hs 250h", "make": 27}}, {"model": "akuko_api.vehiclemodel", "pk": 873, "fields": {"name": "is 200", "make": 27}}, {"model": "akuko_api.vehiclemodel", "pk": 874, "fields": {"name": "is 250", "make": 27}}, {"model": "akuko_api.vehiclemodel", "pk": 875, "fields": {"name": "is 300 h", "make": 27}}, {"model": "akuko_api.vehiclemodel", "pk": 876, "fields": {"name": "isf", "make": 27}}, {"model": "akuko_api.vehiclemodel", "pk": 877, "fields": {"name": "lbx", "make": 27}}, {"model": "akuko_api.vehiclemodel", "pk": 878, "fields": {"name": "lc 500 h", "make": 27}}, {"model": "akuko_api.vehiclemodel", "pk": 879, "fields": {"name": "ls 500 h", "make": 27}}, {"model": "akuko_api.vehiclemodel", "pk": 880, "fields": {"name": "ls 600 h", "make": 27}}, {"model": "akuko_api.vehiclemodel", "pk": 881, "fields": {"name": "nx 300 h", "make": 27}}, {"model": "akuko_api.vehiclemodel", "pk": 882, "fields": {"name": "nx 450 h+", "make": 27}}, {"model": "akuko_api.vehiclemodel", "pk": 883, "fields": {"name": "rc 300 h", "make": 27}}, {"model": "akuko_api.vehiclemodel", "pk": 884, "fields": {"name": "rx 350", "make": 27}}, {"model": "akuko_api.vehiclemodel", "pk": 885, "fields": {"name": "rx 400 h", "make": 27}}, {"model": "akuko_api.vehiclemodel", "pk": 886, "fields": {"name": "rx 450 h", "make": 27}}, {"model": "akuko_api.vehiclemodel", "pk": 887, "fields": {"name": "rx 450h+", "make": 27}}, {"model": "akuko_api.vehiclemodel", "pk": 888, "fields": {"name": "sc 430", "make": 27}}, {"model": "akuko_api.vehiclemodel", "pk": 889, "fields": {"name": "ux 250 h", "make": 27}}, {"model": "akuko_api.vehiclemodel", "pk": 890, "fields": {"name": "DB11", "make": 28}}, {"model": "akuko_api.vehiclemodel", "pk": 891, "fields": {"name": "DB7", "make": 28}}, {"model": "akuko_api.vehiclemodel", "pk": 892, "fields": {"name": "DB9", "make": 28}}, {"model": "akuko_api.vehiclemodel", "pk": 893, "fields": {"name": "DBS", "make": 28}}, {"model": "akuko_api.vehiclemodel", "pk": 894, "fields": {"name": "<PERSON><PERSON><PERSON>", "make": 28}}, {"model": "akuko_api.vehiclemodel", "pk": 895, "fields": {"name": "Vantage", "make": 28}}, {"model": "akuko_api.vehiclemodel", "pk": 896, "fields": {"name": "Rapide", "make": 28}}, {"model": "akuko_api.vehiclemodel", "pk": 897, "fields": {"name": "Lagonda", "make": 28}}, {"model": "akuko_api.vehiclemodel", "pk": 898, "fields": {"name": "Virage", "make": 28}}, {"model": "akuko_api.vehiclemodel", "pk": 899, "fields": {"name": "<PERSON><PERSON><PERSON>", "make": 28}}, {"model": "akuko_api.vehiclemodel", "pk": 900, "fields": {"name": "One-77", "make": 28}}, {"model": "akuko_api.vehiclemodel", "pk": 901, "fields": {"name": "Vulcan", "make": 28}}, {"model": "akuko_api.vehiclemodel", "pk": 902, "fields": {"name": "Valhalla", "make": 28}}, {"model": "akuko_api.vehiclemodel", "pk": 903, "fields": {"name": "Victor", "make": 28}}, {"model": "akuko_api.vehiclemodel", "pk": 904, "fields": {"name": "Valkyrie", "make": 28}}, {"model": "akuko_api.vehiclemodel", "pk": 905, "fields": {"name": "<PERSON><PERSON><PERSON>", "make": 29}}, {"model": "akuko_api.vehiclemodel", "pk": 906, "fields": {"name": "<PERSON><PERSON>", "make": 29}}, {"model": "akuko_api.vehiclemodel", "pk": 907, "fields": {"name": "<PERSON><PERSON>", "make": 29}}, {"model": "akuko_api.vehiclemodel", "pk": 908, "fields": {"name": "Tivoli", "make": 29}}, {"model": "akuko_api.vehiclemodel", "pk": 909, "fields": {"name": "Tivoli XLV", "make": 29}}, {"model": "akuko_api.vehiclemodel", "pk": 910, "fields": {"name": "Actyon", "make": 29}}, {"model": "akuko_api.vehiclemodel", "pk": 911, "fields": {"name": "<PERSON><PERSON><PERSON>", "make": 29}}, {"model": "akuko_api.vehiclemodel", "pk": 912, "fields": {"name": "<PERSON><PERSON>", "make": 29}}, {"model": "akuko_api.vehiclemodel", "pk": 913, "fields": {"name": "Stavic", "make": 29}}, {"model": "akuko_api.vehiclemodel", "pk": 914, "fields": {"name": "XLV", "make": 29}}, {"model": "akuko_api.vehiclemodel", "pk": 915, "fields": {"name": "HD785-7", "make": 30}}, {"model": "akuko_api.vehiclemodel", "pk": 916, "fields": {"name": "WA380-8", "make": 30}}, {"model": "akuko_api.vehiclemodel", "pk": 917, "fields": {"name": "PC210-10", "make": 30}}, {"model": "akuko_api.vehiclemodel", "pk": 918, "fields": {"name": "D375A-8", "make": 30}}, {"model": "akuko_api.vehiclemodel", "pk": 919, "fields": {"name": "GD655-6", "make": 30}}, {"model": "akuko_api.vehiclemodel", "pk": 920, "fields": {"name": "HM400-5", "make": 30}}, {"model": "akuko_api.vehiclemodel", "pk": 921, "fields": {"name": "PC138US-11", "make": 30}}, {"model": "akuko_api.vehiclemodel", "pk": 922, "fields": {"name": "D61EX-24", "make": 30}}, {"model": "akuko_api.vehiclemodel", "pk": 923, "fields": {"name": "WA500-8", "make": 30}}, {"model": "akuko_api.vehiclemodel", "pk": 924, "fields": {"name": "PC490LC-11", "make": 30}}, {"model": "akuko_api.vehiclemodel", "pk": 925, "fields": {"name": "C1", "make": 31}}, {"model": "akuko_api.vehiclemodel", "pk": 926, "fields": {"name": "C3", "make": 31}}, {"model": "akuko_api.vehiclemodel", "pk": 927, "fields": {"name": "C3 Aircross", "make": 31}}, {"model": "akuko_api.vehiclemodel", "pk": 928, "fields": {"name": "C4", "make": 31}}, {"model": "akuko_api.vehiclemodel", "pk": 929, "fields": {"name": "C4 Cactus", "make": 31}}, {"model": "akuko_api.vehiclemodel", "pk": 930, "fields": {"name": "C4 Picasso", "make": 31}}, {"model": "akuko_api.vehiclemodel", "pk": 931, "fields": {"name": "C5", "make": 31}}, {"model": "akuko_api.vehiclemodel", "pk": 932, "fields": {"name": "C5 Aircross", "make": 31}}, {"model": "akuko_api.vehiclemodel", "pk": 933, "fields": {"name": "Berlingo", "make": 31}}, {"model": "akuko_api.vehiclemodel", "pk": 934, "fields": {"name": "DS3", "make": 31}}, {"model": "akuko_api.vehiclemodel", "pk": 935, "fields": {"name": "DS4", "make": 31}}, {"model": "akuko_api.vehiclemodel", "pk": 936, "fields": {"name": "DS5", "make": 31}}, {"model": "akuko_api.vehiclemodel", "pk": 937, "fields": {"name": "Nexon", "make": 32}}, {"model": "akuko_api.vehiclemodel", "pk": 938, "fields": {"name": "Harrier", "make": 32}}, {"model": "akuko_api.vehiclemodel", "pk": 939, "fields": {"name": "Safari", "make": 32}}, {"model": "akuko_api.vehiclemodel", "pk": 940, "fields": {"name": "Tiago", "make": 32}}, {"model": "akuko_api.vehiclemodel", "pk": 941, "fields": {"name": "Tigor", "make": 32}}, {"model": "akuko_api.vehiclemodel", "pk": 942, "fields": {"name": "Altroz", "make": 32}}, {"model": "akuko_api.vehiclemodel", "pk": 943, "fields": {"name": "Punch", "make": 32}}, {"model": "akuko_api.vehiclemodel", "pk": 944, "fields": {"name": "Hexa", "make": 32}}, {"model": "akuko_api.vehiclemodel", "pk": 945, "fields": {"name": "Zest", "make": 32}}, {"model": "akuko_api.vehiclemodel", "pk": 946, "fields": {"name": "<PERSON><PERSON>", "make": 32}}, {"model": "akuko_api.vehiclemodel", "pk": 947, "fields": {"name": "Sierra", "make": 32}}, {"model": "akuko_api.vehiclemodel", "pk": 948, "fields": {"name": "Indica", "make": 32}}, {"model": "akuko_api.vehiclemodel", "pk": 949, "fields": {"name": "Aria", "make": 32}}, {"model": "akuko_api.vehiclemodel", "pk": 950, "fields": {"name": "4x4", "make": 33}}, {"model": "akuko_api.vehiclemodel", "pk": 951, "fields": {"name": " 6x6", "make": 33}}, {"model": "akuko_api.vehiclemodel", "pk": 952, "fields": {"name": "Cab", "make": 33}}, {"model": "akuko_api.vehiclemodel", "pk": 953, "fields": {"name": "Open", "make": 33}}, {"model": "akuko_api.vehiclemodel", "pk": 954, "fields": {"name": "SUV", "make": 33}}, {"model": "akuko_api.vehiclemodel", "pk": 955, "fields": {"name": "Utility", "make": 33}}, {"model": "akuko_api.vehiclemodel", "pk": 956, "fields": {"name": "Pickup", "make": 33}}, {"model": "akuko_api.vehiclemodel", "pk": 957, "fields": {"name": "X", "make": 33}}, {"model": "akuko_api.vehiclemodel", "pk": 958, "fields": {"name": "Z", "make": 33}}, {"model": "akuko_api.vehiclemodel", "pk": 959, "fields": {"name": "Electric", "make": 33}}, {"model": "akuko_api.vehiclemodel", "pk": 960, "fields": {"name": "Hybrid", "make": 33}}, {"model": "akuko_api.vehiclemodel", "pk": 961, "fields": {"name": "Luxury", "make": 33}}, {"model": "akuko_api.vehiclemodel", "pk": 962, "fields": {"name": "488 GTB", "make": 34}}, {"model": "akuko_api.vehiclemodel", "pk": 963, "fields": {"name": "F8 Tributo", "make": 34}}, {"model": "akuko_api.vehiclemodel", "pk": 964, "fields": {"name": " Roma", "make": 34}}, {"model": "akuko_api.vehiclemodel", "pk": 965, "fields": {"name": "SF90 Stradale", "make": 34}}, {"model": "akuko_api.vehiclemodel", "pk": 966, "fields": {"name": "Portofino", "make": 34}}, {"model": "akuko_api.vehiclemodel", "pk": 967, "fields": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "make": 34}}, {"model": "akuko_api.vehiclemodel", "pk": 968, "fields": {"name": "812 Superfast", "make": 34}}, {"model": "akuko_api.vehiclemodel", "pk": 969, "fields": {"name": "F12 Berlinetta", "make": 34}}, {"model": "akuko_api.vehiclemodel", "pk": 970, "fields": {"name": "458 Italia", "make": 34}}, {"model": "akuko_api.vehiclemodel", "pk": 971, "fields": {"name": "California", "make": 34}}, {"model": "akuko_api.vehiclemodel", "pk": 972, "fields": {"name": "488 Spider", "make": 34}}, {"model": "akuko_api.vehiclemodel", "pk": 973, "fields": {"name": "F430", "make": 34}}, {"model": "akuko_api.vehiclemodel", "pk": 974, "fields": {"name": " Giulia", "make": 35}}, {"model": "akuko_api.vehiclemodel", "pk": 975, "fields": {"name": "Stelvio", "make": 35}}, {"model": "akuko_api.vehiclemodel", "pk": 976, "fields": {"name": "Giulietta", "make": 35}}, {"model": "akuko_api.vehiclemodel", "pk": 977, "fields": {"name": "4C", "make": 35}}, {"model": "akuko_api.vehiclemodel", "pk": 978, "fields": {"name": "<PERSON><PERSON>", "make": 35}}, {"model": "akuko_api.vehiclemodel", "pk": 979, "fields": {"name": " 8C Competizione", "make": 35}}, {"model": "akuko_api.vehiclemodel", "pk": 980, "fields": {"name": "GTV", "make": 35}}, {"model": "akuko_api.vehiclemodel", "pk": 981, "fields": {"name": "Spider", "make": 35}}, {"model": "akuko_api.vehiclemodel", "pk": 982, "fields": {"name": "<PERSON><PERSON>", "make": 35}}, {"model": "akuko_api.vehiclemodel", "pk": 983, "fields": {"name": "33", "make": 35}}, {"model": "akuko_api.vehiclemodel", "pk": 984, "fields": {"name": "147", "make": 35}}, {"model": "akuko_api.vehiclemodel", "pk": 985, "fields": {"name": "159", "make": 35}}, {"model": "akuko_api.vehiclemodel", "pk": 986, "fields": {"name": "164", "make": 35}}, {"model": "akuko_api.vehiclemodel", "pk": 987, "fields": {"name": "Tonale", "make": 35}}, {"model": "akuko_api.vehiclemodel", "pk": 988, "fields": {"name": "Charger", "make": 36}}, {"model": "akuko_api.vehiclemodel", "pk": 989, "fields": {"name": "Challenger", "make": 36}}, {"model": "akuko_api.vehiclemodel", "pk": 990, "fields": {"name": "Durango", "make": 36}}, {"model": "akuko_api.vehiclemodel", "pk": 991, "fields": {"name": "Journey", "make": 36}}, {"model": "akuko_api.vehiclemodel", "pk": 992, "fields": {"name": "Viper", "make": 36}}, {"model": "akuko_api.vehiclemodel", "pk": 993, "fields": {"name": "Dart", "make": 36}}, {"model": "akuko_api.vehiclemodel", "pk": 994, "fields": {"name": "Ram 1500", "make": 36}}, {"model": "akuko_api.vehiclemodel", "pk": 995, "fields": {"name": "Neon", "make": 36}}, {"model": "akuko_api.vehiclemodel", "pk": 996, "fields": {"name": "Cal<PERSON>", "make": 36}}, {"model": "akuko_api.vehiclemodel", "pk": 997, "fields": {"name": "Dakota", "make": 36}}, {"model": "akuko_api.vehiclemodel", "pk": 998, "fields": {"name": " 300", "make": 37}}, {"model": "akuko_api.vehiclemodel", "pk": 999, "fields": {"name": " Pacifica", "make": 37}}, {"model": "akuko_api.vehiclemodel", "pk": 1000, "fields": {"name": " Voyager", "make": 37}}, {"model": "akuko_api.vehiclemodel", "pk": 1001, "fields": {"name": " Aspen", "make": 37}}, {"model": "akuko_api.vehiclemodel", "pk": 1002, "fields": {"name": " PT Cruiser", "make": 37}}, {"model": "akuko_api.vehiclemodel", "pk": 1003, "fields": {"name": "Sebring", "make": 37}}, {"model": "akuko_api.vehiclemodel", "pk": 1004, "fields": {"name": "200", "make": 37}}, {"model": "akuko_api.vehiclemodel", "pk": 1005, "fields": {"name": "Town & Country", "make": 37}}, {"model": "akuko_api.vehiclemodel", "pk": 1006, "fields": {"name": "Crossfire", "make": 37}}, {"model": "akuko_api.vehiclemodel", "pk": 1007, "fields": {"name": " Cordoba", "make": 37}}, {"model": "akuko_api.vehiclemodel", "pk": 1008, "fields": {"name": "Imperial", "make": 37}}, {"model": "akuko_api.vehiclemodel", "pk": 1009, "fields": {"name": "Newport", "make": 37}}, {"model": "akuko_api.vehiclemodel", "pk": 1010, "fields": {"name": "LeBaron", "make": 37}}, {"model": "akuko_api.vehiclemodel", "pk": 1011, "fields": {"name": "Conquest", "make": 37}}, {"model": "akuko_api.vehiclemodel", "pk": 1012, "fields": {"name": "100", "make": 37}}, {"model": "akuko_api.vehiclemodel", "pk": 1013, "fields": {"name": "R Series", "make": 38}}, {"model": "akuko_api.vehiclemodel", "pk": 1014, "fields": {"name": "S Series", "make": 38}}, {"model": "akuko_api.vehiclemodel", "pk": 1015, "fields": {"name": "P Series", "make": 38}}, {"model": "akuko_api.vehiclemodel", "pk": 1016, "fields": {"name": "G Series", "make": 38}}, {"model": "akuko_api.vehiclemodel", "pk": 1017, "fields": {"name": "L Series", "make": 38}}, {"model": "akuko_api.vehiclemodel", "pk": 1018, "fields": {"name": "Citywide", "make": 38}}, {"model": "akuko_api.vehiclemodel", "pk": 1019, "fields": {"name": "Intercity", "make": 38}}, {"model": "akuko_api.vehiclemodel", "pk": 1020, "fields": {"name": "OmniExpress", "make": 38}}, {"model": "akuko_api.vehiclemodel", "pk": 1021, "fields": {"name": "Touring", "make": 38}}, {"model": "akuko_api.vehiclemodel", "pk": 1022, "fields": {"name": "R 500", "make": 38}}, {"model": "akuko_api.vehiclemodel", "pk": 1023, "fields": {"name": "P 280", "make": 38}}, {"model": "akuko_api.vehiclemodel", "pk": 1024, "fields": {"name": "G 410", "make": 38}}, {"model": "akuko_api.vehiclemodel", "pk": 1025, "fields": {"name": "R 490", "make": 38}}, {"model": "akuko_api.vehiclemodel", "pk": 1026, "fields": {"name": "14.5m Bus", "make": 38}}, {"model": "akuko_api.vehiclemodel", "pk": 1027, "fields": {"name": "18m Bus", "make": 38}}, {"model": "akuko_api.vehiclemodel", "pk": 1028, "fields": {"name": "P 340", "make": 38}}, {"model": "akuko_api.vehiclemodel", "pk": 1029, "fields": {"name": "L 280", "make": 38}}, {"model": "akuko_api.vehiclemodel", "pk": 1030, "fields": {"name": "TGX", "make": 39}}, {"model": "akuko_api.vehiclemodel", "pk": 1031, "fields": {"name": "TGS", "make": 39}}, {"model": "akuko_api.vehiclemodel", "pk": 1032, "fields": {"name": "TGM", "make": 39}}, {"model": "akuko_api.vehiclemodel", "pk": 1033, "fields": {"name": "TGL", "make": 39}}, {"model": "akuko_api.vehiclemodel", "pk": 1034, "fields": {"name": "Lion's City", "make": 39}}, {"model": "akuko_api.vehiclemodel", "pk": 1035, "fields": {"name": "Lion's Coach", "make": 39}}, {"model": "akuko_api.vehiclemodel", "pk": 1036, "fields": {"name": "A40", "make": 39}}, {"model": "akuko_api.vehiclemodel", "pk": 1037, "fields": {"name": "Lion's Regio", "make": 39}}, {"model": "akuko_api.vehiclemodel", "pk": 1038, "fields": {"name": "A26", "make": 39}}, {"model": "akuko_api.vehiclemodel", "pk": 1039, "fields": {"name": "TGS 18.640", "make": 39}}, {"model": "akuko_api.vehiclemodel", "pk": 1040, "fields": {"name": "TGX 18.640", "make": 39}}, {"model": "akuko_api.vehiclemodel", "pk": 1041, "fields": {"name": "Lion's Intercity", "make": 39}}, {"model": "akuko_api.vehiclemodel", "pk": 1042, "fields": {"name": "CLA", "make": 39}}, {"model": "akuko_api.vehiclemodel", "pk": 1043, "fields": {"name": "F2000", "make": 39}}, {"model": "akuko_api.vehiclemodel", "pk": 1044, "fields": {"name": "L2000", "make": 39}}, {"model": "akuko_api.vehiclemodel", "pk": 1045, "fields": {"name": "TGE", "make": 39}}, {"model": "akuko_api.vehicletype", "pk": 1, "fields": {"name": "Sedan"}}, {"model": "akuko_api.vehicletype", "pk": 2, "fields": {"name": "SUV"}}, {"model": "akuko_api.vehicletype", "pk": 3, "fields": {"name": "Truck"}}, {"model": "akuko_api.vehicletype", "pk": 4, "fields": {"name": "Minivan"}}, {"model": "akuko_api.vehicletype", "pk": 5, "fields": {"name": "Bus"}}, {"model": "akuko_api.vehicletype", "pk": 6, "fields": {"name": "Hatchback"}}, {"model": "akuko_api.vehicletype", "pk": 7, "fields": {"name": "Pickup"}}, {"model": "akuko_api.vehicletype", "pk": 8, "fields": {"name": "Saloon"}}, {"model": "akuko_api.vehicletype", "pk": 9, "fields": {"name": "Tuktuk"}}, {"model": "akuko_api.vehicletype", "pk": 10, "fields": {"name": "Motorcycle"}}, {"model": "akuko_api.vehicletype", "pk": 11, "fields": {"name": "Convertible"}}, {"model": "akuko_api.vehicletype", "pk": 12, "fields": {"name": "Lorry"}}, {"model": "akuko_api.vehicletype", "pk": 13, "fields": {"name": "Wagon"}}, {"model": "akuko_api.vehicletype", "pk": 14, "fields": {"name": "Coupe"}}]