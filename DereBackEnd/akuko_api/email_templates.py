"""
Email templates for the KadereConnect application.
This file contains HTML templates for various email notifications.
"""

from django.conf import settings
from django.utils import timezone


def get_base_template():
    """
    Returns the base HTML template with common styling.
    """
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <style type="text/css">
            /* Reset styles */
            body, html {{
                margin: 0;
                padding: 0;
                width: 100% !important;
                height: 100% !important;
            }}
            
            /* Base styles */
            body {{
                -webkit-text-size-adjust: 100%;
                -ms-text-size-adjust: 100%;
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
                line-height: 1.6;
                color: #333333;
                background-color: #ffffff;
            }}
            
            /* Container */
            .container {{
                width: 100%;
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
                box-sizing: border-box;
            }}
            
            /* Header */
            .header {{
                background-color: #f8f9fa;
                padding: 20px;
                text-align: center;
                border-radius: 8px;
                margin-bottom: 20px;
            }}
            
            /* Content Box */
            .content-box {{
                background-color: #f8f9fa;
                padding: 20px;
                margin: 20px 0;
                border-radius: 8px;
                border-left: 4px solid #c3410d;
            }}
            
            /* Button */
            .button {{
                background: #c3410d;
                border: 1px solid #c3410d;
                border-radius: 6px;
                color: #ffffff !important;
                display: inline-block;
                font-size: 16px;
                font-weight: bold;
                margin: 0;
                padding: 12px 25px;
                text-decoration: none;
                text-align: center;
                min-width: 120px;
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            }}
            
            /* Note */
            .note {{
                font-style: italic;
                color: #666666;
                text-align: center;
                margin: 20px 0;
                font-size: 14px;
            }}
            
            /* Footer */
            .footer {{
                text-align: center;
                color: #666666;
                margin-top: 30px;
                padding-top: 20px;
                border-top: 1px solid #eeeeee;
            }}
            
            /* Outlook-specific fixes */
            body {{
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
            }}
            
            * {{
                -ms-text-size-adjust: 100%;
            }}
            
            a {{
                text-decoration: none;
            }}
            
            img {{
                -ms-interpolation-mode: bicubic;
            }}
            
            table, td {{
                mso-table-lspace: 0pt;
                mso-table-rspace: 0pt;
            }}
            
            /* Mobile responsiveness */
            @media only screen and (max-width: 480px) {{
                .container {{
                    padding: 10px !important;
                }}
                
                .button {{
                    width: 100% !important;
                    min-width: 100% !important;
                }}
            }}
        </style>
    </head>
    <body>
        <div class="container">
            {content}
            <div class="footer">
                <p style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;">
                    Best regards,<br>
                    <strong>KadereConnect Team</strong>
                </p>
            </div>
        </div>
    </body>
    </html>
    """


def get_verification_email_template(user):
    """
    Returns the HTML template for email verification.
    """
    role = 'partner' if user.role == 'partner' else 'driver'
    verification_link = f"{settings.FRONTEND_URL}/verify-email/{user.verification_code}/?role={role}"
    
    content = f"""
    <div class="header">
        <h2>📧 Email Verification</h2>
    </div>
    
    <p>Hello {user.first_name},</p>
    <p>Thank you for registering with KadereConnect. Please verify your email address to complete your registration.</p>
    
    <div style="text-align: center; margin: 30px 0;">
        <a href="{verification_link}" class="button">
            Verify Email
        </a>
    </div>
    
    <p class="note">
        If the button above doesn't work, you can copy and paste the following link into your browser:<br>
        {verification_link}
    </p>
    """
    
    return get_base_template().format(content=content)


def get_driver_added_email_template(driver, partner_name, password):
    """
    Returns the HTML template for driver added notification.
    """
    verification_link = f"{settings.FRONTEND_URL}/verify-email/{driver.user.verification_code}/?role=driver"
    
    content = f"""
    <div class="header">
        <h2>🚗 Welcome to KadereConnect!</h2>
    </div>
    
    <p>Hello {driver.first_name},</p>
    <p>You have been added as a driver by <strong>{partner_name}</strong>.</p>
    
    <div class="content-box">
        <h3 style="margin-top: 0;">Your Login Details:</h3>
        <p><strong>Email:</strong> {driver.email}</p>
        <p><strong>Temporary Password:</strong> {password}</p>
    </div>
    
    <p>Please confirm your email and change your password after logging in.</p>
    
    <div style="text-align: center; margin: 30px 0;">
        <a href="{verification_link}" class="button">
            Verify Email
        </a>
    </div>
    
    <p class="note">
        If the button above doesn't work, you can copy and paste the following link into your browser:<br>
        {verification_link}
    </p>
    """
    
    return get_base_template().format(content=content)


def get_driver_hired_email_template(driver, partner, job):
    """
    Returns the HTML template for driver hired notification.
    """
    content = f"""
    <div class="header">
        <h2>🎉 Congratulations! You've Been Hired</h2>
    </div>
    
    <p>Hello {driver.user.first_name},</p>
    <p>We're pleased to inform you that <strong>{partner.user.first_name} {partner.user.last_name}</strong> has hired you for the following job:</p>
    
    <div class="content-box">
        <h3 style="margin-top: 0;">Job Details:</h3>
        <p><strong>🚘 Vehicle:</strong> {job.vehicle_make.name} {job.vehicle_model.name}</p>
        <p><strong>📍 Work Area:</strong> {job.preferred_work_area.name}</p>
        <p><strong>📅 Work Days:</strong> {job.work_days}</p>
    </div>
    
    <p>Please contact your employer for further instructions and next steps.</p>
    
    <div style="text-align: center; margin: 30px 0;">
        <a href="{settings.FRONTEND_URL}/driverdashboard" class="button">
            Go to Dashboard
        </a>
    </div>
    """
    
    return get_base_template().format(content=content)


def get_driver_rejected_email_template(driver, partner, job):
    """
    Returns the HTML template for driver rejection notification.
    """
    content = f"""
    <div class="header">
        <h2>Update on Your Job Application</h2>
    </div>
    
    <p>Hello {driver.user.first_name},</p>
    <p>We regret to inform you that your application for the job with <strong>{partner.user.first_name} {partner.user.last_name}</strong> has been rejected.</p>
    
    <div class="content-box">
        <h3 style="margin-top: 0;">Job Details:</h3>
        <p><strong>🚘 Vehicle:</strong> {job.vehicle_make.name} {job.vehicle_model.name}</p>
        <p><strong>📍 Work Area:</strong> {job.preferred_work_area.name}</p>
    </div>
    
    <p>Don't be discouraged! There are many other opportunities available on our platform.</p>
    
    <div style="text-align: center; margin: 30px 0;">
        <a href="{settings.FRONTEND_URL}/jobs" class="button">
            Browse More Jobs
        </a>
    </div>
    
    <p class="note">
        Keep applying! The right opportunity is waiting for you.
    </p>
    """
    
    return get_base_template().format(content=content)


def get_password_reset_email_template(user, reset_link):
    """
    Returns the HTML template for password reset notification.
    """
    content = f"""
    <div class="header">
        <h2>🔐 Password Reset Request</h2>
    </div>
    
    <p>Hello {user.first_name},</p>
    <p>We received a request to reset your password. Click the button below to create a new password:</p>
    
    <div style="text-align: center; margin: 30px 0;">
        <a href="{reset_link}" class="button">
            Reset Password
        </a>
    </div>
    
    <p class="note">
        If you did not request a password reset, please ignore this email or contact support if you have concerns.
    </p>
    
    <p class="note">
        If the button above doesn't work, you can copy and paste the following link into your browser:<br>
        {reset_link}
    </p>
    """
    
    return get_base_template().format(content=content)


def get_subscription_success_email_template(partner, subscription, transaction=None):
    """
    Returns the HTML template for subscription success notification.
    """
    # Determine if it's a free or paid plan
    is_free_plan = subscription.plan_type == 'Free'
    plan_details = f"{subscription.plan_type} Plan"
    
    if not is_free_plan:
        plan_details += f" - {subscription.billing_cycle}"
    
    # Payment details section
    payment_section = ""
    if transaction and not is_free_plan:
        payment_section = f"""
        <div class="content-box" style="background-color: #e8f5e8; border-left-color: #28a745;">
            <h3 style="color: #28a745; margin-top: 0;">💳 Payment Confirmation</h3>
            <p><strong>Receipt Number:</strong> {transaction.mpesa_receipt_number or 'N/A'}</p>
            <p><strong>Amount Paid:</strong> KSh {subscription.amount}</p>
            <p><strong>Transaction Date:</strong> {transaction.transaction_date.strftime('%B %d, %Y at %I:%M %p') if transaction.transaction_date else 'N/A'}</p>
        </div>
        """
    elif is_free_plan:
        payment_section = """
        <div class="content-box" style="background-color: #e8f5e8; border-left-color: #28a745;">
            <h3 style="color: #28a745; margin-top: 0;">🎁 Free Plan Activated</h3>
            <p>Your free plan has been activated successfully. No payment required!</p>
        </div>
        """
    
    # Subscription details
    expiry_info = ""
    if subscription.expiry_date:
        expiry_info = f"<p><strong>🗓️ Expires:</strong> {subscription.expiry_date.strftime('%B %d, %Y')}</p>"
    
    content = f"""
    <div class="header" style="background: linear-gradient(135deg, #c3410d 0%, #e85a1a 100%); color: white;">
        <h1 style="margin: 0; font-size: 28px;">🎉 Subscription Activated!</h1>
        <p style="margin: 10px 0 0 0; opacity: 0.9;">Welcome to KadereConnect Premium</p>
    </div>
    
    <div style="text-align: center; margin-bottom: 30px;">
        <h2 style="color: #333; margin: 0 0 10px 0;">Hello {partner.user.first_name}!</h2>
        <p style="color: #666; font-size: 16px; margin: 0;">Your subscription has been activated successfully.</p>
    </div>
    
    {payment_section}
    
    <div class="content-box" style="border: 2px solid #c3410d; background-color: #fff;">
        <h3 style="color: #333; margin-top: 0; display: flex; align-items: center;">
            <span style="background-color: #c3410d; color: white; padding: 5px 10px; border-radius: 15px; font-size: 12px; margin-right: 10px;">ACTIVE</span>
            📋 Subscription Details
        </h3>
        <p><strong>📦 Plan:</strong> {plan_details}</p>
        <p><strong>🚗 Vehicle Limit:</strong> {subscription.vehicle_count} vehicle{'s' if subscription.vehicle_count != 1 else ''}</p>
        <p><strong>📅 Start Date:</strong> {subscription.start_date.strftime('%B %d, %Y') if subscription.start_date else 'Today'}</p>
        {expiry_info}
    </div>
    
    <div class="content-box" style="background-color: #fff8f6; border-left-color: #c3410d;">
        <h3 style="color: #333; margin-top: 0;">🚀 What's Next?</h3>
        <ul style="color: #666; line-height: 1.6; padding-left: 20px;">
            <li>📝 Post unlimited job listings</li>
            <li>👥 Access advanced driver management tools</li>
            <li>📊 Track vehicle performance and expenses</li>
            <li>📈 Generate detailed financial reports</li>
            <li>🆘 24/7 priority customer support</li>
        </ul>
    </div>
    
    <div style="text-align: center; margin: 30px 0;">
        <a href="{settings.FRONTEND_URL}/partnerdashboard" class="button">
            Go to Dashboard
        </a>
    </div>
    
    <p class="note">
        Need help? We're here for you!<br>
        📧 <a href="mailto:<EMAIL>" style="color: #c3410d; text-decoration: none;"><EMAIL></a> | 
    </p>
    """
    
    return get_base_template().format(content=content)


def get_subscription_expiry_template(partner_name, plan_type, expiry_date, days_before=3):
    """
    Returns the HTML template for subscription expiry notifications.
    """
    # Determine urgency and styling based on days_before
    if days_before == 0:
        urgency = "EXPIRED"
        urgency_color = "#dc3545"  # Red
        urgency_icon = "🚨"
        header_bg = "linear-gradient(135deg, #dc3545 0%, #c82333 100%)"
        subject = "Your Subscription Has Expired!"
        action_text = "Renew Now"
        message_intro = f"Your <strong>{plan_type}</strong> subscription has expired."
        urgency_message = "Your subscription has expired. Please renew immediately to restore access to all features."
    elif days_before == 1:
        urgency = "URGENT"
        urgency_color = "#fd7e14"  # Orange
        urgency_icon = "⚠️"
        header_bg = "linear-gradient(135deg, #fd7e14 0%, #e55a00 100%)"
        subject = "URGENT: Your Subscription Expires Tomorrow!"
        action_text = "Renew Today"
        message_intro = f"Your <strong>{plan_type}</strong> subscription expires tomorrow."
        urgency_message = "Your subscription expires tomorrow. Renew today to avoid service interruption."
    elif days_before == 3:
        urgency = "IMPORTANT"
        urgency_color = "#ffc107"  # Yellow
        urgency_icon = "📅"
        header_bg = "linear-gradient(135deg, #ffc107 0%, #e0a800 100%)"
        subject = "Important: Your Subscription Expires in 3 Days"
        action_text = "Renew Soon"
        message_intro = f"Your <strong>{plan_type}</strong> subscription expires in 3 days."
        urgency_message = "Your subscription expires in 3 days. Please renew soon to ensure uninterrupted service."
    else:  # 7 days
        urgency = "REMINDER"
        urgency_color = "#17a2b8"  # Info blue
        urgency_icon = "📋"
        header_bg = "linear-gradient(135deg, #17a2b8 0%, #138496 100%)"
        subject = "Reminder: Your Subscription Expires in 1 Week"
        action_text = "Plan Renewal"
        message_intro = f"Your <strong>{plan_type}</strong> subscription expires in 1 week."
        urgency_message = "Your subscription expires in 1 week. Plan your renewal to avoid any service interruption."

    # Format the expiry date for display
    expiry_date_formatted = expiry_date.strftime('%B %d, %Y')

    call_to_action_url = f"{settings.FRONTEND_URL}/partnerdashboard"

    content = f"""
    <div class="header" style="background: {header_bg}; color: white;">
        <h1 style="margin: 0; font-size: 28px;">{urgency_icon} {urgency}: Subscription Alert</h1>
        <p style="margin: 10px 0 0 0; opacity: 0.9;">KadeReconnect Subscription</p>
    </div>

    <p>Dear {partner_name},</p>

    <p>{message_intro}</p>

    <div class="content-box" style="border-left: 4px solid {urgency_color};">
        <h3 style="margin-top: 0; color: {urgency_color};">📋 Subscription Details:</h3>
        <p><strong>Plan:</strong> {plan_type}</p>
        <p><strong>{'Expired on' if days_before == 0 else 'Expires on'}:</strong> {expiry_date_formatted}</p>
    </div>

    <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin: 20px 0;">
        <p style="margin: 0; color: #856404;"><strong>⚠️ Important:</strong> {urgency_message}</p>
    </div>

    <div style="text-align: center; margin: 30px 0;">
        <a href="{call_to_action_url}" class="button" style="background-color: {urgency_color};">
            {action_text}
        </a>
    </div>

    <p>If you have any questions or need assistance with your subscription, please do not hesitate to contact our support team.</p>

    <p>Thank you for being a valued member of the KadeReconnect community.</p>

    <p class="note">
        Need help? We're here for you!<br>
        📧 <a href="mailto:<EMAIL>" style="color: #c3410d; text-decoration: none;"><EMAIL></a>
    </p>
    """

    return subject, get_base_template().format(content=content)


def get_driver_document_expiry_template(driver, expiring_docs, days_before):
    """
    Returns the HTML template for driver document expiry notifications.
    """
    # Determine urgency and styling
    if days_before == 0:
        # Check if this is actually expiring today vs expired
        from django.utils import timezone
        today = timezone.now().date()

        # If any document expires today, treat as "expiring today"
        is_expiring_today = any("expires: " + str(today) in doc for doc in expiring_docs)

        if is_expiring_today:
            urgency = "CRITICAL"
            urgency_color = "#dc3545"  # Red
            urgency_icon = "🚨"
            header_bg = "linear-gradient(135deg, #dc3545 0%, #c82333 100%)"
            action_text = "Renew Today"
            message_intro = "Your documents expire today and require immediate attention:"
        else:
            urgency = "EXPIRED"
            urgency_color = "#dc3545"  # Red
            urgency_icon = "🚨"
            header_bg = "linear-gradient(135deg, #dc3545 0%, #c82333 100%)"
            action_text = "Renew Immediately"
            message_intro = "Your documents have expired and require immediate attention:"
    elif days_before == 1:
        urgency = "URGENT"
        urgency_color = "#fd7e14"  # Orange
        urgency_icon = "⚠️"
        header_bg = "linear-gradient(135deg, #fd7e14 0%, #e55a00 100%)"
        action_text = "Renew Today"
        message_intro = "Your documents expire tomorrow and require immediate attention:"
    elif days_before == 7:
        urgency = "IMPORTANT"
        urgency_color = "#ffc107"  # Yellow
        urgency_icon = "📅"
        header_bg = "linear-gradient(135deg, #ffc107 0%, #e0a800 100%)"
        action_text = "Renew Soon"
        message_intro = "Your documents expire in 1 week. Please renew them soon:"
    else:
        urgency = "REMINDER"
        urgency_color = "#17a2b8"  # Info blue
        urgency_icon = "📋"
        header_bg = "linear-gradient(135deg, #17a2b8 0%, #138496 100%)"
        action_text = "Plan Renewal"
        message_intro = f"Your documents expire in {days_before} days. Please plan for renewal:"

    subject = f"KadeReconnect - {urgency}: Document {'Expired' if days_before == 0 else 'Expiry'} Alert"

    # Format document list with icons
    docs_html = ""
    for doc in expiring_docs:
        if "License" in doc and "PSV" not in doc:
            icon = "🪪"
        elif "PSV" in doc:
            icon = "🚌"
        elif "Good Conduct" in doc:
            icon = "📜"
        else:
            icon = "📄"
        docs_html += f'<li style="margin: 8px 0; padding: 8px; background: #f8f9fa; border-radius: 4px;"><strong>{icon} {doc}</strong></li>'

    dashboard_url = f"{settings.FRONTEND_URL}/driverdashboard"

    content = f"""
    <div class="header" style="background: {header_bg}; color: white;">
        <h1 style="margin: 0; font-size: 28px;">{urgency_icon} {urgency}: Document Alert</h1>
        <p style="margin: 10px 0 0 0; opacity: 0.9;">Action Required - KadeReconnect</p>
    </div>

    <p>Dear {driver.first_name} {driver.last_name},</p>

    <p>{message_intro}</p>

    <div class="content-box" style="border-left: 4px solid {urgency_color};">
        <h3 style="margin-top: 0; color: {urgency_color};">📋 Documents Requiring Attention:</h3>
        <ul style="list-style: none; padding: 0; margin: 0;">
            {docs_html}
        </ul>
    </div>

    <div class="content-box">
        <h3 style="margin-top: 0;">📝 How to Update Your Documents:</h3>
        <ol style="margin: 0; padding-left: 20px;">
            <li>Log in to your KadeReconnect account</li>
            <li>Go to your profile section</li>
            <li>Upload the renewed documents</li>
            <li>Wait for verification confirmation</li>
        </ol>
    </div>

    <div style="text-align: center; margin: 30px 0;">
        <a href="{dashboard_url}" class="button" style="background-color: {urgency_color};">
            {action_text}
        </a>
    </div>

    <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin: 20px 0;">
        <p style="margin: 0; color: #856404;"><strong>⚠️ Important:</strong>
        {'Expired documents may result in service suspension until renewed.' if days_before == 0 else 'Ensure your documents are renewed before expiry to avoid service interruptions.'}
        </p>
    </div>

    <p>If you have any questions or need assistance with document renewal, please contact our support team.</p>

    <p class="note">
        Need help? We're here for you!<br>
        📧 <a href="mailto:<EMAIL>" style="color: #c3410d; text-decoration: none;"><EMAIL></a>
    </p>
    """

    return subject, get_base_template().format(content=content)


def get_vehicle_document_expiry_template(vehicle, expiring_docs, days_before, recipient_type="partner"):
    """
    Returns the HTML template for vehicle document expiry notifications.
    """
    # Determine urgency and styling
    if days_before == 0:
        # Check if this is actually expiring today vs expired
        from django.utils import timezone
        today = timezone.now().date()

        # If any document expires today, treat as "expiring today"
        is_expiring_today = any("expires: " + str(today) in doc for doc in expiring_docs)

        if is_expiring_today:
            urgency = "CRITICAL"
            urgency_color = "#dc3545"  # Red
            urgency_icon = "🚨"
            header_bg = "linear-gradient(135deg, #dc3545 0%, #c82333 100%)"
            action_text = "Renew Today"
            message_intro = "Vehicle documents expire today and require immediate attention:"
        else:
            urgency = "EXPIRED"
            urgency_color = "#dc3545"  # Red
            urgency_icon = "🚨"
            header_bg = "linear-gradient(135deg, #dc3545 0%, #c82333 100%)"
            action_text = "Renew Immediately"
            message_intro = "Vehicle documents have expired and require immediate attention:"
    elif days_before == 1:
        urgency = "URGENT"
        urgency_color = "#fd7e14"  # Orange
        urgency_icon = "⚠️"
        header_bg = "linear-gradient(135deg, #fd7e14 0%, #e55a00 100%)"
        action_text = "Renew Today"
        message_intro = "Vehicle documents expire tomorrow and require immediate attention:"
    elif days_before == 7:
        urgency = "IMPORTANT"
        urgency_color = "#ffc107"  # Yellow
        urgency_icon = "📅"
        header_bg = "linear-gradient(135deg, #ffc107 0%, #e0a800 100%)"
        action_text = "Renew Soon"
        message_intro = "Vehicle documents expire in 1 week. Please renew them soon:"
    else:
        urgency = "REMINDER"
        urgency_color = "#17a2b8"  # Info blue
        urgency_icon = "📋"
        header_bg = "linear-gradient(135deg, #17a2b8 0%, #138496 100%)"
        action_text = "Plan Renewal"
        message_intro = f"Vehicle documents expire in {days_before} days. Please plan for renewal:"

    subject = f"KadeReconnect - {urgency}: Vehicle Document {'Expired' if days_before == 0 else 'Expiry'} Alert"

    # Format document list with icons
    docs_html = ""
    for doc in expiring_docs:
        if "Registration" in doc:
            icon = "📋"
        elif "NTSA" in doc:
            icon = "🔍"
        elif "Insurance" in doc:
            icon = "🛡️"
        else:
            icon = "📄"
        docs_html += f'<li style="margin: 8px 0; padding: 8px; background: #f8f9fa; border-radius: 4px;"><strong>{icon} {doc}</strong></li>'

    # Customize message based on recipient
    if recipient_type == "partner":
        recipient_name = f"{vehicle.partner.first_name} {vehicle.partner.last_name}"
        dashboard_url = f"{settings.FRONTEND_URL}/partnerdashboard"
        role_context = "As the vehicle owner, please ensure these documents are renewed promptly."
    else:  # driver
        recipient_name = f"{vehicle.driver.first_name} {vehicle.driver.last_name}"
        dashboard_url = f"{settings.FRONTEND_URL}/driverdashboard"
        role_context = "Please inform your partner about these expiring documents."

    content = f"""
    <div class="header" style="background: {header_bg}; color: white;">
        <h1 style="margin: 0; font-size: 28px;">{urgency_icon} {urgency}: Vehicle Document Alert</h1>
        <p style="margin: 10px 0 0 0; opacity: 0.9;">Action Required - KadeReconnect</p>
    </div>

    <p>Dear {recipient_name},</p>

    <p>{message_intro}</p>

    <div class="content-box" style="border-left: 4px solid {urgency_color};">
        <h3 style="margin-top: 0; color: {urgency_color};">🚗 Vehicle: {vehicle.registration_number}</h3>
        <h4 style="margin: 10px 0;">📋 Documents Requiring Attention:</h4>
        <ul style="list-style: none; padding: 0; margin: 0;">
            {docs_html}
        </ul>
    </div>

    <div class="content-box">
        <p><strong>📝 Action Required:</strong> {role_context}</p>
    </div>

    <div style="text-align: center; margin: 30px 0;">
        <a href="{dashboard_url}" class="button" style="background-color: {urgency_color};">
            {action_text}
        </a>
    </div>

    <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin: 20px 0;">
        <p style="margin: 0; color: #856404;"><strong>⚠️ Important:</strong>
        {'Expired vehicle documents may result in service suspension and legal issues.' if days_before == 0 else 'Ensure vehicle documents are renewed before expiry to maintain compliance and avoid service interruptions.'}
        </p>
    </div>

    <p>If you have any questions or need assistance with document renewal, please contact our support team.</p>

    <p class="note">
        Need help? We're here for you!<br>
        📧 <a href="mailto:<EMAIL>" style="color: #c3410d; text-decoration: none;"><EMAIL></a>
    </p>
    """

    return subject, get_base_template().format(content=content)
