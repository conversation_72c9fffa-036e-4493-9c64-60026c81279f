# utils.py

from django.core.mail import send_mail
from django.conf import settings
from .models import User, Notification, NotificationSubscriber
from django.core.exceptions import ValidationError
import os
# import asyncio
from functools import wraps
# import threading


def send_email(subject, message, recipient_list):
    send_mail(
        subject,
        message,
        settings.DEFAULT_FROM_EMAIL,
        recipient_list,
        fail_silently=False,
    )


def send_bulk_emails(subject, message, user_filter=None):
    """
    Send bulk emails to all subscribed users who match the filter (if provided).
    Creates notification records for each email sent.

    Args:
        subject (str): Email subject
        message (str): Email message
        user_filter (dict, optional): Filter to apply when querying users
    """

    # We're only checking if NotificationSubscriber record exists (isnull=False)
    subscribed_users = User.objects.filter(
        # This is correct - checking for presence in table
        notificationsubscriber__isnull=False,
        is_active=True,
        email_verified=True
    )

    # Apply additional filters
    if user_filter:
        subscribed_users = subscribed_users.filter(**user_filter)

    # Create notification records and send emails
    for user in subscribed_users:
        try:
            # Personalize message if needed (example)
            personalized_message = message.replace(
                '{first_name}', user.first_name or '')

            # Create notification record
            notification = Notification.objects.create(
                subject=subject,
                type="email",
                recipient=user.email,
                status="pending",
                message=personalized_message
            )

            # Send email
            send_mail(
                subject=subject,
                message=personalized_message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[user.email],
                fail_silently=False
            )

            notification.mark_as_sent()
        except Exception as e:
            notification.mark_as_failed()
            print(f"Failed to send email to {user.email}: {str(e)}")


def validate_file_extension(value):
    """
    Validate that uploaded file has an allowed extension (JPG, PNG, PDF).
    """
    allowed_extensions = ['.jpg', '.jpeg', '.png', '.pdf']
    ext = os.path.splitext(value.name)[1].lower()
    if ext not in allowed_extensions:
        raise ValidationError(
            f'Unsupported file extension. Allowed extensions are: {", ".join(allowed_extensions)}'
        )


def validate_file_size(value):
    """
    Validate that uploaded file size is within the limit (5MB).
    """
    max_size = 5 * 1024 * 1024  # 5MB in bytes
    if value.size > max_size:
        raise ValidationError(
            f'File size too large. Maximum allowed size is 5MB. Current file size is {value.size / (1024 * 1024):.2f}MB.'
        )
