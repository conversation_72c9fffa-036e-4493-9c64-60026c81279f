#!/usr/bin/env bash
set -o errexit  # exit on error

pip install -r requirements.txt

python manage.py collectstatic --no-input
# python manage.py migrate akuko_api 0035 --fake
# python manage.py migrate akuko_api 0037_alter_paymentsettings_options_and_more --fake
python manage.py migrate
python manage.py loaddata db.json
python manage.py populate_notification_subscribers 
# python manage.py run_notification_service
#python sync_production_data.py