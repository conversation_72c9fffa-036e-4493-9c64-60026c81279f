# 🚗 Driver Management API Documentation

> **Complete API Reference**: [View Gist](https://gist.github.com/Kennjenga/932ea6dcb6a234e3f509184caca2d287)

---

## 🤝 Partner Endpoints

### 📊 Performance Report
**Endpoint:** `GET /api/drivers/performance_report/`

#### Query Parameters
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `page` | integer | 1 | Page number |
| `page_size` | integer | 20 (max: 100) | Records per page |
| `age_group` | string | - | Filter by age group |
| `vehicle_type` | string | - | Filter by vehicle type |
| `vehicle_registration` | string | - | Filter by vehicle registration |
| `status` | enum | all | Filter by status: `all`, `active`, `inactive` |
| `min_rating` | float | - | Minimum rating filter |
| `max_rating` | float | - | Maximum rating filter |
| `sort_by` | enum | - | Sort field: `rating`, `name`, `success_rate`, `total_points` |
| `sort_order` | enum | asc | Sort direction: `asc`, `desc` |

<img width="1387" height="888" alt="Performance Report Screenshot" src="https://github.com/user-attachments/assets/2ad60b7f-4e6e-4cb7-bfaf-4ad9222e971a" />

---

### 💰 Payment History Report
**Endpoint:** `GET /api/drivers/payment_history_report/`

#### Query Parameters
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `days` | integer | 30 | Days to look back |
| `driver_id` | integer | - | Filter by specific driver |
| `limit_per_driver` | integer | 10 (max: 50) | Records per driver |

<img width="1381" height="988" alt="Payment History Report Screenshot" src="https://github.com/user-attachments/assets/2df98a45-037e-497b-9adf-7ef12ba1cd9c" />

---

## 👨‍💼 Driver Endpoints

### ⭐ Driver Rating
**Endpoint:** `GET /api/drivers/{driver_id}/rating/`

Get detailed rating information for a specific driver.

<img width="1348" height="910" alt="Driver Rating Screenshot" src="https://github.com/user-attachments/assets/951dba76-034c-4bc8-9f52-e2277338cc5d" />

---

### 💳 Payment History (Enhanced)
**Endpoint:** `GET /api/drivers/{driver_id}/payment_history/`

Enhanced payment history with comprehensive filtering and pagination options.

#### Query Parameters
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `page` | integer | 1 | Page number |
| `page_size` | integer | 20 (max: 100) | Records per page |
| `start_date` | date | - | Filter from date (YYYY-MM-DD) |
| `end_date` | date | - | Filter to date (YYYY-MM-DD) |
| `days` | integer | - | Days to look back |
| `vehicle_id` | integer | - | Filter by specific vehicle |
| `vehicle_registration` | string | - | Filter by vehicle registration |
| `status` | enum | all | Payment status: `on_time`, `late`, `all` |
| `min_amount` | decimal | - | Minimum payment amount |
| `max_amount` | decimal | - | Maximum payment amount |

<img width="1347" height="903" alt="Payment History Screenshot" src="https://github.com/user-attachments/assets/fa7282e2-f290-46f0-a020-87f5da5fe08f" />

---

### 📈 Rating Trend Analysis
**Endpoint:** `GET /api/drivers/{driver_id}/rating_trend/`

Track driver rating performance over time with trend analysis.

<img width="1379" height="889" alt="Rating Trend Screenshot" src="https://github.com/user-attachments/assets/7a022469-cad4-45cc-b377-01fa718c6627" />

---

## 🔗 Quick Links
- [Complete API Documentation](https://gist.github.com/Kennjenga/932ea6dcb6a234e3f509184caca2d287)
- Base URL: `http://127.0.0.1:8000`



