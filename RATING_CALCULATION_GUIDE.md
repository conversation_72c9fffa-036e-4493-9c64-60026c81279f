# Driver Rating System - Calculation & Vehicle Switching Guide

This comprehensive guide explains how driver ratings are calculated, how vehicle switching is handled, and how to trigger rating calculations based on historical data.

## Rating Calculation Overview

### Core Calculation Method
The driver rating system uses a **payment-based scoring system**:

- **On-time Payment**: 5.0 points
- **Late Payment**: 2.5 points  
- **Missed Payment**: 0.0 points

**Final Rating Formula**: `Total Points ÷ Total Expected Payment Days`

### Rating Levels
| Score Range | Level | Description |
|-------------|-------|-------------|
| 4.5 - 5.0 | Excellent | Outstanding payment performance |
| 3.5 - 4.4 | Good | Good payment performance |
| 2.5 - 3.4 | Average | Average payment performance |
| 1.0 - 2.4 | Poor | Poor payment performance |
| 0.0 - 0.9 | Very Poor | Very poor payment performance |

---

## Vehicle Switching Handling

### Robust Vehicle Switch Management

The system handles vehicle switches through multiple mechanisms:

#### 1. **Separate Rating Records per Vehicle**
- Each driver-vehicle combination has its own `DriverRating` record
- Ratings are calculated independently for each vehicle
- Overall driver rating is the weighted average across all vehicles

#### 2. **Historical Payment Tracking**
- All `Revenue` records maintain vehicle associations
- Payment history is preserved even after vehicle switches
- Vehicle information includes historical context

#### 3. **Continuous Rating Updates**
When a driver switches vehicles:

```python
# Example: Driver switches from Vehicle A to Vehicle B
# 1. Vehicle A rating becomes inactive (but preserved)
old_rating = DriverRating.objects.get(driver=driver, vehicle=vehicle_a)
old_rating.is_active = False
old_rating.save()

# 2. New rating record created for Vehicle B
new_rating = create_or_update_driver_rating(driver, vehicle_b, partner)

# 3. Historical payments remain linked to original vehicles
# 4. Overall driver rating considers all vehicles
```

#### 4. **Vehicle History Tracking**
The API provides complete vehicle history:
- All vehicles a driver has used
- Rating data for each vehicle
- Active/inactive status
- Date ranges for each vehicle assignment

---

## Triggering Rating Calculations

### 1. **Automatic Triggers**
Rating calculations are automatically triggered when:

```python
# In Revenue model's save method or signal
def trigger_rating_calculation(revenue_instance):
    """Automatically calculate rating when payment is made"""
    from .rating_utils import create_or_update_driver_rating
    
    driver = revenue_instance.driver
    vehicle = revenue_instance.vehicle
    partner = revenue_instance.partner or driver.partner
    
    # Update rating for this driver-vehicle combination
    create_or_update_driver_rating(driver, vehicle, partner)
```

### 2. **Manual Calculation Commands**

#### Calculate for Single Driver
```bash
python manage.py calculate_driver_rating --driver_id=1
```

#### Calculate for All Drivers
```bash
python manage.py calculate_all_ratings
```

#### Calculate for Partner's Drivers
```bash
python manage.py calculate_partner_ratings --partner_id=1
```

#### Recalculate from Historical Data
```bash
python manage.py recalculate_ratings_from_history --start_date=2024-01-01
```

### 3. **API Endpoints for Manual Triggers**

#### Trigger Single Driver Calculation
```http
POST /api/drivers/{driver_id}/calculate_rating/
Authorization: Bearer {token}
```

#### Trigger Bulk Calculations (Admin only)
```http
POST /api/admin/calculate_ratings/
Content-Type: application/json
{
  "driver_ids": [1, 2, 3],
  "recalculate_from_date": "2024-01-01"
}
```

---

## Historical Data Processing

### Recalculating from Past Data

When you need to recalculate ratings based on historical payment data:

#### 1. **Full Historical Recalculation**
```python
def recalculate_all_ratings_from_history(start_date=None):
    """Recalculate all driver ratings from historical data"""
    from datetime import datetime
    from .models import Driver, Revenue
    from .rating_utils import create_or_update_driver_rating
    
    if not start_date:
        start_date = datetime(2024, 1, 1).date()  # Default start
    
    # Get all drivers with payments
    drivers_with_payments = Driver.objects.filter(
        revenue__date__gte=start_date,
        revenue__deleted=False
    ).distinct()
    
    for driver in drivers_with_payments:
        # Get all vehicles this driver has used
        vehicles_used = Revenue.objects.filter(
            driver=driver,
            date__gte=start_date,
            deleted=False
        ).values_list('vehicle', flat=True).distinct()
        
        for vehicle_id in vehicles_used:
            if vehicle_id:
                vehicle = Vehicle.objects.get(id=vehicle_id)
                partner = driver.partner
                
                # Recalculate rating for this driver-vehicle combo
                create_or_update_driver_rating(driver, vehicle, partner)
```

#### 2. **Incremental Updates**
```python
def update_ratings_for_date_range(start_date, end_date):
    """Update ratings for payments in specific date range"""
    from .models import Revenue
    from .rating_utils import create_or_update_driver_rating
    
    # Get all payments in date range
    payments = Revenue.objects.filter(
        date__gte=start_date,
        date__lte=end_date,
        deleted=False
    ).select_related('driver', 'vehicle')
    
    # Group by driver-vehicle combinations
    driver_vehicle_combos = set()
    for payment in payments:
        if payment.driver and payment.vehicle:
            driver_vehicle_combos.add((payment.driver.id, payment.vehicle.id))
    
    # Update ratings for each combination
    for driver_id, vehicle_id in driver_vehicle_combos:
        driver = Driver.objects.get(id=driver_id)
        vehicle = Vehicle.objects.get(id=vehicle_id)
        create_or_update_driver_rating(driver, vehicle, driver.partner)
```

---

## Advanced Scenarios

### 1. **Driver Transfers Between Partners**
```python
def handle_driver_partner_transfer(driver, old_partner, new_partner):
    """Handle driver transfer between partners"""
    
    # Mark old ratings as inactive
    old_ratings = DriverRating.objects.filter(
        driver=driver, 
        partner=old_partner
    )
    old_ratings.update(is_active=False)
    
    # Create new ratings with new partner
    current_vehicles = driver.current_vehicles()  # Custom method
    for vehicle in current_vehicles:
        create_or_update_driver_rating(driver, vehicle, new_partner)
```

### 2. **Bulk Vehicle Reassignment**
```python
def handle_bulk_vehicle_reassignment(vehicle_assignments):
    """Handle bulk vehicle reassignments
    
    Args:
        vehicle_assignments: List of (driver_id, old_vehicle_id, new_vehicle_id)
    """
    
    for driver_id, old_vehicle_id, new_vehicle_id in vehicle_assignments:
        driver = Driver.objects.get(id=driver_id)
        old_vehicle = Vehicle.objects.get(id=old_vehicle_id)
        new_vehicle = Vehicle.objects.get(id=new_vehicle_id)
        
        # Deactivate old rating
        old_rating = DriverRating.objects.filter(
            driver=driver, 
            vehicle=old_vehicle
        ).first()
        if old_rating:
            old_rating.is_active = False
            old_rating.save()
        
        # Create new rating
        create_or_update_driver_rating(driver, new_vehicle, driver.partner)
```

### 3. **Rating Freeze/Unfreeze**
```python
def freeze_driver_rating(driver, reason="Administrative hold"):
    """Temporarily freeze rating calculations"""
    ratings = DriverRating.objects.filter(driver=driver)
    ratings.update(
        is_active=False,
        freeze_reason=reason,
        frozen_at=timezone.now()
    )

def unfreeze_driver_rating(driver):
    """Resume rating calculations"""
    ratings = DriverRating.objects.filter(driver=driver)
    ratings.update(
        is_active=True,
        freeze_reason=None,
        frozen_at=None
    )
    
    # Recalculate from freeze date
    for rating in ratings:
        create_or_update_driver_rating(driver, rating.vehicle, rating.partner)
```

---

## Data Integrity & Validation

### 1. **Rating Consistency Checks**
```python
def validate_rating_consistency():
    """Check for rating calculation inconsistencies"""
    
    inconsistencies = []
    
    for rating in DriverRating.objects.filter(is_active=True):
        # Recalculate expected rating
        expected_rating = calculate_expected_rating(rating.driver, rating.vehicle)
        
        if abs(rating.current_rating - expected_rating) > 0.1:
            inconsistencies.append({
                'driver': rating.driver.id,
                'vehicle': rating.vehicle.id,
                'current': rating.current_rating,
                'expected': expected_rating
            })
    
    return inconsistencies
```

### 2. **Missing Rating Detection**
```python
def find_drivers_without_ratings():
    """Find drivers who should have ratings but don't"""
    
    # Drivers with recent payments but no active ratings
    drivers_with_payments = Driver.objects.filter(
        revenue__date__gte=timezone.now().date() - timedelta(days=30),
        revenue__deleted=False
    ).distinct()
    
    drivers_without_ratings = []
    for driver in drivers_with_payments:
        if not DriverRating.objects.filter(driver=driver, is_active=True).exists():
            drivers_without_ratings.append(driver)
    
    return drivers_without_ratings
```

---

## Performance Optimization

### 1. **Batch Processing**
- Process ratings in batches to avoid memory issues
- Use database transactions for consistency
- Implement progress tracking for long operations

### 2. **Caching Strategy**
- Cache frequently accessed rating data
- Invalidate cache when ratings are updated
- Use Redis for distributed caching

### 3. **Background Processing**
- Use Celery for asynchronous rating calculations
- Queue rating updates to avoid blocking API responses
- Implement retry mechanisms for failed calculations

This guide provides the foundation for understanding and managing the driver rating system's complex scenarios and ensures data integrity across vehicle switches and partner transfers.
